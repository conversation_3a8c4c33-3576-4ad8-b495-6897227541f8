import { queryOptions, useQuery } from "@tanstack/react-query";
import { PaginationFilter } from "@/types/app";
import {
  Leaderboard,
  LeaderboardType,
  MyLeaderboard,
} from "@/types/leaderboard";
import { API_URL, req } from "@/utils/api";
import { qs } from "@/utils/qs";

interface FetchLeaderboardParams extends PaginationFilter {
  type?: LeaderboardType;
}

export const fetchLeaderboard = async (params?: FetchLeaderboardParams) => {
  return req<Leaderboard[]>(
    `${API_URL}/api/v1/leaderboard${qs({ ...params })}`,
    {
      withAuth: true,
    },
  ).then((data) => {
    return data;
  });
};

export const leaderboardQueryOptions = (params?: FetchLeaderboardParams) => {
  return queryOptions({
    queryKey: ["leaderboard"],
    queryFn: () => fetchLeaderboard(params),
    enabled: !!params?.type,
    staleTime: 5 * 1000,
  });
};

export function useLeaderboard(params?: FetchLeaderboardParams) {
  return useQuery(leaderboardQueryOptions(params));
}

export const fetchMyLeaderboard = async () => {
  return req<MyLeaderboard>(`${API_URL}/api/v1/leaderboard/me`, {
    withAuth: true,
  }).then((data) => {
    return data;
  });
};

export const myLeaderboardQueryOptions = () => {
  return queryOptions({
    queryKey: ["my-leaderboard"],
    queryFn: () => fetchMyLeaderboard(),
  });
};

export function useMyLeaderboard() {
  return useQuery(myLeaderboardQueryOptions());
}
