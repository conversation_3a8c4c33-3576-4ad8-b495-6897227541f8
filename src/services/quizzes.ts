import { queryOptions, skipToken, useQuery } from "@tanstack/react-query";
import { Quiz, QuizAttempt } from "@/types/quizzes";
import { API_URL, req } from "@/utils/api";

export const fetchQuizzes = (sectionID: string): Promise<Quiz[]> => {
  return req(`${API_URL}/api/v1/sections/${sectionID}/quizzes`, {
    withAuth: true,
  });
};

export const fetchQuizAttempts = (
  sectionID: string,
): Promise<QuizAttempt[]> => {
  return req<QuizAttempt[]>(
    `${API_URL}/api/v1/sections/${sectionID}/quizzes/attempts`,
    {
      withAuth: true,
    },
  );
};

export const checkAnswer = (
  quizId: string,
  answer: number[],
): Promise<QuizAttempt> => {
  return req(`${API_URL}/api/v1/quizzes/${quizId}`, {
    body: JSON.stringify({
      user_answer: answer,
    }),
    method: "POST",
    withAuth: true,
  });
};

export function useQuizAttempts(sectionID: string | undefined) {
  return useQuery(
    queryOptions({
      queryKey: ["quiz-attempts", sectionID],
      queryFn: !sectionID
        ? skipToken
        : () => {
            return fetchQuizAttempts(sectionID);
          },
    }),
  );
}

export function useQuizzes(sectionID: string | undefined) {
  return useQuery(
    queryOptions({
      queryKey: ["quizzes", sectionID],
      queryFn: !sectionID
        ? skipToken
        : () => {
            return fetchQuizzes(sectionID);
          },
    }),
  );
}
