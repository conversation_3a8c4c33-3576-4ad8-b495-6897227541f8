import { queryOptions, useQuery } from "@tanstack/react-query";
import { Category } from "@/types/app";
import { API_URL, req } from "@/utils/api";

export const fetchCategories = async () => {
  return req<Category[]>(`${API_URL}/api/v1/categories`).then((data) => {
    return data;
  });
};

export const categoriesQueryOptions = () => {
  return queryOptions({
    queryKey: ["categories"],
    queryFn: () => fetchCategories(),
    staleTime: 60 * 1000,
  });
};

export function useCategories() {
  return useQuery(categoriesQueryOptions());
}
