import { queryOptions, useQuery } from "@tanstack/react-query";
import {
  GroupedLessons,
  Lesson,
  LessonGroup,
  Section,
  SectionType,
} from "@/types/lessons";
import { API_URL, AppError, req } from "@/utils/api";
import { qs } from "@/utils/qs";

/*
 * Start lessons service
 */

export const fetchLessons = async (courseSlug: string) => {
  const queryString = qs({
    page: 1,
    limit: 20,
  });
  return req<Lesson[]>(
    `${API_URL}/api/v1/courses/${courseSlug}/lessons${queryString}`,
  ).then((data) => {
    return data;
  });
};

export const lessonsQueryOptions = (courseSlug: string) => {
  return queryOptions({
    queryKey: ["lessons", courseSlug],
    queryFn: () => {
      return fetchLessons(courseSlug);
    },
  });
};

export const useLessons = (courseSlug: string) => {
  return useQuery(lessonsQueryOptions(courseSlug));
};

/*
 * Start lesson service
 */

export const fetchLesson = async (lessonSlug: string) => {
  return req<Lesson>(`${API_URL}/api/v1/lessons/${lessonSlug}`).then((data) => {
    return data;
  });
};

export const lessonQueryOptions = (lessonSlug: string) => {
  return queryOptions({
    queryKey: ["lesson", lessonSlug],
    queryFn: () => {
      return fetchLesson(lessonSlug);
    },
  });
};

/*
 * Start section service
 */

export const fetchSections = async (lessonId: string) => {
  if (!lessonId) return [];
  return req<Section[]>(`${API_URL}/api/v1/lessons/${lessonId}/sections`, {
    withAuth: true,
  })
    .then((data) => {
      return data.map((section) => {
        if (
          section.type === SectionType.TEXT ||
          section.type === SectionType.GAME
        ) {
          try {
            section.contentParsed = JSON.parse(section.content);

            // Handle dynamic section type based on content
            if (section.contentParsed?.type === "quizzes") {
              section = {
                ...section,
                type: SectionType.CHALLENGE,
              };
            }
          } catch (err) {}
        }

        return section;
      });
    })
    .catch(async (error) => {
      if (
        error instanceof AppError &&
        error.code === "Validation" &&
        error.cause === "not started lesson"
      ) {
        await startLesson(lessonId);
        await fetchSections(lessonId);
      }
      throw error;
    });
};

export const sectionsQueryOptions = (lessonId: string) => {
  return queryOptions({
    queryKey: ["sections", lessonId],
    queryFn: () => {
      return fetchSections(lessonId);
    },
    retry: true,
  });
};

/*
 * Start user lesson progress service
 */

export interface UpdateUserProgressParams {
  section_id?: string;
  lesson_id: string;
}

export function updateUserProgress({
  section_id,
  lesson_id,
}: UpdateUserProgressParams) {
  return req(`${API_URL}/api/v1/lessons/${lesson_id}/progress`, {
    body: JSON.stringify({
      lesson_id,
      section_id,
    }),
    method: "POST",
    withAuth: true,
  });
}

export const startLesson = async (lesson_id: string) => {
  return req(`${API_URL}/api/v1/lessons/${lesson_id}/start`, {
    method: "POST",
    withAuth: true,
  });
};

export const completeLesson = async (lesson_id: string) => {
  return req(`${API_URL}/api/v1/lessons/${lesson_id}/complete`, {
    method: "POST",
    withAuth: true,
  });
};

export function groupAndSortLessons(lessons: Lesson[]): GroupedLessons {
  const grouped: { [key: string]: Lesson[] } = {};

  lessons.forEach((lesson) => {
    const groupKey = lesson.group || `ungrouped_${lesson.id}`;
    if (!grouped[groupKey]) {
      grouped[groupKey] = [];
    }
    grouped[groupKey].push(lesson);
  });

  const result: GroupedLessons = {};

  Object.keys(grouped).forEach((groupKey) => {
    const sortedGroupLessons = grouped[groupKey].sort(
      (a, b) => a.ordinal_index - b.ordinal_index,
    );
    const groupName = groupKey || sortedGroupLessons[0]?.name;
    result[groupName] = sortedGroupLessons;
  });

  return result;
}

export function groupAndSortLessonsAsArray(lessons: Lesson[]): LessonGroup[] {
  const grouped = groupAndSortLessons(lessons);

  if (lessons.every((lesson) => !lesson.group)) {
    return [
      {
        groupName: "Ungrouped",
        lessons,
        isUngrouped: true,
      },
    ];
  }

  return Object.entries(grouped).map(([groupName, lessons]) => ({
    groupName,
    lessons,
    isUngrouped: lessons[0]?.group == null,
  }));
}
