import { type UseQ<PERSON><PERSON><PERSON><PERSON>ult, useQuery } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { RESET } from "jotai/utils";
import { toast } from "sonner";
import { atomAuth, atomAuthRef, authStore } from "@/store/auth";
import type { AuthOK, User, UserOther, UserUpdatePayload } from "@/types/auth";
import { API_URL, AppError, req } from "@/utils/api";
import { qs } from "@/utils/qs";

export function renderGoogleSignIn(target: HTMLElement | null) {
  if (!target || !window.google?.accounts?.id) {
    return;
  }

  window.google.accounts.id.renderButton(target, {
    type: "standard",
    theme: "outline",
    size: "large",
    text: "signin_with",
    shape: "pill",
    width: target.clientWidth,
  });
}

export function loginWithGoogle({
  credential,
  ref,
}: {
  credential: string;
  ref?: string;
}) {
  return req<AuthOK>(`${API_URL}/auth/social/google`, {
    method: "POST",
    body: JSON.stringify({
      id_token: credential,
      ref,
    }),
  })
    .then((data) => {
      authStore.set(atomAuthRef, RESET);
      authStore.set(atomAuth, data);

      window.location.href = "/dashboard";
      return data;
    })
    .catch((err) => {
      console.log("err", { err });
      toast.error(err.cause);
    });
}

export function loginWithEmailChallenge({ email }: { email: string }) {
  return req<string>(`${API_URL}/auth/email/login`, {
    method: "POST",
    body: JSON.stringify({
      email,
    }),
  });
}

export function loginWithEmail({
  email,
  otp,
  ref,
}: {
  email: string;
  otp: string;
  ref?: string;
}) {
  return req<AuthOK>(`${API_URL}/auth/email/verify`, {
    method: "POST",
    body: JSON.stringify({
      email,
      code: otp,
      ref,
    }),
  }).then((data) => {
    authStore.set(atomAuth, data);
    return data;
  });
}

export function fetchProfile(params: {
  jwt: string;
  id: string;
  stats?: boolean;
}): Promise<UserOther>;
export function fetchProfile(params: {
  jwt: string;
  stats?: boolean;
}): Promise<User>;
export function fetchProfile(params: {
  jwt: string;
  id?: string;
  stats?: boolean;
}): Promise<User | UserOther> {
  const queryParams = {};
  if (params.stats) {
    Object.assign(queryParams, { stats: true });
  }
  const queryString = qs(queryParams);
  return req<typeof params extends { id: string } ? UserOther : User>(
    `${API_URL}/api/v1/profile${params.id ? `/${params.id}` : ""}${queryString}`,
    {
      headers: {
        authorization: `Bearer ${params.jwt}`,
      },
    },
  );
}

export function useProfile(): UseQueryResult<User, Error>;
export function useProfile(params: {
  stats?: boolean;
}): UseQueryResult<User, Error>;
export function useProfile(params: {
  id: string;
  stats?: boolean;
}): UseQueryResult<UserOther, Error>;
export function useProfile(params?: { id?: string; stats?: boolean }) {
  const auth = useAtomValue(atomAuth);

  return useQuery({
    queryKey: ["profile", params?.id, auth?.claims.sub, params?.stats],
    queryFn: async () => {
      if (!auth?.jwt) {
        throw new AppError("Authn", "No authentication token");
      }
      return params?.id
        ? fetchProfile({
            jwt: auth.jwt,
            id: params?.id || "",
            stats: params.stats,
          })
        : fetchProfile({
            jwt: auth.jwt,
            stats: params?.stats,
          });
    },
    retry(failureCount, error) {
      if (error instanceof AppError && error.code === "Authn") {
        return false;
      }

      if (failureCount < 2) {
        return true;
      }

      return false;
    },
  });
}

export const updateProfile = async (
  payload: UserUpdatePayload,
): Promise<User> => {
  return req(`${API_URL}/api/v1/profile`, {
    body: JSON.stringify(payload),
    method: "PATCH",
    withAuth: true,
  });
};
