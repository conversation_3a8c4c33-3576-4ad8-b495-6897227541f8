import { IMAGE_URL, req, UPLOAD_URL } from "@/utils/api";

type UploadFile = {
  file_name: string;
  square?: string;
  thumbnail?: string;
};

export function uploadFile(params: { file: File }): Promise<UploadFile> {
  const formData = new FormData();
  formData.append("file", params.file);
  return req<UploadFile>(`${UPLOAD_URL}/api/v1/files`, {
    method: "POST",
    withAuth: true,
    body: formData,
  }).then((r) => {
    r.file_name = `${IMAGE_URL}/${r.file_name}`;
    r.square = r.square && `${IMAGE_URL}/${r.square}`;
    r.thumbnail = r.thumbnail && `${IMAGE_URL}/${r.thumbnail}`;
    return r;
  });
}
