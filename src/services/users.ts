import { queryOptions, useQuery } from "@tanstack/react-query";
import { UserFriend } from "@/types/auth";
import { API_URL, req } from "@/utils/api";

export const fetchMyFriends = (): Promise<UserFriend[]> => {
  return req(`${API_URL}/api/v1/me/friends`, {
    withAuth: true,
  });
};

export function useMyFriends() {
  return useQuery(
    queryOptions({
      queryKey: ["friends"],
      queryFn: () => fetchMyFriends(),
    }),
  );
}

export const checkin = () => {
  return req(`${API_URL}/api/v1/checkin`, {
    method: "POST",
    withAuth: true,
  });
};
