import {
  InfiniteData,
  infiniteQueryOptions,
  queryOptions,
  skipToken,
  UseInfiniteQueryResult,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { create, keyResolver, windowScheduler } from "@yornaath/batshit";
import { UUID } from "@/types/app";
import {
  ChatbotAction,
  ChatFAQ,
  ChatLanguage,
  ChatMessage,
  ChatPreferences,
  ChatProfile,
  Conversation,
  FeedbackType,
  MessageFeedback,
} from "@/types/chat";
import { QuizAnswer } from "@/types/quizzes";
import { API_URL, LLM_URL, req } from "@/utils/api";
import { qs } from "@/utils/qs";

type ChatHints = {
  section_id?: UUID;
  quiz?: {
    id: UUID | undefined;
    user_answer: QuizAnswer | undefined;
  };
  game?: GameHints;
};

type GameHints = {
  user_answer?: {
    label: string;
    items: string[];
  }[];
};

type SendMessageInputBase = {
  input: string;
  quote?: string;
  hints?: ChatHints;
};

export type SendMessageChat = SendMessageInputBase & {
  type: ChatbotAction.CHAT;
};

export type SendMessageFAQ = SendMessageInputBase & {
  type: ChatbotAction.CHAT;
  hints: ChatHints & {
    faq_id: UUID;
  };
};

export type SendMessageTranslate = SendMessageInputBase & {
  type: ChatbotAction.TRANSLATE;
  quote: string;
  hints: ChatHints & {
    language: "en" | "vi";
  };
};

export type SendMessageQuotable = SendMessageInputBase & {
  type: ChatbotAction.EXAMPLES | ChatbotAction.SUMMARY | ChatbotAction.EXPLAIN;
  quote: string;
};

export type SendMessageInput =
  | SendMessageChat
  | SendMessageFAQ
  | SendMessageQuotable
  | SendMessageTranslate;

export type SendMessageResponse = {
  input: ChatMessage;
  output: ChatMessage;
  preferences?: Partial<ChatPreferences>;
};

export const fetchLessonConversations = ({ lessonID }: { lessonID: UUID }) => {
  return req<Conversation[]>(
    `${LLM_URL}/api/v1/conversations?lesson=${lessonID}&recent_messages_limit=2`,
    {
      withAuth: true,
    },
  )
    .then((data) => {
      if (data.length) {
        return data;
      }

      return req<Conversation>(`${LLM_URL}/api/v1/conversations`, {
        method: "POST",
        withAuth: true,
        body: JSON.stringify({
          lesson_id: lessonID,
        }),
      })
        .then((conversation) => {
          return [conversation];
        })
        .catch(() => {
          return [];
        });
    })
    .then((conversations) => {
      return conversations.map((conversation) => {
        (conversation.recent_messages || []).reverse();
        return conversation;
      });
    });
};

export const lessonConversationsQueryOptions = ({
  lessonID,
}: {
  lessonID: UUID | undefined;
}) => {
  return queryOptions({
    queryKey: ["conversations", lessonID],
    queryFn: !lessonID
      ? skipToken
      : () => fetchLessonConversations({ lessonID }),
    staleTime: 60 * 60 * 1000,
  });
};

const fetchMessages = ({
  conversationID,
  limit = 10,
  beforeID,
}: {
  conversationID: UUID;
  limit?: number;
  beforeID?: UUID;
}) => {
  return req<ChatMessage[]>(
    `${LLM_URL}/api/v1/conversations/${conversationID}/messages${qs({
      before: beforeID,
      limit,
    })}`,
    {
      withAuth: true,
    },
  ).then((msgs) => {
    return msgs.sort(
      (a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
    );
  });
};

export const messagesQueryOptions = ({
  conversationID,
  limit,
}: {
  conversationID: UUID | undefined;
  limit?: number;
}) => {
  return infiniteQueryOptions<
    ChatMessage[],
    Error,
    InfiniteData<ChatMessage[], UUID | undefined>,
    (string | undefined)[],
    UUID | undefined
  >({
    queryKey: ["conversations", "messages", conversationID],
    queryFn: !conversationID
      ? skipToken
      : ({ pageParam }: { pageParam: UUID | undefined }) => {
          return fetchMessages({
            conversationID,
            beforeID: pageParam,
            limit,
          });
        },
    initialPageParam: undefined,
    getNextPageParam(lastPage) {
      return lastPage?.[0]?.id;
    },
    staleTime: 5 * 1000,
  });
};

export const fetchSuggestionsBySection = ({
  sectionID,
  language,
  limit = 5,
}: {
  sectionID: UUID;
  language: keyof typeof ChatLanguage | undefined;
  limit?: number;
}) => {
  return req<ChatFAQ[]>(
    `${LLM_URL}/api/v1/faqs${qs({
      section: sectionID,
      language,
      limit,
    })}`,
    {
      withAuth: true,
    },
  ).then((data) => {
    // TODO: remove this after API change
    return data.splice(0, limit);
  });
};

export const faqQueryOptions = ({
  sectionID,
  language,
  limit,
}: {
  sectionID: UUID | undefined;
  language: keyof typeof ChatLanguage | undefined;
  limit?: number;
}) => {
  return queryOptions({
    queryKey: ["faqs", sectionID, language],
    queryFn: !sectionID
      ? skipToken
      : () => {
          return fetchSuggestionsBySection({
            sectionID,
            limit,
            language,
          });
        },
    staleTime: 5 * 1000,
  });
};

export type ChatMessageQuery = UseInfiniteQueryResult<
  InfiniteData<ChatMessage[], UUID | undefined>,
  Error
>;

export function useChatMessages({
  conversation,
  limit,
}: {
  conversation: Conversation | undefined;
  limit?: number;
}) {
  const conversationID = conversation?.id;
  const qo = messagesQueryOptions({
    conversationID,
    limit,
  });

  const messages = useInfiniteQuery(qo);

  return {
    messages,
    queryOptions: qo,
    mutationKey: ["conversation:chat", conversationID],
  };
}

export type SendFeedbackParams = MessageFeedback;

export const sendMessageFeedback = (
  payload: SendFeedbackParams,
): Promise<MessageFeedback> => {
  return req(`${API_URL}/api/v1/feedback`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(payload),
    headers: {
      "content-type": "application/json",
    },
  });
};

const FEEDBACK_BATCH_WINDOW_MS = 10;

const batcherMessageFeedback = create({
  fetcher: async (messageIds: string[]) => {
    const params = messageIds
      .map((messageId) => `message_id=${messageId}`)
      .join("&");
    return req<MessageFeedback[]>(`${API_URL}/api/v1/feedback?${params}`, {
      method: "GET",
      withAuth: true,
    });
  },
  resolver: keyResolver("message_id"),
  scheduler: windowScheduler(FEEDBACK_BATCH_WINDOW_MS),
});

export const useMessageFeedback = ({
  messageId,
  lessonId,
}: {
  messageId: string;
  lessonId: string;
}) => {
  const queryClient = useQueryClient();

  const feedbackQuery = useQuery({
    queryKey: ["feedback", messageId],
    queryFn: () => batcherMessageFeedback.fetch(messageId),
  });

  const { mutate: mutateFeedback } = useMutation({
    mutationKey: ["feedback", messageId],
    mutationFn: ({
      feedback,
      comment,
    }: {
      feedback: FeedbackType;
      comment?: string;
    }) => {
      return sendMessageFeedback({
        message_id: messageId,
        feedback,
        lesson_id: lessonId,
        comment: comment || "",
      });
    },
    onMutate: async ({ feedback }: { feedback: FeedbackType }) => {
      const queryKey = ["feedback", messageId];
      await queryClient.cancelQueries({ queryKey });
      const previousData = queryClient.getQueryData(queryKey);

      queryClient.setQueryData(queryKey, (old: MessageFeedback | undefined) => {
        if (old)
          return {
            ...old,
            feedback,
          };
        return {
          message_id: messageId,
          feedback,
          lesson_id: lessonId,
          comment: "",
        };
      });
      return { previousData };
    },
    onError: (_, __, context) => {
      if (context?.previousData) {
        const queryKey = ["feedback", messageId];
        queryClient.setQueryData(queryKey, context.previousData);
      }
    },
    onSuccess: () => {
      const queryKey = ["feedback", messageId];
      queryClient.invalidateQueries({
        queryKey,
      });
    },
    onSettled: () => {
      const queryKey = ["feedback", messageId];
      queryClient.invalidateQueries({
        queryKey,
      });
    },
  });

  return {
    feedbackQuery,
    mutateFeedback,
  };
};

const fetchChatProfile = () => {
  return req<ChatProfile>(`${LLM_URL}/api/v1/users/me`, {
    withAuth: true,
  });
};

export const chatProfileQueryOptions = () => {
  return queryOptions({
    queryKey: ["chat:profile"],
    queryFn: fetchChatProfile,
  });
};

export const updateChatPreferences = (v: Partial<ChatPreferences>) => {
  return req<ChatProfile>(`${LLM_URL}/api/v1/users/preferences`, {
    method: "POST",
    body: JSON.stringify(v),
    withAuth: true,
  });
};
