import { queryOptions, useQuery } from "@tanstack/react-query";
import { PaginationFilter } from "@/types/app";
import { UserCourse } from "@/types/courses";
import { UserVoting } from "@/types/rating";
import { API_URL, req } from "@/utils/api";
import { qs } from "@/utils/qs";

interface FetchLearningCoursesParams extends PaginationFilter {
  completed?: boolean;
}

export const fetchLearningCourses = async ({
  completed,
  ...paginationParams
}: FetchLearningCoursesParams) => {
  const queryParams = {
    limit: paginationParams.limit || 20,
    ...(completed ? { completed } : {}),
  };
  const queryString = qs(queryParams);
  return req<UserCourse[]>(
    `${API_URL}/api/v1/courses/my-learning${queryString}`,
    {
      withAuth: true,
    },
  ).then((data) => {
    return data;
  });
};

export const learningCoursesQueryOptions = (
  params: FetchLearningCoursesParams,
) => {
  return queryOptions({
    queryKey: ["learning-courses", params],
    queryFn: () => fetchLearningCourses(params),
    staleTime: 5 * 1000,
  });
};

export function useLearningCourses(params: FetchLearningCoursesParams) {
  return useQuery(learningCoursesQueryOptions(params));
}

export function fetchMyVoting(withCourses: boolean = false) {
  const queryParams = {
    load_courses: withCourses,
  };
  const queryString = qs(queryParams);
  return req<UserVoting[]>(`${API_URL}/api/v1/me/voting${queryString}`, {
    withAuth: true,
  });
}

export const myVotingQueryOptions = (
  enabled: boolean = true,
  withCourses?: boolean,
) => {
  return queryOptions({
    queryKey: ["my-voting"],
    queryFn: () => fetchMyVoting(withCourses),
    enabled,
  });
};

export function useMyVoting({
  enabled = true,
  withCourses,
}: {
  enabled?: boolean;
  withCourses?: boolean;
} = {}) {
  return useQuery(myVotingQueryOptions(enabled, withCourses));
}
