import { UUID } from "crypto";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { Attempt, STATUSES } from "@/components/games/quiz/use-quiz";
import { SendMessageInput } from "@/services/conversations";
import { GameSectionCategory, Section } from "@/types/lessons";
import { QuizAnswer } from "@/types/quizzes";

export const atomCurrentChat = atom<SendMessageInput | null>(null);
export const atomCurrentSection = atom<Section | null>(null);

export const atomCurrentGameUserAnswer = atomWithReset<
  GameSectionCategory[] | undefined
>(undefined);

export const atomCurrentQuizId = atom<UUID>();
export const atomMappingQuizIdToUserAnswer = atom<Record<UUID, Attempt>>({});
