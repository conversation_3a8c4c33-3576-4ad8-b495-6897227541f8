import { StrictMode, startTransition } from "react";
import { hydrateRoot } from "react-dom/client";
import { loginWithGoogle } from "@/services/auth";
import { StartClientPromise } from "./components/StartClientPromise";
import { createRouter } from "./router";
import { atomAuthRef, authStore } from "./store/auth";

const router = createRouter(window.location.pathname);

import {
  getLocale,
  overwriteGetLocale,
  strategy,
} from "./paraglide/runtime.js";

if (strategy.includes("cookie")) {
  const inMemoryLocale = getLocale();
  overwriteGetLocale(() => inMemoryLocale);
}
startTransition(() => {
  hydrateRoot(
    document,
    <StrictMode>
      <StartClientPromise
        store={authStore}
        router={router}
        onResolved={() => {
          const clientID = import.meta.env.VITE_GOOGLE_CLIENT_ID || "";
          console.debug("GSI client", clientID);
          if (!clientID || !window.google?.accounts) {
            return;
          }

          window.google.accounts.id.initialize({
            client_id: clientID,
            callback: (resp) => {
              const ref = authStore.get(atomAuthRef);
              loginWithGoogle({
                credential: resp.credential,
                ref,
              });
            },
          });
        }}
      />
    </StrictMode>,
  );
});
