import { userFormSchema } from "@/components/user/useProfileForm";
import { UUID } from "@/types/app";

export type AuthOK = {
  claims: {
    sub: UUID;
    exp: number;
    nbf: number;
    iat: number;
    jti: string;
  };
  jwt: string;
};

export type User = {
  id: UUID;
  name: string;
  email: string;
  email_verified_at: string | null | undefined;
  created_at: string;
  updated_at: string;
  is_instructor: boolean;
  username?: string;
  bio?: string;
  avatar_url?: string;
  points: number;
  ref_code: string;
  ref_count: number;
  badges: object;
  unseen_badges: string[];
  keys: number;
  stats?: UserStats;
  learned_at?: string;
  current_streak: number;
  last_streak_claimed_at?: string;
};

type UserStats = {
  completed_courses: number;
};

export type UserOther = Pick<
  User,
  "id" | "name" | "is_instructor" | "bio" | "avatar_url" | "username"
>;

export type UserFriend = Pick<
  User,
  | "id"
  | "name"
  | "is_instructor"
  | "bio"
  | "avatar_url"
  | "username"
  | "email"
  | "learned_at"
>;

export type UserUpdatePayload = Partial<typeof userFormSchema.infer>;
