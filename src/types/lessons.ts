import { TipTapNode } from "@/components/tip-tap-render";
import { UUID } from "./app";

export interface Lesson {
  id: UUID;
  course_id: UUID;
  name: string;
  slug: string;
  description: string;
  requirements?: LessonRequirements;
  group?: string | null;
  ordinal_index: number;
  duration: number;
  points: number;
  archived_at?: string | null;
  published_at?: string | null;
  sections?: Section[];
  // custom fields
  status?: LessonStatus;
}

export interface LessonRequirements {
  keys?: number;
}

export enum LessonStatus {
  CURRENT = "CURRENT",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  LOCKED = "LOCKED",
}

export enum SectionType {
  TEXT = "TEXT",
  GAME = "GAME",
  CHALLENGE = "CHALLENGE",
  AIGENERATED = "AIGENERATED",
}

type BaseSectionFields = {
  id: UUID;
  lesson_id: UUID;
  ordinal_index: number;
  content: string;
};

export type SectionText = BaseSectionFields & {
  type: SectionType.TEXT;
  contentParsed?: TipTapNode;
};

export type SectionGame = BaseSectionFields & {
  type: SectionType.GAME;
  contentParsed?: GameConfig;
};

export type SectionQuiz = BaseSectionFields & {
  type: SectionType.CHALLENGE;
};

export type SectionAIGenerated = BaseSectionFields & {
  type: SectionType.AIGENERATED;
};

export type Section =
  | SectionText
  | SectionGame
  | SectionQuiz
  | SectionAIGenerated;

export enum GameType {
  CATEGORIZING = "categorizing",
  MATCHING = "matching",
}

export interface GameConfig {
  type: string;
  game: GameType;
  description: string;
  parameters: {
    categories: GameSectionCategory[];
    explanation: string;
  };
}

export interface GameSectionCategory {
  label: string;
  items: string[];
}

export interface GroupedLessons {
  [groupName: string]: Lesson[];
}

export interface LessonGroup {
  groupName: string;
  lessons: Lesson[];
  isUngrouped: boolean;
}
