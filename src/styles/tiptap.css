.sentence {
  transition: all 0.2s ease;
}

.selectable-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1rem;

  th,
  td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }

  th {
    background-color: #f2f2f2;
  }
}

.exam-tool-container {
  position: absolute;
}

.sentence-hover-active {
  color: #000 !important;
  background-color: var(--text-selected) !important;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.selectable-content {
  white-space: pre-wrap;
}

.tiptap-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
