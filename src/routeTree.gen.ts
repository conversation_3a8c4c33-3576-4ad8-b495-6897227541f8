/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

import { Route as rootRouteImport } from './routes/__root'
import { Route as TermsOfServiceRouteImport } from './routes/terms-of-service'
import { Route as PrivacyRouteImport } from './routes/privacy'
import { Route as _layoutRouteImport } from './routes/__layout'
import { Route as IndexRouteImport } from './routes/index'
import { Route as _layoutDashboardRouteImport } from './routes/__layout/dashboard'
import { Route as _layoutProfileRouteRouteImport } from './routes/__layout/profile/route'
import { Route as _layoutMyLearningRouteRouteImport } from './routes/__layout/my-learning/route'
import { Route as _layoutCoursesRouteRouteImport } from './routes/__layout/courses/route'
import { Route as _layoutMyLearningIndexRouteImport } from './routes/__layout/my-learning/index'
import { Route as _layoutHelpCenterIndexRouteImport } from './routes/__layout/help-center/index'
import { Route as _layoutCoursesIndexRouteImport } from './routes/__layout/courses/index'
import { Route as _layoutProfileSettingRouteImport } from './routes/__layout/profile/setting'
import { Route as _layoutProfileMeRouteImport } from './routes/__layout/profile/me'
import { Route as _layoutHelpCenterHelpCenterRouteImport } from './routes/__layout/help-center/$helpCenter'
import { Route as _layoutCoursesCourseSlugIndexRouteImport } from './routes/__layout/courses/$courseSlug/index'
import { Route as LearnCourseSlugLessonSlug_layoutLearningRouteImport } from './routes/learn/$courseSlug/$lessonSlug/__layout-learning'
import { Route as LearnCourseSlugLessonSlug_layoutLearningIndexRouteImport } from './routes/learn/$courseSlug/$lessonSlug/__layout-learning/index'

const LearnCourseSlugLessonSlugRouteImport = createFileRoute(
  '/learn/$courseSlug/$lessonSlug',
)()

const TermsOfServiceRoute = TermsOfServiceRouteImport.update({
  id: '/terms-of-service',
  path: '/terms-of-service',
  getParentRoute: () => rootRouteImport,
} as any)
const PrivacyRoute = PrivacyRouteImport.update({
  id: '/privacy',
  path: '/privacy',
  getParentRoute: () => rootRouteImport,
} as any)
const _layoutRoute = _layoutRouteImport.update({
  id: '/__layout',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const _layoutDashboardRoute = _layoutDashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => _layoutRoute,
} as any)
const _layoutProfileRouteRoute = _layoutProfileRouteRouteImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => _layoutRoute,
} as any)
const _layoutMyLearningRouteRoute = _layoutMyLearningRouteRouteImport.update({
  id: '/my-learning',
  path: '/my-learning',
  getParentRoute: () => _layoutRoute,
} as any)
const _layoutCoursesRouteRoute = _layoutCoursesRouteRouteImport.update({
  id: '/courses',
  path: '/courses',
  getParentRoute: () => _layoutRoute,
} as any)
const LearnCourseSlugLessonSlugRoute =
  LearnCourseSlugLessonSlugRouteImport.update({
    id: '/learn/$courseSlug/$lessonSlug',
    path: '/learn/$courseSlug/$lessonSlug',
    getParentRoute: () => rootRouteImport,
  } as any)
const _layoutMyLearningIndexRoute = _layoutMyLearningIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => _layoutMyLearningRouteRoute,
} as any)
const _layoutHelpCenterIndexRoute = _layoutHelpCenterIndexRouteImport.update({
  id: '/help-center/',
  path: '/help-center/',
  getParentRoute: () => _layoutRoute,
} as any)
const _layoutCoursesIndexRoute = _layoutCoursesIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => _layoutCoursesRouteRoute,
} as any)
const _layoutProfileSettingRoute = _layoutProfileSettingRouteImport.update({
  id: '/setting',
  path: '/setting',
  getParentRoute: () => _layoutProfileRouteRoute,
} as any)
const _layoutProfileMeRoute = _layoutProfileMeRouteImport.update({
  id: '/me',
  path: '/me',
  getParentRoute: () => _layoutProfileRouteRoute,
} as any)
const _layoutHelpCenterHelpCenterRoute =
  _layoutHelpCenterHelpCenterRouteImport.update({
    id: '/help-center/$helpCenter',
    path: '/help-center/$helpCenter',
    getParentRoute: () => _layoutRoute,
  } as any)
const _layoutCoursesCourseSlugIndexRoute =
  _layoutCoursesCourseSlugIndexRouteImport.update({
    id: '/$courseSlug/',
    path: '/$courseSlug/',
    getParentRoute: () => _layoutCoursesRouteRoute,
  } as any)
const LearnCourseSlugLessonSlug_layoutLearningRoute =
  LearnCourseSlugLessonSlug_layoutLearningRouteImport.update({
    id: '/__layout-learning',
    getParentRoute: () => LearnCourseSlugLessonSlugRoute,
  } as any)
const LearnCourseSlugLessonSlug_layoutLearningIndexRoute =
  LearnCourseSlugLessonSlug_layoutLearningIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => LearnCourseSlugLessonSlug_layoutLearningRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/privacy': typeof PrivacyRoute
  '/terms-of-service': typeof TermsOfServiceRoute
  '/courses': typeof _layoutCoursesRouteRouteWithChildren
  '/my-learning': typeof _layoutMyLearningRouteRouteWithChildren
  '/profile': typeof _layoutProfileRouteRouteWithChildren
  '/dashboard': typeof _layoutDashboardRoute
  '/help-center/$helpCenter': typeof _layoutHelpCenterHelpCenterRoute
  '/profile/me': typeof _layoutProfileMeRoute
  '/profile/setting': typeof _layoutProfileSettingRoute
  '/courses/': typeof _layoutCoursesIndexRoute
  '/help-center': typeof _layoutHelpCenterIndexRoute
  '/my-learning/': typeof _layoutMyLearningIndexRoute
  '/learn/$courseSlug/$lessonSlug': typeof LearnCourseSlugLessonSlug_layoutLearningRouteWithChildren
  '/courses/$courseSlug': typeof _layoutCoursesCourseSlugIndexRoute
  '/learn/$courseSlug/$lessonSlug/': typeof LearnCourseSlugLessonSlug_layoutLearningIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/privacy': typeof PrivacyRoute
  '/terms-of-service': typeof TermsOfServiceRoute
  '/profile': typeof _layoutProfileRouteRouteWithChildren
  '/dashboard': typeof _layoutDashboardRoute
  '/help-center/$helpCenter': typeof _layoutHelpCenterHelpCenterRoute
  '/profile/me': typeof _layoutProfileMeRoute
  '/profile/setting': typeof _layoutProfileSettingRoute
  '/courses': typeof _layoutCoursesIndexRoute
  '/help-center': typeof _layoutHelpCenterIndexRoute
  '/my-learning': typeof _layoutMyLearningIndexRoute
  '/learn/$courseSlug/$lessonSlug': typeof LearnCourseSlugLessonSlug_layoutLearningIndexRoute
  '/courses/$courseSlug': typeof _layoutCoursesCourseSlugIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/__layout': typeof _layoutRouteWithChildren
  '/privacy': typeof PrivacyRoute
  '/terms-of-service': typeof TermsOfServiceRoute
  '/__layout/courses': typeof _layoutCoursesRouteRouteWithChildren
  '/__layout/my-learning': typeof _layoutMyLearningRouteRouteWithChildren
  '/__layout/profile': typeof _layoutProfileRouteRouteWithChildren
  '/__layout/dashboard': typeof _layoutDashboardRoute
  '/__layout/help-center/$helpCenter': typeof _layoutHelpCenterHelpCenterRoute
  '/__layout/profile/me': typeof _layoutProfileMeRoute
  '/__layout/profile/setting': typeof _layoutProfileSettingRoute
  '/__layout/courses/': typeof _layoutCoursesIndexRoute
  '/__layout/help-center/': typeof _layoutHelpCenterIndexRoute
  '/__layout/my-learning/': typeof _layoutMyLearningIndexRoute
  '/learn/$courseSlug/$lessonSlug': typeof LearnCourseSlugLessonSlugRouteWithChildren
  '/learn/$courseSlug/$lessonSlug/__layout-learning': typeof LearnCourseSlugLessonSlug_layoutLearningRouteWithChildren
  '/__layout/courses/$courseSlug/': typeof _layoutCoursesCourseSlugIndexRoute
  '/learn/$courseSlug/$lessonSlug/__layout-learning/': typeof LearnCourseSlugLessonSlug_layoutLearningIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/privacy'
    | '/terms-of-service'
    | '/courses'
    | '/my-learning'
    | '/profile'
    | '/dashboard'
    | '/help-center/$helpCenter'
    | '/profile/me'
    | '/profile/setting'
    | '/courses/'
    | '/help-center'
    | '/my-learning/'
    | '/learn/$courseSlug/$lessonSlug'
    | '/courses/$courseSlug'
    | '/learn/$courseSlug/$lessonSlug/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/privacy'
    | '/terms-of-service'
    | '/profile'
    | '/dashboard'
    | '/help-center/$helpCenter'
    | '/profile/me'
    | '/profile/setting'
    | '/courses'
    | '/help-center'
    | '/my-learning'
    | '/learn/$courseSlug/$lessonSlug'
    | '/courses/$courseSlug'
  id:
    | '__root__'
    | '/'
    | '/__layout'
    | '/privacy'
    | '/terms-of-service'
    | '/__layout/courses'
    | '/__layout/my-learning'
    | '/__layout/profile'
    | '/__layout/dashboard'
    | '/__layout/help-center/$helpCenter'
    | '/__layout/profile/me'
    | '/__layout/profile/setting'
    | '/__layout/courses/'
    | '/__layout/help-center/'
    | '/__layout/my-learning/'
    | '/learn/$courseSlug/$lessonSlug'
    | '/learn/$courseSlug/$lessonSlug/__layout-learning'
    | '/__layout/courses/$courseSlug/'
    | '/learn/$courseSlug/$lessonSlug/__layout-learning/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  _layoutRoute: typeof _layoutRouteWithChildren
  PrivacyRoute: typeof PrivacyRoute
  TermsOfServiceRoute: typeof TermsOfServiceRoute
  LearnCourseSlugLessonSlugRoute: typeof LearnCourseSlugLessonSlugRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/terms-of-service': {
      id: '/terms-of-service'
      path: '/terms-of-service'
      fullPath: '/terms-of-service'
      preLoaderRoute: typeof TermsOfServiceRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/privacy': {
      id: '/privacy'
      path: '/privacy'
      fullPath: '/privacy'
      preLoaderRoute: typeof PrivacyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/__layout': {
      id: '/__layout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof _layoutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/__layout/dashboard': {
      id: '/__layout/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof _layoutDashboardRouteImport
      parentRoute: typeof _layoutRoute
    }
    '/__layout/profile': {
      id: '/__layout/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof _layoutProfileRouteRouteImport
      parentRoute: typeof _layoutRoute
    }
    '/__layout/my-learning': {
      id: '/__layout/my-learning'
      path: '/my-learning'
      fullPath: '/my-learning'
      preLoaderRoute: typeof _layoutMyLearningRouteRouteImport
      parentRoute: typeof _layoutRoute
    }
    '/__layout/courses': {
      id: '/__layout/courses'
      path: '/courses'
      fullPath: '/courses'
      preLoaderRoute: typeof _layoutCoursesRouteRouteImport
      parentRoute: typeof _layoutRoute
    }
    '/learn/$courseSlug/$lessonSlug': {
      id: '/learn/$courseSlug/$lessonSlug'
      path: '/learn/$courseSlug/$lessonSlug'
      fullPath: '/learn/$courseSlug/$lessonSlug'
      preLoaderRoute: typeof LearnCourseSlugLessonSlugRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/__layout/my-learning/': {
      id: '/__layout/my-learning/'
      path: '/'
      fullPath: '/my-learning/'
      preLoaderRoute: typeof _layoutMyLearningIndexRouteImport
      parentRoute: typeof _layoutMyLearningRouteRoute
    }
    '/__layout/help-center/': {
      id: '/__layout/help-center/'
      path: '/help-center'
      fullPath: '/help-center'
      preLoaderRoute: typeof _layoutHelpCenterIndexRouteImport
      parentRoute: typeof _layoutRoute
    }
    '/__layout/courses/': {
      id: '/__layout/courses/'
      path: '/'
      fullPath: '/courses/'
      preLoaderRoute: typeof _layoutCoursesIndexRouteImport
      parentRoute: typeof _layoutCoursesRouteRoute
    }
    '/__layout/profile/setting': {
      id: '/__layout/profile/setting'
      path: '/setting'
      fullPath: '/profile/setting'
      preLoaderRoute: typeof _layoutProfileSettingRouteImport
      parentRoute: typeof _layoutProfileRouteRoute
    }
    '/__layout/profile/me': {
      id: '/__layout/profile/me'
      path: '/me'
      fullPath: '/profile/me'
      preLoaderRoute: typeof _layoutProfileMeRouteImport
      parentRoute: typeof _layoutProfileRouteRoute
    }
    '/__layout/help-center/$helpCenter': {
      id: '/__layout/help-center/$helpCenter'
      path: '/help-center/$helpCenter'
      fullPath: '/help-center/$helpCenter'
      preLoaderRoute: typeof _layoutHelpCenterHelpCenterRouteImport
      parentRoute: typeof _layoutRoute
    }
    '/__layout/courses/$courseSlug/': {
      id: '/__layout/courses/$courseSlug/'
      path: '/$courseSlug'
      fullPath: '/courses/$courseSlug'
      preLoaderRoute: typeof _layoutCoursesCourseSlugIndexRouteImport
      parentRoute: typeof _layoutCoursesRouteRoute
    }
    '/learn/$courseSlug/$lessonSlug/__layout-learning': {
      id: '/learn/$courseSlug/$lessonSlug/__layout-learning'
      path: '/learn/$courseSlug/$lessonSlug'
      fullPath: '/learn/$courseSlug/$lessonSlug'
      preLoaderRoute: typeof LearnCourseSlugLessonSlug_layoutLearningRouteImport
      parentRoute: typeof LearnCourseSlugLessonSlugRoute
    }
    '/learn/$courseSlug/$lessonSlug/__layout-learning/': {
      id: '/learn/$courseSlug/$lessonSlug/__layout-learning/'
      path: '/'
      fullPath: '/learn/$courseSlug/$lessonSlug/'
      preLoaderRoute: typeof LearnCourseSlugLessonSlug_layoutLearningIndexRouteImport
      parentRoute: typeof LearnCourseSlugLessonSlug_layoutLearningRoute
    }
  }
}

interface _layoutCoursesRouteRouteChildren {
  _layoutCoursesIndexRoute: typeof _layoutCoursesIndexRoute
  _layoutCoursesCourseSlugIndexRoute: typeof _layoutCoursesCourseSlugIndexRoute
}

const _layoutCoursesRouteRouteChildren: _layoutCoursesRouteRouteChildren = {
  _layoutCoursesIndexRoute: _layoutCoursesIndexRoute,
  _layoutCoursesCourseSlugIndexRoute: _layoutCoursesCourseSlugIndexRoute,
}

const _layoutCoursesRouteRouteWithChildren =
  _layoutCoursesRouteRoute._addFileChildren(_layoutCoursesRouteRouteChildren)

interface _layoutMyLearningRouteRouteChildren {
  _layoutMyLearningIndexRoute: typeof _layoutMyLearningIndexRoute
}

const _layoutMyLearningRouteRouteChildren: _layoutMyLearningRouteRouteChildren =
  {
    _layoutMyLearningIndexRoute: _layoutMyLearningIndexRoute,
  }

const _layoutMyLearningRouteRouteWithChildren =
  _layoutMyLearningRouteRoute._addFileChildren(
    _layoutMyLearningRouteRouteChildren,
  )

interface _layoutProfileRouteRouteChildren {
  _layoutProfileMeRoute: typeof _layoutProfileMeRoute
  _layoutProfileSettingRoute: typeof _layoutProfileSettingRoute
}

const _layoutProfileRouteRouteChildren: _layoutProfileRouteRouteChildren = {
  _layoutProfileMeRoute: _layoutProfileMeRoute,
  _layoutProfileSettingRoute: _layoutProfileSettingRoute,
}

const _layoutProfileRouteRouteWithChildren =
  _layoutProfileRouteRoute._addFileChildren(_layoutProfileRouteRouteChildren)

interface _layoutRouteChildren {
  _layoutCoursesRouteRoute: typeof _layoutCoursesRouteRouteWithChildren
  _layoutMyLearningRouteRoute: typeof _layoutMyLearningRouteRouteWithChildren
  _layoutProfileRouteRoute: typeof _layoutProfileRouteRouteWithChildren
  _layoutDashboardRoute: typeof _layoutDashboardRoute
  _layoutHelpCenterHelpCenterRoute: typeof _layoutHelpCenterHelpCenterRoute
  _layoutHelpCenterIndexRoute: typeof _layoutHelpCenterIndexRoute
}

const _layoutRouteChildren: _layoutRouteChildren = {
  _layoutCoursesRouteRoute: _layoutCoursesRouteRouteWithChildren,
  _layoutMyLearningRouteRoute: _layoutMyLearningRouteRouteWithChildren,
  _layoutProfileRouteRoute: _layoutProfileRouteRouteWithChildren,
  _layoutDashboardRoute: _layoutDashboardRoute,
  _layoutHelpCenterHelpCenterRoute: _layoutHelpCenterHelpCenterRoute,
  _layoutHelpCenterIndexRoute: _layoutHelpCenterIndexRoute,
}

const _layoutRouteWithChildren =
  _layoutRoute._addFileChildren(_layoutRouteChildren)

interface LearnCourseSlugLessonSlug_layoutLearningRouteChildren {
  LearnCourseSlugLessonSlug_layoutLearningIndexRoute: typeof LearnCourseSlugLessonSlug_layoutLearningIndexRoute
}

const LearnCourseSlugLessonSlug_layoutLearningRouteChildren: LearnCourseSlugLessonSlug_layoutLearningRouteChildren =
  {
    LearnCourseSlugLessonSlug_layoutLearningIndexRoute:
      LearnCourseSlugLessonSlug_layoutLearningIndexRoute,
  }

const LearnCourseSlugLessonSlug_layoutLearningRouteWithChildren =
  LearnCourseSlugLessonSlug_layoutLearningRoute._addFileChildren(
    LearnCourseSlugLessonSlug_layoutLearningRouteChildren,
  )

interface LearnCourseSlugLessonSlugRouteChildren {
  LearnCourseSlugLessonSlug_layoutLearningRoute: typeof LearnCourseSlugLessonSlug_layoutLearningRouteWithChildren
}

const LearnCourseSlugLessonSlugRouteChildren: LearnCourseSlugLessonSlugRouteChildren =
  {
    LearnCourseSlugLessonSlug_layoutLearningRoute:
      LearnCourseSlugLessonSlug_layoutLearningRouteWithChildren,
  }

const LearnCourseSlugLessonSlugRouteWithChildren =
  LearnCourseSlugLessonSlugRoute._addFileChildren(
    LearnCourseSlugLessonSlugRouteChildren,
  )

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  _layoutRoute: _layoutRouteWithChildren,
  PrivacyRoute: PrivacyRoute,
  TermsOfServiceRoute: TermsOfServiceRoute,
  LearnCourseSlugLessonSlugRoute: LearnCourseSlugLessonSlugRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
