import { createFileRoute, Outlet } from "@tanstack/react-router";
import { SiteHeader } from "@/components/site-header";
import { serverFnAuthHint } from "@/server/authHint";
import { atomAuthHint, authStore } from "@/store/auth";

export const Route = createFileRoute("/__layout")({
  component: RouteComponent,
  beforeLoad: async () => {
    const authHint = await serverFnAuthHint();
    authStore.set(atomAuthHint, authHint);
    return { authHint };
  },
});

function RouteComponent() {
  return (
    <main>
      <SiteHeader />
      <Outlet />
    </main>
  );
}
