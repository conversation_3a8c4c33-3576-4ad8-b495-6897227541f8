import { createFileRoute } from "@tanstack/react-router";
import { SearchIcon } from "lucide-react";
import AiloIcon from "@/assets/images/aicademy-logo-mobile.png";
import { Divider } from "@/components/course/course-detail-items";
import { InformationList } from "@/components/help-center/information-list";
import { GradientText } from "@/components/ui/gradient-text";
import * as m from "@/paraglide/messages";

export const Route = createFileRoute("/__layout/help-center/")({
  component: PageHelpCenter,
});

function PageHelpCenter() {
  return (
    <div className="flex w-full flex-col items-center bg-(--layout-bg) py-8 text-center">
      <GradientText className="font-light text-2xl md:text-3xl">
        {m["help_center.greeting"]()}
      </GradientText>
      <div className="my-4 flex w-[85%] max-w-md items-center rounded-full bg-white px-5 py-3 md:w-1/2">
        <SearchIcon className="size-4" />
        <span className="ml-2 text-(--color-grey-2) text-sm">
          {m["help_center.search_placeholder"]()}
        </span>
      </div>
      <div className="container my-10 flex max-w-5xl flex-col gap-4 bg-white p-10 sm:rounded-md sm:border-(--card-border-secondary) sm:border-1">
        <InformationList />
        {/* <Divider />
        <Explore /> */}
        <Divider />
        <div className="my-10 flex flex-col">
          <div className="mb-4 text-left font-semibold text-2xl">
            {m["help_center.still_have_questions"]()}
          </div>
          <div className="grid gap-8 sm:grid-cols-2">
            <div className="flex items-center gap-6">
              <img
                src={AiloIcon}
                alt="Ailo"
                className="h-10 w-10 rounded-full"
              />
              <div className="text-left">
                <div className="text-lg">{m["help_center.email_us"]()}</div>
                <div className="text-sm"><EMAIL></div>
              </div>
            </div>
            <div className="flex items-center gap-6">
              <img
                src={AiloIcon}
                alt="Ailo"
                className="h-10 w-10 rounded-full"
              />
              <div className="text-left">
                <div className="text-lg">
                  {m["help_center.chat_with_ailo"]()}
                </div>
                <div className="text-sm">
                  {m["help_center.get_instant_answers"]()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
