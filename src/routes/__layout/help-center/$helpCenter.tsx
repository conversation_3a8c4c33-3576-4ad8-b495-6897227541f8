import { createFileRoute } from "@tanstack/react-router";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Bread<PERSON>rumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { GradientText } from "@/components/ui/gradient-text";
import { helpCenterDataEn } from "@/data/help-center-en";
import { helpCenterDataVi } from "@/data/help-center-vi";
import * as m from "@/paraglide/messages";
import { getLocale } from "@/paraglide/runtime";

export const Route = createFileRoute("/__layout/help-center/$helpCenter")({
  component: RouteComponent,
});

function RouteComponent() {
  const { helpCenter } = Route.useParams();
  const locale = getLocale();
  const helpCenterData = locale === "en" ? helpCenterDataEn : helpCenterDataVi;

  const section = helpCenterData.find((s) => s.id === helpCenter);

  if (!section) {
    return (
      <div className="container my-10 flex max-w-5xl flex-col gap-4 bg-white p-10 sm:rounded-md">
        <GradientText className="font-light text-3xl lg:text-[42px]">
          {m["help_center.section_not_found"]()}
        </GradientText>
        <p>{m["help_center.section_not_found_desc"]()}</p>
      </div>
    );
  }

  return (
    <div className="container my-10 flex max-w-5xl flex-col gap-4">
      <div>
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/help-center">
                {m["help_center.title"]()}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem className="!text-black">
              {section.title}
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <div className="flex flex-col gap-4 bg-white p-10 sm:rounded-md">
        <header className=" flex flex-col md:mb-0 md:flex-row md:gap-6 lg:items-center">
          <div className="flex items-center gap-4 md:gap-8">
            <GradientText className="whitespace-nowrap font-light text-3xl lg:text-[42px]">
              {section.title}
            </GradientText>
          </div>
          <p className="translate-y-2 text-sm lg:max-w-[60%]">
            {section.description}
          </p>
        </header>
        <div className="mt-1 w-full border-(--card-border-secondary) border-b" />

        <Accordion
          type="single"
          collapsible
          className="mt-4 w-full text-left sm:mt-6"
        >
          {section.items.map((item, index) => (
            <AccordionItem
              key={`item-${index.toString()}`}
              value={`item-${index}`}
            >
              <AccordionTrigger className="font-normal md:text-lg">
                {index + 1}. {item.question}
              </AccordionTrigger>
              <AccordionContent className="text-sm">
                {item.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  );
}
