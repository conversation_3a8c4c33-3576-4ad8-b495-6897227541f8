import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import AiMentorCard from "@/components/dashboard/cards/ai-mentor";
import CourseCard from "@/components/dashboard/cards/course";
import DailyAttendanceCard from "@/components/dashboard/cards/daily-attendance";
import InviteFriendCard from "@/components/dashboard/cards/invite-friend";
import LeaderboardCard from "@/components/dashboard/cards/leaderboard";
import { useProfile } from "@/services/auth";
import { categoriesQueryOptions } from "@/services/categories";
import { User } from "@/types/auth";
import { requireAuth } from "@/utils/auth-guard";
import { cn } from "@/utils/cn";

export const Route = createFileRoute("/__layout/dashboard")({
  loader: async ({ context }) => {
    await context.queryClient.ensureQueryData(categoriesQueryOptions());
  },
  component: Dashboard,
  beforeLoad: requireAuth,
});

const SideBarInfo = ({
  profile,
  expandLeaderboard,
  setExpandLeaderboard,
  refetchProfile,
}: {
  refetchProfile: () => void;
  profile: User;
  expandLeaderboard: boolean;
  setExpandLeaderboard: (isExpanded: boolean) => void;
}) => (
  <div className="flex h-full flex-1 flex-col gap-3 md:gap-4">
    {!expandLeaderboard && (
      <DailyAttendanceCard refetchProfile={refetchProfile} profile={profile} />
    )}
    <div className={cn("flex-1", expandLeaderboard && "h-full")}>
      <LeaderboardCard
        expandLeaderboard={expandLeaderboard}
        setExpandLeaderboard={setExpandLeaderboard}
      />
    </div>
  </div>
);

function Dashboard() {
  const { data: profile, refetch } = useProfile();
  const [expandLeaderboard, setExpandLeaderboard] = useState(false);
  const { data: categories } = useSuspenseQuery(categoriesQueryOptions());
  const [mainContentHeight, setMainContentHeight] = useState(0);
  const mainContentRef = useRef<HTMLDivElement>(null);

  // biome-ignore lint/correctness/useExhaustiveDependencies: listen when ref changes
  useEffect(() => {
    if (!mainContentRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        setMainContentHeight(entry.contentRect.height);
      }
    });

    resizeObserver.observe(mainContentRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [mainContentRef.current, profile]);

  if (!profile) {
    return null;
  }

  return (
    <main className="container mx-auto w-full max-w-7xl py-5">
      <h2 className="mb-2 text-lg">Welcome, {profile?.name}</h2>

      {/* Desktop Layout */}
      <div className={cn("hidden w-full flex-wrap gap-3 md:flex md:gap-6")}>
        {/* Left column */}
        <motion.div
          className="flex w-full flex-col gap-3 overflow-hidden md:w-[30%] md:gap-4"
          style={{ height: mainContentHeight || "40rem" }}
        >
          <AnimatePresence>
            {!expandLeaderboard && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="grid flex-shrink-0 grid-cols-2 gap-2 sm:gap-3"
              >
                <InviteFriendCard profile={profile} />
                <AiMentorCard />
              </motion.div>
            )}
          </AnimatePresence>
          <SideBarInfo
            refetchProfile={refetch}
            profile={profile}
            expandLeaderboard={expandLeaderboard}
            setExpandLeaderboard={setExpandLeaderboard}
          />
        </motion.div>

        {/* Main content */}
        <motion.div
          layout
          className="flex-1 overflow-hidden"
          ref={mainContentRef}
        >
          <CourseCard categories={categories} />
        </motion.div>
      </div>

      {/* mobile content */}
      <div className="block space-y-3 md:hidden">
        <AnimatePresence>
          {!expandLeaderboard && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="grid flex-shrink-0 grid-cols-2 gap-2"
            >
              <InviteFriendCard profile={profile} />
              <AiMentorCard />
            </motion.div>
          )}
        </AnimatePresence>

        <motion.div layout className="w-full" data-main-content>
          <CourseCard categories={categories} />
        </motion.div>

        <div className="mb-8 w-full">
          <SideBarInfo
            refetchProfile={refetch}
            profile={profile}
            expandLeaderboard={expandLeaderboard}
            setExpandLeaderboard={setExpandLeaderboard}
          />
        </div>
      </div>
    </main>
  );
}
