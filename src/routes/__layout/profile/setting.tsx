import { createFileRoute } from "@tanstack/react-router";
import { useProfileForm } from "@/components/user/useProfileForm";
import { UserEditForm } from "@/components/user/user-edit-form";
import { UserInfo } from "@/components/user/user-info";
import { useProfile } from "@/services/auth";

export const Route = createFileRoute("/__layout/profile/setting")({
  component: RouteComponent,
});

function RouteComponent() {
  const { data: profile } = useProfile();
  const form = useProfileForm({ profile });

  return (
    <main className="container mx-auto w-full max-w-7xl py-5">
      <div className="flex w-full flex-wrap gap-3 md:gap-6">
        <div className="flex w-full flex-col gap-3 md:w-[30%] md:gap-4">
          <UserInfo user={profile} form={form} />
        </div>
        <UserEditForm currentProfile={profile} form={form} />
      </div>
    </main>
  );
}
