import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { type } from "arktype";
import { motion } from "framer-motion";
import { useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Tab, Tabs } from "@/components/ui/tabs";
import { UserAchievement } from "@/components/user/user-achievement";
import UserFriends from "@/components/user/user-friends";
import { UserInfo } from "@/components/user/user-info";
import { UserReview } from "@/components/user/user-review";
import UserStatistics from "@/components/user/user-statistics";
import { useProfile } from "@/services/auth";
import { useMyLeaderboard } from "@/services/leaderboard";
import { useMyVoting } from "@/services/my-learning";

const TabQueries = type({
  tab: type.string.optional(),
});

const tabs = [
  { id: "friends", label: "Friends" },
  { id: "my-review", label: "My Review" },
  { id: "achievement", label: "Achievement" },
];

export const Route = createFileRoute("/__layout/profile/me")({
  component: RouteComponent,
  validateSearch: TabQueries,
});

function RouteComponent() {
  const { data: userProfile } = useProfile({ stats: true });
  const { data: rank } = useMyLeaderboard();
  const { data: votings } = useMyVoting({ withCourses: true });

  const navigate = useNavigate({ from: Route.fullPath });
  const { tab } = Route.useSearch();

  const activeTab = useMemo(() => {
    if (!tab) return tabs[0].label;
    const t = tabs.find((e) => e.id === tab);
    return t ? t.label : tabs[0].label;
  }, [tab]);

  const onChangeTab = (tabName: string) => {
    const t = tabs.find((e) => e.label === tabName);
    t &&
      navigate({
        search: { tab: t.id },
      });
  };
  return (
    <main className="container mx-auto w-full max-w-7xl py-5">
      <div className="flex w-full flex-wrap gap-3 lg:gap-6">
        <div className="flex w-full flex-col gap-3 md:w-[30%] md:gap-4">
          <UserInfo user={userProfile} />
          <UserStatistics user={userProfile} rank={rank} />
        </div>

        {userProfile && (
          <motion.div layout className="flex-1 overflow-hidden">
            <div className="h-full">
              {
                <Card className="h-full rounded-lg border-(--card-border-secondary) p-0">
                  <CardContent className="py-3 md:px-10">
                    <Tabs
                      defaultActiveTab={activeTab}
                      onChangeTab={onChangeTab}
                    >
                      <Tab label="Friends">
                        <UserFriends profile={userProfile} />
                      </Tab>
                      <Tab label="My Review">
                        <UserReview votings={votings} />
                      </Tab>
                      <Tab label="Achievement">
                        <UserAchievement user={userProfile} />
                      </Tab>
                    </Tabs>
                  </CardContent>
                </Card>
              }
            </div>
          </motion.div>
        )}
      </div>
    </main>
  );
}
