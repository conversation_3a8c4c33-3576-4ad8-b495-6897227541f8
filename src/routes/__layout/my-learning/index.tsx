import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { type } from "arktype";
import { useCallback } from "react";
import EmptyLearningImg from "@/assets/images/empty-learning-progress.png";
import owlLearning from "@/assets/images/owls/owl-learning.png";
import {
  LearningItem,
  LearningItemSkeleton,
} from "@/components/dashboard/learning-item";
import { Button } from "@/components/ui/button";
import { GradientText } from "@/components/ui/gradient-text";
import * as m from "@/paraglide/messages.js";
import { useLearningCourses } from "@/services/my-learning";
import { cn } from "@/utils/cn";

export enum ELearningStatus {
  IN_PROGRESS = "in-progress",
  COMPLETED = "completed",
}

const queryParams = type({
  status: type.valueOf(ELearningStatus).optional(),
});

export const Route = createFileRoute("/__layout/my-learning/")({
  component: MyLearningComponent,
  validateSearch: queryParams,
});

function MyLearningComponent() {
  const search = Route.useSearch();
  const status = search.status;
  const navigate = useNavigate({ from: Route.fullPath });
  const isCompleted = status === ELearningStatus.COMPLETED;
  const { data: learningCourses, isFetching } = useLearningCourses(
    isCompleted ? { completed: true } : {},
  );
  const statusButtons = (
    Object.keys(ELearningStatus) as Array<keyof typeof ELearningStatus>
  ).map((key) => ({
    key,
    value: ELearningStatus[key],
    label: ELearningStatus[key].replaceAll("-", " "),
    isActive: search.status
      ? search.status === ELearningStatus[key]
      : key === "IN_PROGRESS",
  }));

  const filterStatus = useCallback(
    (status: ELearningStatus) => {
      navigate({ search: () => ({ status }) });
    },
    [navigate],
  );

  return (
    <div className="my-5 md:my-8">
      <div className="flex w-full xs:flex-row flex-col-reverse justify-between gap-x-10 gap-y-2 overflow-hidden sm:items-end">
        <div className="flex xs:max-w-[50%] flex-col flex-wrap items-start gap-x-3 gap-y-2 pb-3 md:items-center md:gap-x-5 lg:flex-row">
          <GradientText className="w-full font-light text-3xl leading-10 sm:text-5xl sm:leading-14 md:w-auto xl:text-6xl xl:leading-20">
            My Learning
          </GradientText>
          <div className="flex items-center gap-x-3 md:gap-x-5">
            {statusButtons.map((item) => {
              return (
                <Button
                  key={item.key}
                  variant="outline"
                  className={cn(
                    "rounded-full capitalize hover:bg-gray-200 hover:text-foreground md:translate-y-1",
                    item.isActive &&
                      "bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:text-secondary-foreground",
                  )}
                  size="lg"
                  onClick={() => filterStatus(item.value)}
                >
                  {item.label}
                </Button>
              );
            })}
          </div>
        </div>
        <div className="max-w-[40%]">
          <img
            src={owlLearning}
            alt={"Learning"}
            className="size-full max-h-36 object-cover md:max-h-40"
          />
        </div>
      </div>

      <div className="flex flex-col gap-4 md:gap-5">
        {isFetching && !learningCourses?.length ? (
          // Show skeleton loading
          Array.from({ length: 3 }).map((_, index) => (
            <LearningItemSkeleton
              key={`skeleton-${index.toString()}`}
              isCompleted={isCompleted}
            />
          ))
        ) : !learningCourses?.length ? (
          <EmptyLearning
            content={
              status === ELearningStatus.COMPLETED
                ? m["learning.empty_states.completed"]()
                : m["learning.empty_states.in_progress"]()
            }
          />
        ) : (
          learningCourses?.map((course) => (
            <LearningItem
              key={course.course_id}
              userCourse={course}
              isCompleted={isCompleted}
            />
          ))
        )}
      </div>
    </div>
  );
}

const EmptyLearning = ({ content }: { content: string }) => {
  return (
    <div className="flex w-full flex-col items-center justify-center gap-6 rounded-(--size-2xs) bg-(--card) p-20 py-20">
      <img src={EmptyLearningImg} alt="Empty Learning" />
      <p className="sm:text-xl">{content}</p>
    </div>
  );
};
