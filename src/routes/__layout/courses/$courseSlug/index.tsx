import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute, redirect } from "@tanstack/react-router";
import { memo, useMemo, useState } from "react";
import { CourseBreadcrumb } from "@/components/course/breadcrumb";
import CourseAuthor from "@/components/course/course-author";
import { CourseMapper } from "@/components/course/course-container";
import {
  ActionButtonsProps,
  CourseActionButtons,
  CourseDescriptionSection,
  CourseImageSection,
  Divider,
} from "@/components/course/course-detail-items";
import { CourseError } from "@/components/course/course-error";
import CourseInstructor from "@/components/course/course-instructor";
import CourseRating from "@/components/course/course-rating";
import CourseReview from "@/components/course/course-review";
import { CourseShare } from "@/components/course/course-share";
import { LessonModules } from "@/components/lesson/modules";
import LessonUnlock from "@/components/lesson-unlock";
import { ProgressWithLabel } from "@/components/progress-with-label";
import { CourseCategoryTag } from "@/components/ui/hashtag";
import * as m from "@/paraglide/messages.js";
import {
  courseQueryOptions,
  useCourses,
  useUserCourseProgresses,
} from "@/services/courses";
import { getLearningCourseProgresses } from "@/services/courses/getLearningCourseProgresses";
import { lessonsQueryOptions } from "@/services/lessons";
import { Course, CourseDescription, UserCourse } from "@/types/courses";
import { Lesson } from "@/types/lessons";
import { cn } from "@/utils/cn";
import { parseCourseDescription } from "@/utils/course";
import { seo } from "@/utils/seo";

export const Route = createFileRoute("/__layout/courses/$courseSlug/")({
  loader: async ({ params: { courseSlug }, context }) => {
    const [course, lessons] = await Promise.all([
      context.queryClient.ensureQueryData(courseQueryOptions(courseSlug)),
      context.queryClient.ensureQueryData(lessonsQueryOptions(courseSlug)),
    ]);
    if (!course || course.unpublished) {
      throw redirect({ to: "/courses" });
    }
    return { course, lessons };
  },
  head: ({ loaderData }) => {
    if (!loaderData) return { meta: undefined };
    const description: CourseDescription = parseCourseDescription(
      loaderData.course.description,
    );
    return {
      meta: seo({
        title: loaderData.course.name,
        description: description.overview,
        image: loaderData.course.image_url,
        keywords: [
          loaderData.course.categories?.[0]?.name ?? "",
          ...description.goals,
        ].join(", "),
      }),
    };
  },
  errorComponent: () => (
    <CourseError status="error" content={m["courses.unexpected_error"]()} />
  ),
  notFoundComponent: () => (
    <CourseError status="not-found" content={m["courses.course_not_found"]()} />
  ),
  component: CourseComponent,
});

interface SidebarProps {
  userCourse?: UserCourse;
  lessons: Lesson[];
}

interface MobileHeaderProps {
  course: Course;
  isOpen: boolean;
  toggleOpen: () => void;
}

function CourseComponent() {
  const { courseSlug } = Route.useParams();
  const { data: course } = useSuspenseQuery(courseQueryOptions(courseSlug));
  const { data: lessons = [] } = useSuspenseQuery(
    lessonsQueryOptions(courseSlug),
  );
  const { data: userCourse } = useUserCourseProgresses(course.id);
  const [isCourseInfoOpen, setIsCourseInfoOpen] = useState(true);

  const toggleCourseInfo = () => setIsCourseInfoOpen((prev) => !prev);

  const isCourseCompleted = useMemo(
    () =>
      !!userCourse?.progresses &&
      lessons.length > 0 &&
      lessons.every(
        (lesson) => userCourse.progresses[lesson.id] === "COMPLETED",
      ),
    [lessons, userCourse?.progresses],
  );

  const continueLessonSlug = useMemo(
    () =>
      lessons.length > 0
        ? isCourseCompleted
          ? lessons[0].slug
          : userCourse?.continue_lesson?.slug || lessons[0].slug
        : undefined,
    [isCourseCompleted, lessons, userCourse?.continue_lesson],
  );

  return (
    <div className="-mt-10 relative grid grid-cols-10 pb-10 lg:container lg:mt-0">
      <MobileHeader
        course={course}
        isOpen={isCourseInfoOpen}
        toggleOpen={toggleCourseInfo}
      />
      <Sidebar userCourse={userCourse} lessons={lessons} />
      {!isCourseInfoOpen && (
        <MobileCurriculum userCourse={userCourse} lessons={lessons} />
      )}
      {isCourseInfoOpen && (
        <MobileActionButtons
          course={course}
          continueLessonSlug={continueLessonSlug}
          isCourseCompleted={isCourseCompleted}
          userCourse={userCourse}
        />
      )}
      <MainContent
        course={course}
        lessons={lessons}
        userCourse={userCourse}
        isCourseInfoOpen={isCourseInfoOpen}
        toggleCourseInfo={toggleCourseInfo}
        isCourseCompleted={isCourseCompleted}
        continueLessonSlug={continueLessonSlug}
      />
    </div>
  );
}

const Sidebar = memo(({ userCourse, lessons }: SidebarProps) => (
  <div className="col-span-0 lg:col-span-4">
    <div className="mx-auto hidden lg:block lg:max-w-lg">
      <LessonModules userCourse={userCourse} lessons={lessons} />
    </div>
  </div>
));

const MobileCurriculum = memo(({ userCourse, lessons }: SidebarProps) => (
  <div className="col-span-10 mt-4 items-center justify-center sm:mt-20 lg:hidden">
    <div className="mx-auto max-w-[90dvw]">
      <LessonModules userCourse={userCourse} lessons={lessons} />
    </div>
  </div>
));

const MobileHeader = memo(
  ({ course, isOpen, toggleOpen }: MobileHeaderProps) => {
    if (isOpen) return null;

    return (
      <div
        className="w-screen cursor-pointer bg-[var(--card)] px-3 py-4 text-center shadow-[0px_3.95px_3.95px_0px_#10184026] lg:hidden"
        onClick={toggleOpen}
      >
        <p className="mb-1 text-xs">
          {m["courses.courses_breadcrumb"]()} /{" "}
          {course.categories?.[0]?.name ?? m["courses.unknown_category"]()}
        </p>
        <p className="text-xl">{course.name}</p>
      </div>
    );
  },
);

const MobileActionButtons = (props: ActionButtonsProps) => (
  <div className="fixed bottom-0 left-0 z-40 flex w-full justify-center border-gray-200 border-t bg-[var(--card)] pt-3 pb-4 lg:hidden">
    <div className="min-w-[80dvw] sm:min-w-[70dvw]">
      <CourseActionButtons {...props} isFixedBottom />
    </div>
  </div>
);

interface MainContentProps {
  course: Course;
  lessons: Lesson[];
  userCourse?: UserCourse;
  isCourseInfoOpen: boolean;
  toggleCourseInfo: () => void;
  isCourseCompleted: boolean;
  continueLessonSlug?: string;
}

const MainContent = memo(
  ({
    course,
    lessons,
    userCourse,
    isCourseInfoOpen,
    toggleCourseInfo,
    isCourseCompleted,
    continueLessonSlug,
  }: MainContentProps) => {
    const { courseSlug } = Route.useParams();
    const { data: courses = [] } = useCourses({
      category_id: course.categories?.[0]?.id ?? undefined,
    });

    const description = useMemo(
      () => parseCourseDescription(course.description),
      [course.description],
    );

    const learningProgresses = useMemo(
      () =>
        getLearningCourseProgresses({
          lessons,
          userCourse,
          isCourseCompleted,
        }),
      [lessons, userCourse, isCourseCompleted],
    );

    const filteredCourses = useMemo(
      () => courses.filter((c) => c.slug !== courseSlug),
      [courses, courseSlug],
    );

    return (
      <div
        className={cn(
          "col-span-10 w-full transition-all lg:col-span-6 lg:block lg:h-auto lg:animate-none lg:pr-8",
          isCourseInfoOpen
            ? "fade-in slide-in-from-top-8 animate-in duration-500"
            : "h-0",
        )}
      >
        <div className="hidden lg:block">
          <CourseBreadcrumb course={course} />
        </div>
        <div
          className={cn(
            "hidden rounded-(--size-2xs) bg-[var(--card-tag-color)] px-4 py-7 pb-20 sm:px-9 lg:block",
            isCourseInfoOpen ? "block" : "hidden",
          )}
        >
          <div className="text-end lg:hidden">
            <p
              className="mb-3 inline-block cursor-pointer underline underline-offset-2"
              onClick={toggleCourseInfo}
            >
              {m["courses.curriculum"]()} {">>"}
            </p>
          </div>
          <CourseCategoryTag tag={course.categories?.[0]?.slug ?? ""} />
          <h4 className="mt-5 text-center text-2xl sm:text-left sm:text-3xl">
            {course.name}
          </h4>
          <div className="my-3 flex flex-col items-center justify-between gap-2 sm:flex-row">
            <CourseAuthor course={course} />
            {course.stats && (
              <CourseRating courseId={course.id} stats={course.stats} />
            )}
          </div>
          <ProgressWithLabel
            value={learningProgresses}
            className="h-1.5 md:h-2.5"
            indicatorColor="bg-green-500"
            labelClassName="bg-green-500 text-white"
          />
          <div className="my-5 flex flex-col items-start gap-6 sm:flex-row md:my-9 md:gap-8 lg:gap-12 xl:gap-10">
            <div className="sm:max-w-1/3">
              <CourseImageSection course={course} />
            </div>
            <CourseDescriptionSection
              description={description}
              course={course}
            />
          </div>
          <div className="flex flex-row items-center justify-start gap-5 md:justify-between">
            <div className="hidden w-full md:w-[40%] lg:inline-block">
              <CourseActionButtons
                course={course}
                continueLessonSlug={continueLessonSlug}
                isCourseCompleted={isCourseCompleted}
                userCourse={userCourse}
              />
            </div>
            <CourseShare
              course={course}
              head={
                <div>
                  <p className="text-base">{m["courses.share_and_learn"]()}</p>
                  <p className="text-xs">{m["courses.with_friends"]()}</p>
                </div>
              }
            />
          </div>
          {filteredCourses.length > 0 && (
            <>
              <Divider />
              <div className="mt-5">
                <CourseMapper
                  category={course.categories?.[0]}
                  data={filteredCourses}
                  className="gap-4 sm:basis-1/2 lg:basis-1/3 lg:gap-6 xl:basis-1/3"
                  showButton
                  isCarousel
                />
              </div>
            </>
          )}
          <Divider />
          <CourseInstructor course={course} stat={course.stats} />
          <Divider />
          <CourseReview courseId={course.id} />
          <LessonUnlock />
        </div>
      </div>
    );
  },
);
