import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { CourseContainer } from "@/components/course/course-container";
import { CourseError } from "@/components/course/course-error";
import * as m from "@/paraglide/messages.js";
import { categoriesQueryOptions } from "@/services/categories";
import { coursesQueryOptions } from "@/services/courses";

export const Route = createFileRoute("/__layout/courses/")({
  loaderDeps: ({ search: { category } }) => ({ category }),
  loader: async ({ context: { queryClient }, deps }) => {
    const categorySlug = deps?.category;
    const categories = await queryClient.ensureQueryData(
      categoriesQueryOptions(),
    );

    if (!categorySlug) {
      await queryClient.ensureQueryData(coursesQueryOptions({}));
      return { queryFilter: {} };
    }

    if (!categories.length) {
      return {
        queryFilter: {},
      };
    }

    const categoryFiltered =
      categories.find((category) => category.slug === categorySlug) ||
      categories[0];

    // Load courses filtered by the selected category
    await queryClient.ensureQueryData(
      coursesQueryOptions({ category_id: categoryFiltered.id }),
    );

    return { queryFilter: { category_id: categoryFiltered.id } };
  },
  errorComponent: () => (
    <CourseError content={m["errors.general"]()} status="error" />
  ),
  component: CoursesIndexComponent,
  notFoundComponent: () => {
    return <CourseError content={m["errors.not_found"]()} status="not-found" />;
  },
});

function CoursesIndexComponent() {
  const { queryFilter } = Route.useLoaderData();
  const { data: courses } = useSuspenseQuery(coursesQueryOptions(queryFilter));

  return (
    <div className="container flex flex-col gap-8 pb-10">
      <CourseContainer
        headerImage={""}
        headerTitle={m["courses.all_courses"]()}
        headerContent={m["landing.discover_courses.description"]()}
        data={courses}
        showFilter
      />
    </div>
  );
}
