import { createFileRoute, Outlet } from "@tanstack/react-router";
import { type } from "arktype";

export const CourseFiltersQueries = type({
  category: type.string.optional(),
  sort: type.string.optional(),
});
export const Route = createFileRoute("/__layout/courses")({
  validateSearch: CourseFiltersQueries,
  head: () => ({
    meta: [{ title: "Courses" }],
  }),
  component: CoursesComponent,
});

function CoursesComponent() {
  return (
    <div className="mt-10 flex-1">
      <Outlet />
    </div>
  );
}
