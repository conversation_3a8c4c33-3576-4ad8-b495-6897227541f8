import { createFileRoute, Outlet } from "@tanstack/react-router";
import { type } from "arktype";
import { atomAuthHint, authStore } from "@/store/auth";
import { requireAuth } from "@/utils/auth-guard";

export const CourseFiltersQueries = type({
  category: type.string.optional(),
  sort: type.string.optional(),
});
export const Route = createFileRoute("/__layout/courses")({
  validateSearch: CourseFiltersQueries,
  head: () => ({
    meta: [{ title: "Courses" }],
  }),
  beforeLoad: async () => {
    const result = await requireAuth();
    authStore.set(atomAuthHint, result.authHint);
    return result;
  },
  component: CoursesComponent,
});

function CoursesComponent() {
  return (
    <div className="mt-10 flex-1">
      <Outlet />
    </div>
  );
}
