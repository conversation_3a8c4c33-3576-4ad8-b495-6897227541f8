import { createFileRoute, redirect } from "@tanstack/react-router";
import { LoginMenu } from "@/components/navbar";
import { serverFnAuthHint } from "@/server/authHint";

export const Route = createFileRoute("/")({
  loader: async () => {
    const authHint = await serverFnAuthHint();
    if (authHint) throw redirect({ to: "/dashboard" });
    return { authHint };
  },
  component: LandingPage,
});

function LandingPage() {
  const { authHint } = Route.useLoaderData();

  if (!authHint) {
    return (
      <main className="flex min-h-screen items-center justify-center scroll-smooth">
        <div className="flex min-h-72 w-[90%] rounded-xl bg-white p-4 shadow-lg sm:max-w-lg sm:p-8">
          <LoginMenu />
        </div>
      </main>
    );
  }

  return null;
}
