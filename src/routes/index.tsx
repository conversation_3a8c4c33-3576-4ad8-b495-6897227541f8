import { createFileRoute } from "@tanstack/react-router";
import { useAtom, useAtomValue } from "jotai";
import InviteDialog from "@/components/invite-dialog";
import { DiscoverCourses } from "@/components/landing/discover-courses";
import { Discover<PERSON>ay<PERSON>earn } from "@/components/landing/discover-way-learn";
import { Faq } from "@/components/landing/faq";
import { StarterScreen } from "@/components/landing/starter-screen";
import { WhyChoose } from "@/components/landing/why-choose";
import { LandingNavbar } from "@/components/landing-navbar";
import { atomAuthRef, atomAuthRefDismissed } from "@/store/auth";

export const Route = createFileRoute("/")({
  component: LandingPage,
});

function LandingPage() {
  const referral = useAtomValue(atomAuthRef);
  const [referralDismissed, dismiss] = useAtom(atomAuthRefDismissed);

  return (
    <main className="landing-container scroll-smooth">
      <div className="flex w-full items-center justify-between gap-2 bg-(--card) py-1">
        <div className="flex size-full items-center justify-between gap-4 overflow-hidden px-3 shadow-[0px_4px_6px_0px_#D8D8D840] sm:px-10 lg:gap-8.5">
          <LandingNavbar />
        </div>
      </div>
      <div className="mx-auto w-full bg-(--card)">
        <div className="flex flex-col">
          <StarterScreen />
          <DiscoverCourses />
          <WhyChoose />
          <Faq />
        </div>
        <DiscoverWayLearn />
      </div>
      <InviteDialog
        open={!!referral && !referralDismissed}
        onOpenChange={(v) => {
          dismiss(!v);
        }}
        referral={referral}
      />
    </main>
  );
}
