import { useQuery, useSuspenseQ<PERSON>y } from "@tanstack/react-query";
import { createFileRoute, Outlet } from "@tanstack/react-router";
import { motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { XIcon } from "lucide-react";
import { CSSProperties, JSX, RefObject, useEffect, useRef } from "react";
import {
  ChatbotContent,
  ChatCooking,
  ChatMessageRender,
} from "@/components/chatbot/chatbot-content";
import { ChatbotInput } from "@/components/chatbot/chatbot-input";
import { WebSocketChatProvider } from "@/components/chatbot/websocket-chat-provider";
import { NoteIcon, PanelIcon } from "@/components/icons";
import { LessonHeader } from "@/components/lesson/lesson-header";
import { LessonModules } from "@/components/lesson/modules";
import {
  Said<PERSON>,
  SaidbarContent,
  SaidbarTrigger,
  useSaidbar,
} from "@/components/saidbar";
import { But<PERSON> } from "@/components/ui/button";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  ChatMessageQuery,
  lessonConversationsQueryOptions,
  useChatMessages,
} from "@/services/conversations";
import {
  courseQueryOptions,
  useUserCourseProgresses,
} from "@/services/courses";
import { lessonQueryOptions, lessonsQueryOptions } from "@/services/lessons";
import { atomAuthHint, authStore } from "@/store/auth";
import { atomCurrentSection } from "@/store/chat";
import {
  atomSidebarCourseChat,
  atomSidebarCourseModule,
} from "@/store/sidebar";
import { Conversation } from "@/types/chat";
import { UserCourse } from "@/types/courses";
import { Lesson } from "@/types/lessons";
import { requireAuth } from "@/utils/auth-guard";
import { cn } from "@/utils/cn";
import { useIsMobile } from "@/utils/use-mobile";
import "@/styles/tiptap.css";

const chatbotVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export const Route = createFileRoute(
  "/learn/$courseSlug/$lessonSlug/__layout-learning",
)({
  // loader: async ({ params: { courseSlug, lessonSlug }, context }) => {
  //   await Promise.all([
  //     context.queryClient.ensureQueryData(courseQueryOptions(courseSlug)),
  //     context.queryClient.ensureQueryData(lessonsQueryOptions(courseSlug)),
  //     context.queryClient.ensureQueryData(lessonQueryOptions(lessonSlug)),
  //   ]);
  // },
  component: RouteComponent,
  beforeLoad: async () => {
    const result = await requireAuth();
    authStore.set(atomAuthHint, result.authHint);
    return result;
  },
});

function RouteComponent() {
  const { courseSlug, lessonSlug } = Route.useParams();
  const { data: course } = useSuspenseQuery(courseQueryOptions(courseSlug));
  const { data: lesson } = useSuspenseQuery(lessonQueryOptions(lessonSlug));
  const { data: lessons } = useSuspenseQuery(lessonsQueryOptions(courseSlug));
  const { data: userCourse } = useUserCourseProgresses(course.id);
  const currentSection = useAtomValue(atomCurrentSection);
  const conversations = useQuery(
    lessonConversationsQueryOptions({
      lessonID: lesson.id,
    }),
  );
  const conversation = conversations.data?.[0];
  const { messages } = useChatMessages({
    conversation,
    limit: 10,
  });
  const ref = useRef<HTMLDivElement>(null);

  const skeleton = conversations.isPending ? (
    <ChatMessageRender skeleton />
  ) : !conversation ? (
    <ChatCooking />
  ) : undefined;

  return (
    <WebSocketChatProvider conversationId={conversation?.id}>
      <div
        className="flex h-svh flex-col overflow-hidden bg-(--learning-layout-bg)"
        style={
          {
            "--sidebar": "var(--course-chat-bg)", // for bg-sidebar
            "--sidebar-width": "24rem",
            "--sidebar-width-icon": "5rem",
          } as CSSProperties
        }
      >
        <LessonHeader course={course} lesson={lesson} />
        <div
          className="relative z-30 flex h-full flex-1 overflow-hidden *:data-[slot=sheet-overlay]:hidden"
          ref={ref}
        >
          <SidebarModules
            currentLesson={lesson}
            active={lessonSlug}
            lessons={lessons}
            userCourse={userCourse}
          />

          <div className="flex h-full flex-1 flex-col overflow-hidden">
            <div className="h-full overflow-auto">
              <Outlet />
            </div>
            {currentSection && (
              <ChatCenter
                conversation={conversation}
                messages={messages}
                skeleton={skeleton}
                lessonId={lesson.id}
              />
            )}
          </div>

          {currentSection && (
            <SidebarChat
              conversation={conversation}
              messages={messages}
              skeleton={skeleton}
              container={ref}
              lessonId={lesson.id}
            />
          )}
        </div>
      </div>
    </WebSocketChatProvider>
  );
}

export function ChatIndicator() {
  return (
    <div className="flex items-center space-x-1 pb-2">
      <div className="size-2 animate-dot-pulse-opacity rounded-full bg-green-700"></div>
      <div className="size-2 animate-dot-pulse-opacity rounded-full bg-green-700 [animation-delay:0.1s]"></div>
      <div className="size-2 animate-dot-pulse-opacity rounded-full bg-green-700 [animation-delay:0.2s]"></div>
    </div>
  );
}

function SidebarModules({
  active,
  userCourse,
  lessons,
  currentLesson,
}: {
  active: string;
  userCourse: UserCourse | undefined;
  lessons: Lesson[] | undefined;
  currentLesson: Lesson | undefined;
}) {
  const { open: openSaidbarModule, openMobile: openSaidbarModuleMobile } =
    useSaidbar(atomSidebarCourseModule);

  useEffect(() => {
    if (!active || !userCourse) {
      return;
    }
    const element = document.getElementById(active);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "nearest",
      });
    }
  }, [active, userCourse]);

  return (
    <Saidbar
      collapsible="icon"
      side="left"
      atom={atomSidebarCourseModule}
      classNameInner="bg-(--background)"
    >
      <div className="flex items-center justify-end px-5 py-2">
        <SaidbarTrigger
          className="size-8"
          atom={atomSidebarCourseModule}
          icon={<PanelIcon className="size-full" />}
        />
      </div>
      <SaidbarContent>
        <div className="h-full">
          <LessonModules
            userCourse={userCourse}
            lessons={lessons || []}
            className={
              openSaidbarModule || openSaidbarModuleMobile ? "" : "hidden"
            }
            currentLesson={currentLesson}
          />
        </div>
      </SaidbarContent>
    </Saidbar>
  );
}

function ChatCenter({
  skeleton,
  conversation,
  messages,
  lessonId,
}: {
  messages: ChatMessageQuery;
  conversation: Conversation | undefined;
  skeleton: JSX.Element | undefined;
  lessonId: string;
}) {
  const { open: openSaidbarChat, setOpen: setOpenSaidbarChat } = useSaidbar(
    atomSidebarCourseChat,
  );
  const isMobile = useIsMobile();

  if (!conversation) {
    return null;
  }

  // Mobile: Drawer;
  if (isMobile) {
    return (
      <div className={cn(openSaidbarChat && "hidden")}>
        <Drawer
          open={openSaidbarChat}
          onOpenChange={setOpenSaidbarChat}
          dismissible={true}
        >
          <div className="sticky bottom-0 rounded-lg bg-sidebar p-4 pb-2">
            {(!openSaidbarChat || isMobile) && (
              <motion.div
                className="mx-auto flex w-full max-w-4xl flex-none flex-col gap-3"
                variants={chatbotVariants}
                initial="hidden"
                animate="visible"
                transition={{ duration: 0.4, ease: "easeOut" }}
              >
                <ChatbotInput conversation={conversation} />
              </motion.div>
            )}
          </div>
          <DrawerContent className="!max-h-[90vh] flex flex-col p-4">
            <div className="mx-auto mb-4 h-1 w-12 rounded-full bg-muted" />
            <div className="mb-4 flex items-center justify-between">
              <h3 className="font-semibold text-lg">Chat</h3>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setOpenSaidbarChat(false)}
              >
                <XIcon className="size-5" />
              </Button>
            </div>
            <div className="mb-4 flex-1 overflow-y-auto">
              {skeleton || (
                <ChatbotContent messages={messages} lessonId={lessonId} />
              )}
            </div>
            <div className="flex flex-col gap-2">
              <ChatbotInput conversation={conversation} />
            </div>
          </DrawerContent>
        </Drawer>
      </div>
    );
  }

  // Desktop: Original sticky behavior
  return (
    <div
      className={cn(
        "md:!shadow-none sticky bottom-0 rounded-lg bg-sidebar p-4 pb-2 md:bg-transparent ",
        openSaidbarChat && "md:hidden",
      )}
      style={{
        boxShadow:
          "0 -10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      }}
    >
      {openSaidbarChat && (
        <>
          <Button
            variant="ghost"
            className="!p-0 ml-auto flex flex-none"
            onClick={() => {
              setOpenSaidbarChat(false);
            }}
          >
            <XIcon className="size-5" />
          </Button>
          <div className="mb-2 h-[60dvh] overflow-y-auto">
            {skeleton || (
              <ChatbotContent messages={messages} lessonId={lessonId} />
            )}
          </div>
        </>
      )}
      {(!openSaidbarChat || isMobile) && (
        <motion.div
          className="mx-auto flex w-full max-w-4xl flex-none flex-col gap-3"
          variants={chatbotVariants}
          initial="hidden"
          animate="visible"
          transition={{ duration: 0.4, ease: "easeOut" }}
        >
          <ChatbotInput conversation={conversation} />
        </motion.div>
      )}
    </div>
  );
}

interface SidebarChatProps {
  conversation: Conversation | undefined;
  messages: ChatMessageQuery;
  skeleton: JSX.Element | undefined;
  container: RefObject<HTMLDivElement | null>;
  lessonId: string;
}

function SidebarChat({
  conversation,
  skeleton,
  messages,
  container,
  lessonId,
}: SidebarChatProps) {
  const { open: openSaidbarChat } = useSaidbar(atomSidebarCourseChat);

  if (!conversation) {
    return null;
  }

  return (
    <Sheet>
      <Saidbar
        collapsible="icon"
        side="right"
        atom={atomSidebarCourseChat}
        className="!absolute !top-auto !h-full"
      >
        <SaidbarContent className="h-full">
          <div className="relative flex h-full w-full flex-col">
            <div
              className={cn(
                "flex flex-none flex-col items-center p-5 py-2",
                openSaidbarChat && "flex-row justify-between",
              )}
            >
              <SaidbarTrigger
                className="size-8"
                atom={atomSidebarCourseChat}
                icon={<PanelIcon className="size-full" />}
              />
              <SheetTrigger asChild>
                <Button size="icon" variant="ghost" className={cn("size-8")}>
                  <NoteIcon className="size-8" />
                </Button>
              </SheetTrigger>
            </div>
            {openSaidbarChat && (
              <div className="flex h-full flex-1 flex-col overflow-y-auto overflow-x-hidden px-7.5 pb-1">
                <div className="mb-2 h-full flex-1 overflow-y-auto overflow-x-hidden pr-2">
                  {skeleton || (
                    <ChatbotContent messages={messages} lessonId={lessonId} />
                  )}
                </div>

                <div className="relative mt-auto flex flex-none flex-col gap-3 text-center">
                  <ChatbotInput conversation={conversation} full />
                </div>
              </div>
            )}
          </div>

          <SheetContent
            className="absolute w-(--sidebar-width) bg-(--course-card-bg) shadow-none"
            container={container.current}
          >
            <SheetHeader>
              <SheetTitle>Notes</SheetTitle>
              <SheetDescription>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Sunt
                necessitatibus blanditiis optio maiores hic ab ut, alias aliquam
                fuga tempora odio quia. Provident, deleniti culpa. Voluptatum
                magni laboriosam eligendi a.
              </SheetDescription>
            </SheetHeader>
          </SheetContent>
        </SaidbarContent>
      </Saidbar>
    </Sheet>
  );
}
