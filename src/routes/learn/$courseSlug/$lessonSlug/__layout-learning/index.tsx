import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { RefObject, useCallback, useMemo, useRef, useState } from "react";
import { GameStore } from "@/components/games/game-store";
import QuizGame from "@/components/games/quiz/QuizGame";
import JsxPreview from "@/components/JsxPreview";
import { CourseRating } from "@/components/lesson/exam/course-rating";
import { ExamSlide } from "@/components/lesson/exam/exam-slide";
import { ExamToolRender } from "@/components/lesson/exam/exam-tool";
import { useExamScroll } from "@/components/lesson/exam/use-exam-scroll";
import { useExamSection } from "@/components/lesson/exam/use-exam-section";
import { useSentenceSelection } from "@/components/lesson/exam/use-sentence-selected";
import { LessonCompleted } from "@/components/lesson/modules/lesson-completed";
import { GradientText } from "@/components/ui/gradient-text";
import { LessonSkeleton } from "@/components/ui/skeleton";
import { courseQueryOptions } from "@/services/courses";
import {
  lessonQueryOptions,
  lessonsQueryOptions,
  sectionsQueryOptions,
} from "@/services/lessons";
import { Lesson, SectionType } from "@/types/lessons";
import { cn } from "@/utils/cn";
import { seo } from "@/utils/seo";
import { useClickOutside } from "@/utils/use-click-outside";
import { useIsMobile } from "@/utils/use-mobile";

export const Route = createFileRoute(
  "/learn/$courseSlug/$lessonSlug/__layout-learning/",
)({
  // loader: async ({ params: { lessonSlug }, context }) => {
  //   const lesson = await context.queryClient.ensureQueryData(
  //     lessonQueryOptions(lessonSlug),
  //   );
  //   return { lesson };
  // },
  // head: ({ loaderData }) => {
  //   if (!loaderData) return { meta: undefined };
  //   const lesson = loaderData.lesson as Lesson;
  //   return {
  //     meta: seo({
  //       title: lesson.name,
  //       description: lesson?.description || "",
  //     }),
  //   };
  // },
  component: RouteComponent,
});

function RouteComponent() {
  const { lessonSlug, courseSlug } = Route.useParams();
  const { data: course } = useSuspenseQuery(courseQueryOptions(courseSlug));
  const { data: lesson } = useSuspenseQuery(lessonQueryOptions(lessonSlug));
  const { data: lessons } = useSuspenseQuery(lessonsQueryOptions(courseSlug));
  const { data: sections } = useQuery(sectionsQueryOptions(lesson.id));

  // Mode state: "scroll" for current behavior, "slide" for single section view
  const [mode, setMode] = useState<"scroll" | "slide">("slide");

  const isMobile = useIsMobile();
  const { selectedText, clickedSentence, resetClicked } =
    useSentenceSelection();
  const examToolWrapperRef = useRef<HTMLDivElement | null>(null);

  useClickOutside(
    examToolWrapperRef as RefObject<HTMLElement>,
    resetClicked,
    ".exam-tool-container",
  );

  const {
    mutationProgress,
    isRevise,
    navigateSection,
    currentSection,
    currentSectionIndex,
    countCompleted,
    visibleSections,
    navigateBackSection,
  } = useExamSection({
    course,
    lesson,
    lessons,
    sections: sections || [],
  });

  const handleSectionNavigation = useCallback(
    (newIndex: number) => {
      navigateSection(newIndex);
    },
    [navigateSection],
  );

  const { onContainerMounted } = useExamScroll({
    isRevise,
    visibleSections,
    disableAutoScroll: mode === "slide",
  });

  const gameSections = useMemo(
    () => sections?.filter((v) => v.type === SectionType.GAME) || [],
    [sections],
  );

  if (!sections || sections.length === 0) {
    return <LessonSkeleton />;
  }

  if (lesson.id === lessons.at(-1)?.id && currentSection === undefined) {
    return (
      <CourseRating
        points={lessons.reduce((sum, lesson) => sum + lesson.points, 0)}
        courseId={course.id}
      />
    );
  }

  if (currentSection === undefined) {
    return (
      <LessonCompleted
        course={course}
        lessons={lessons}
        lesson={lesson}
        sections={sections}
        countCompleted={countCompleted}
      />
    );
  }

  return (
    <div className="relative mx-auto flex h-full w-full flex-col">
      <div
        ref={onContainerMounted}
        className="scrollbar-hide mx-auto flex w-full max-w-4xl flex-1 flex-col items-center gap-4 overflow-auto px-6 py-10"
      >
        <GradientText className="bg-gradient-to-r from-[#21AA4B] to-[#9FAD00] font-normal text-2xl md:text-3xl">
          {lesson?.name}
        </GradientText>
        {visibleSections.map((section, index) => {
          const isSlideMode = mode === "slide";
          if (isSlideMode && index !== currentSectionIndex) {
            return null;
          }
          const handleNavigateBackSection = isSlideMode
            ? () => navigateBackSection(index - 1)
            : undefined;
          return (
            <div
              id={section.id}
              key={section.id}
              className={cn(
                "selectable-content w-full",
                section.type === SectionType.AIGENERATED && "h-full",
              )}
              data-section-id={section.id}
            >
              {section.type === SectionType.TEXT && (
                <ExamSlide
                  key={section.id}
                  section={section}
                  loading={mutationProgress.isPending}
                  navigateSection={handleSectionNavigation}
                  isGameNext={false}
                  sectionIndex={index}
                  currentSectionIndex={currentSectionIndex}
                  navigateBackSection={handleNavigateBackSection}
                />
              )}

              {section.type === SectionType.GAME && (
                <div className={cn(!isSlideMode && "my-30 h-full w-full")}>
                  <GameStore
                    navigateSection={handleSectionNavigation}
                    section={section}
                    gameSections={gameSections}
                    sectionIndex={sections.findIndex(
                      (v) => v.id === section.id,
                    )}
                    isRevise={isRevise}
                    navigateBackSection={handleNavigateBackSection}
                  />
                </div>
              )}

              {section.type === SectionType.CHALLENGE && (
                <div className={cn(!isSlideMode && "my-30 h-full w-full")}>
                  <QuizGame
                    section={section}
                    nextSection={() =>
                      handleSectionNavigation(currentSectionIndex + 1)
                    }
                    isRevise={isRevise}
                    backSection={handleNavigateBackSection}
                  />
                </div>
              )}

              {section.type === SectionType.AIGENERATED && (
                <div
                  className={cn(
                    !isSlideMode && "my-30 h-full w-full",
                    "h-full",
                  )}
                >
                  <JsxPreview
                    sectionContent={section.content}
                    sectionIndex={index}
                    navigateSection={handleSectionNavigation}
                    isGameNext={false}
                    navigateBackSection={handleNavigateBackSection}
                  />
                </div>
              )}
            </div>
          );
        })}

        {(selectedText?.rect || clickedSentence?.rect) && (
          <div ref={examToolWrapperRef}>
            <ExamToolRender
              isMobile={isMobile}
              rect={
                selectedText?.rect || clickedSentence?.rect || new DOMRect()
              }
            />
          </div>
        )}
      </div>
    </div>
  );
}
