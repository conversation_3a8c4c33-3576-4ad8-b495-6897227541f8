import type { QueryClient } from "@tanstack/react-query";
import {
  createRootRouteWithContext,
  HeadContent,
  Outlet,
  Scripts,
} from "@tanstack/react-router";
import type * as React from "react";
import aicademyLogo from "@/assets/images/aicademy-logo.png";
import { BadgePopup } from "@/components/badge-popup";
import { DefaultCatchBoundary } from "@/components/DefaultCatchBoundary";
import { NotFound } from "@/components/NotFound";
import { SIDEBAR_WIDTH, SIDEBAR_WIDTH_ICON } from "@/components/ui/sidebar";
import { Toaster } from "@/components/ui/sonner";
import { getLocale } from "@/paraglide/runtime";
import appCss from "@/styles/app.css?url";
import { seo } from "@/utils/seo";

export const Route = createRootRouteWithContext<{
  queryClient: QueryClient;
}>()({
  head: () => ({
    meta: [
      {
        charSet: "utf-8",
      },
      {
        name: "viewport",
        content: "width=device-width, initial-scale=1",
      },
      ...seo({
        title: "aicademy - Enhanced learning powered by AI",
        description:
          "Discover aicademy — the interactive learning platform where an AI Mentor supports you every step of the way.",
        keywords:
          "learn ai free, ai mentor, ai learning platform, gamified education, ai tools course, prompt engineering tutorial, interactive ai course, learn with ai, aicademy, ai course 2025, chatgpt course, ai for students",
        image: aicademyLogo,
      }),
    ],
    links: [
      { rel: "preconnect", href: "https://fonts.googleapis.com" },
      {
        rel: "preconnect",
        href: "https://fonts.gstatic.com",
        crossOrigin: "anonymous",
      },
      {
        rel: "stylesheet",
        href: "https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,100..700;1,100..700&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap",
      },
      { rel: "stylesheet", href: "https://fonts.cdnfonts.com/css/baloo" },
      { rel: "stylesheet", href: appCss },
      {
        rel: "icon",
        type: "image/png",
        sizes: "32x32",
        href: "/favicon-32x32.png",
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "16x16",
        href: "/favicon-16x16.png",
      },
      { rel: "manifest", href: "/site.webmanifest", color: "#fffff" },
      { rel: "icon", href: "/favicon.ico" },
    ],
  }),
  scripts() {
    return [
      {
        src: "https://accounts.google.com/gsi/client",
      },
    ];
  },
  errorComponent: (props) => {
    return (
      <RootDocument>
        <DefaultCatchBoundary {...props} />
      </RootDocument>
    );
  },
  notFoundComponent: () => <NotFound />,
  component: RootComponent,
});

function RootComponent() {
  return (
    <RootDocument>
      <Outlet />
    </RootDocument>
  );
}

function RootDocument({ children }: { children: React.ReactNode }) {
  const currentLocale = getLocale();
  const fontClass = currentLocale === "vi" ? "font-roboto" : "font-ibm";

  return (
    <html lang={currentLocale} suppressHydrationWarning>
      <head>
        <HeadContent />
      </head>
      <body
        className={`flex min-h-svh flex-col bg-(--layout-bg) ${fontClass} antialiased`}
        style={
          {
            "--sidebar-width": SIDEBAR_WIDTH,
            "--sidebar-width-icon": SIDEBAR_WIDTH_ICON,
          } as React.CSSProperties
        }
      >
        <BadgePopup />
        {children}
        {/*<TanStackRouterDevtools position="bottom-right" />*/}
        {/* <ReactQueryDevtools buttonPosition="bottom-right" /> */}
        <Scripts />
        <Toaster />
      </body>
    </html>
  );
}
