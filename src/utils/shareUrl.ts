import {
  FacebookIcon,
  InstaIcon,
  LinkCopyIcon,
  TelegramIcon,
  TwitterIcon,
} from "@/components/icons";

export enum SharePlatformName {
  Twitter = "Twitter",
  Telegram = "Telegram",
  Facebook = "Facebook",
  Link = "Link",
  Instagram = "Instagram",
}

export const SHARE_PLATFORMS = [
  { name: SharePlatformName.Twitter, Icon: TwitterIcon, isExternal: true },
  { name: SharePlatformName.Instagram, Icon: InstaIcon, isExternal: true },
  { name: SharePlatformName.Telegram, Icon: TelegramIcon, isExternal: true },
  { name: SharePlatformName.Facebook, Icon: FacebookIcon, isExternal: true },
  { name: SharePlatformName.Link, Icon: LinkCopyIcon, isExternal: false },
] as const;

export const createShareUrl = (
  platform: SharePlatformName,
  url: string,
  shareMessage?: string,
): string => {
  const encodedUrl = encodeURIComponent(url);
  const encodedMessage = shareMessage ? encodeURIComponent(shareMessage) : "";
  const fbAppId = import.meta.env.VITE_FB_APP_ID || "";

  switch (platform) {
    case SharePlatformName.Twitter:
      return `https://x.com/intent/tweet?url=${encodedUrl}${encodedMessage ? `&text=${encodedMessage}` : ""}`;
    case SharePlatformName.Telegram:
      return `https://t.me/share/url?url=${encodedUrl}${encodedMessage ? `&text=${encodedMessage}` : ""}`;
    case SharePlatformName.Facebook:
      return `https://www.facebook.com/share_channel/?type=reshare&link=${encodedUrl}&app_id=${fbAppId}&source_surface=external_reshare&display&hashtag=#aicademy#`;
    case SharePlatformName.Instagram:
      return `https://www.instagram.com/share?url=${encodedUrl}${encodedMessage ? `&text=${encodedMessage}` : ""}`;
    default:
      return url;
  }
};
