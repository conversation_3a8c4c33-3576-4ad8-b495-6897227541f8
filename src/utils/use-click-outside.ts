import { type RefObject, useEffect } from "react";

export function useClickOutside<T extends HTMLElement>(
  ref: RefObject<T>,
  callback: () => void,
  excludeSelector?: string,
) {
  useEffect(() => {
    function handler(event: MouseEvent | TouchEvent) {
      const target = event.target as HTMLElement;

      if (excludeSelector && target.closest(excludeSelector)) {
        return;
      }

      if (ref.current && !ref.current.contains(target)) {
        callback();
      }
    }

    document.addEventListener("mousedown", handler);
    document.addEventListener("touchstart", handler);
    return () => {
      document.removeEventListener("mousedown", handler);
      document.removeEventListener("touchstart", handler);
    };
  }, [ref, callback, excludeSelector]);
}
