import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { useMemo } from "react";
import { toast } from "sonner";
import alpaCrew from "@/assets/images/badges/alpha-crew.png";
import firstMileStone from "@/assets/images/badges/first-milestone.png";
import theInviter5 from "@/assets/images/badges/the-inviter-5.png";
import * as m from "@/paraglide/messages.js";
import { useProfile } from "@/services/auth";
import { userSeenBadge } from "@/services/badges";
import { atomPopupVisible } from "@/store/popup";
import { BadgeEnum, BadgesInfo } from "@/types/app";

// Create a hook to get badge list with translations
export const useBadgeList = (): BadgesInfo[] => {
  return useMemo(
    () => [
      {
        id: BadgeEnum.ALPHACREW,
        congratulation:
          m["profile.achievement_details.badges.alpha_crew.congratulation"](),
        title: m["profile.achievement_details.badges.alpha_crew.title"](),
        description:
          m["profile.achievement_details.badges.alpha_crew.description"](),
        img: alpa<PERSON><PERSON>,
      },
      {
        id: BadgeEnum.FIRSTMILESTONE,
        congratulation:
          m[
            "profile.achievement_details.badges.first_milestone.congratulation"
          ](),
        title: m["profile.achievement_details.badges.first_milestone.title"](),
        description:
          m["profile.achievement_details.badges.first_milestone.description"](),
        img: firstMileStone,
      },
      {
        id: BadgeEnum.THEINVITER5,
        congratulation:
          m["profile.achievement_details.badges.the_inviter.congratulation"](),
        title: m["profile.achievement_details.badges.the_inviter.title"](),
        description:
          m["profile.achievement_details.badges.the_inviter.description"](),
        img: theInviter5,
      },
    ],
    [],
  );
};

export const useBadgePopup = () => {
  const { data: userProfile } = useProfile({ stats: true });
  const unseenBadges = userProfile?.unseen_badges || [];
  const queryClient = useQueryClient();
  const currentBadgeId = unseenBadges[0] as BadgeEnum | undefined;
  const [isVisible, setIsVisible] = useAtom(atomPopupVisible);
  const BadgeList = useBadgeList();

  const badge = useMemo(() => {
    if (!currentBadgeId) return null;
    return BadgeList.find((badge) => badge.id === currentBadgeId);
  }, [currentBadgeId, BadgeList]);

  const { mutate: markBadgeAsSeen } = useMutation({
    mutationFn: userSeenBadge,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["profile"] });
    },
    onError: (error) => {
      toast.error("Failed to update badge status");
      console.error("Error marking badge as seen:", error);
    },
  });

  const showPopup = () => {
    if (!badge || unseenBadges.length === 0) return;
    setIsVisible(true);
  };

  const hidePopup = () => {
    if (currentBadgeId) {
      markBadgeAsSeen(currentBadgeId);
    }
    setIsVisible(false);
  };

  return {
    badge,
    isVisible,
    showPopup,
    hidePopup,
  };
};
