import { useInfiniteQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useInView } from "react-intersection-observer";
import { leaderboardQueryOptions } from "@/services/leaderboard";

interface UseInfiniteScrollOptions<T> {
  callbackFn: (page: number) => Promise<T[]>;
  limit?: number;
  queryKey?: string[];
}

export const useInfiniteScroll = <T>({
  callbackFn,
  limit,
  queryKey,
}: UseInfiniteScrollOptions<T>) => {
  const { ref, inView } = useInView();
  const { data, fetchNextPage, isFetching, refetch, isError, isPending } =
    useInfiniteQuery({
      queryKey: queryKey || leaderboardQueryOptions().queryKey,
      queryFn: ({ pageParam }) => callbackFn(pageParam),
      initialPageParam: 1,
      getNextPageParam: (lastPage, pages) => {
        if (limit && pages.length >= limit) {
          return undefined;
        }
        return (lastPage?.length || 0) > 0
          ? (pages?.length || 0) + 1
          : undefined;
      },
    });

  useEffect(() => {
    if (inView && !isFetching) {
      void fetchNextPage();
    }
  }, [inView, fetchNextPage, isFetching]);

  const flattenedData = data?.pages.flat() ?? [];

  return {
    ref,
    inView,
    data: flattenedData,
    isFetching,
    refetch,
    isError,
    isInitialLoading: isPending && !data,
  } as const;
};
