import { CopyIcon } from "lucide-react";
import { useState } from "react";
import * as m from "@/paraglide/messages.js";
import { cn } from "@/utils/cn";
import { CheckIcon } from "./icons";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";

interface ButtonCopyProps {
  content: string;
  tooltipText?: string;
  label?: string;
  className?: string;
  classNameIcon?: string;
}

export function ButtonCopy({
  content,
  tooltipText = m["ui.copy"](),
  className,
  classNameIcon = "size-4",
  label,
}: ButtonCopyProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content);
    setCopied(true);
    setTimeout(() => {
      setCopied(false);
    }, 1000);
  };

  return (
    <Tooltip open={copied}>
      <TooltipTrigger>
        <div
          onClick={() => handleCopy(content)}
          className={cn(
            "ml-1 flex cursor-pointer items-center justify-center rounded-sm p-2 hover:bg-(--background-second-foreground)/5 hover:shadow-sm",
            className,
          )}
        >
          {label && <span className="pr-2">{label}</span>}
          {copied ? (
            <CheckIcon
              className={cn("text-(--border-correct)", classNameIcon)}
            />
          ) : (
            <CopyIcon className={classNameIcon} />
          )}
        </div>
      </TooltipTrigger>
      <TooltipContent align="center" side="bottom">
        {copied ? m["ui.copied"]() : tooltipText}
      </TooltipContent>
    </Tooltip>
  );
}
