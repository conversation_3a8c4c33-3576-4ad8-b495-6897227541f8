import { useMutation } from "@tanstack/react-query";
import { type } from "arktype";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { useAtomValue } from "jotai";
import { MailIcon } from "lucide-react";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import * as m from "@/paraglide/messages.js";
import { loginWithEmail, loginWithEmailChallenge } from "@/services/auth";
import { atomAuthRef } from "@/store/auth";
import type { AuthOK } from "@/types/auth";
import { cn } from "@/utils/cn";
import { useAppForm } from "./form";
import { Button } from "./ui/button";

const formSchemaLogin = type({
  step: type.enumerated(1),
  email: "string.email",
}).or({
  step: type.enumerated(2),
  email: "string.email",
  otp: type("string").exactlyLength(6),
});

type FormSchemaLogin = typeof formSchemaLogin.infer;

export function FormLoginEmail({
  onSuccess,
}: {
  onSuccess?: (body: AuthOK) => void;
}) {
  const ref = useAtomValue(atomAuthRef);
  const loginEmailChallenge = useMutation({
    mutationFn: async (value: { email: string }) => {
      return loginWithEmailChallenge(value);
    },
  });

  const loginEmailVerify = useMutation({
    mutationFn: async (value: { email: string; otp: string; ref?: string }) => {
      return loginWithEmail(value);
    },
  });

  const form = useAppForm({
    defaultValues: {
      step: 1,
      email: "",
    } as FormSchemaLogin,
    validators: {
      onSubmit: formSchemaLogin,
    },
    onSubmit: async ({ formApi, value }) => {
      if (value.step === 1) {
        const data = await loginEmailChallenge.mutateAsync(value);
        formApi.setFieldValue("email", data); // email got normalized
        formApi.setFieldValue("step", 2);
        return;
      }

      const data = await loginEmailVerify.mutateAsync({
        ...value,
        ref,
      });
      onSuccess?.(data);
      formApi.reset();
    },
  });

  return (
    <form
      className="grid w-full gap-2"
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <form.Subscribe
        selector={(state) => state.values.step}
        children={(step) =>
          step === 1 ? (
            <form.AppField
              name="email"
              children={(field) => (
                <>
                  <div className="relative w-full">
                    <field.TextField
                      className={cn(
                        "w-full min-w-0 bg-background pl-8 shadow-none",
                      )}
                      placeholder={m["auth.email_placeholder"]()}
                    />
                    <MailIcon className="-translate-y-1/2 pointer-events-none absolute top-1/2 left-2 size-4 select-none opacity-50" />
                  </div>

                  <field.FieldMessage />
                </>
              )}
            />
          ) : (
            <>
              <form.AppField
                name="email"
                children={(field) => (
                  <>
                    <div className="relative w-full">
                      <field.TextField
                        className={cn(
                          "w-full min-w-0 bg-background pl-8 shadow-none",
                        )}
                        placeholder={m["auth.email_placeholder"]()}
                        readOnly
                      />
                      <MailIcon className="-translate-y-1/2 pointer-events-none absolute top-1/2 left-2 size-4 select-none opacity-50" />
                    </div>
                  </>
                )}
              />

              <form.AppField
                name="otp"
                children={(field) => (
                  <>
                    <InputOTP
                      maxLength={6}
                      pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
                      autoFocus
                      value={field.state.value}
                      onChange={(value) => field.handleChange(value)}
                    >
                      <InputOTPGroup>
                        <InputOTPSlot index={0} />
                        <InputOTPSlot index={1} />
                        <InputOTPSlot index={2} />
                        <InputOTPSlot index={3} />
                        <InputOTPSlot index={4} />
                        <InputOTPSlot index={5} />
                      </InputOTPGroup>
                    </InputOTP>

                    {field.state.meta.errors.map((error) => (
                      // TODO: this is a bug with tanstack form
                      // might be the same as this: https://github.com/TanStack/form/issues/1172
                      <p
                        key={(error as unknown as Error).message}
                        className={cn("text-destructive text-sm")}
                      >
                        {(error as unknown as Error).message}
                      </p>
                    ))}
                  </>
                )}
              />
            </>
          )
        }
      />

      <form.AppForm>
        <form.SubmitButton className="w-full text-sm">
          {m["common.actions.continue"]()}
        </form.SubmitButton>
        <form.Subscribe
          selector={(state) => state.values.step}
          children={(step) =>
            step === 2 && (
              <Button
                variant="link"
                className="h-auto p-0"
                onClick={(e) => {
                  e.preventDefault();
                  form.reset();
                }}
              >
                {m["common.actions.cancel"]()}
              </Button>
            )
          }
        />
      </form.AppForm>
    </form>
  );
}
