import { DropdownMenuArrow } from "@radix-ui/react-dropdown-menu";
import { Link, useNavigate } from "@tanstack/react-router";
import { useAtomValue } from "jotai";
import { MenuIcon, XIcon } from "lucide-react";
import { useCallback, useState } from "react";
import { FormLoginEmail } from "@/components/form-login-email";
import { LanguageToggle } from "@/components/LanguageToggle";
import { Logo } from "@/components/Logo";
import { NavUser } from "@/components/nav-user";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import * as m from "@/paraglide/messages";
import { renderGoogleSignIn, useProfile } from "@/services/auth";
import { atomAuth, atomAuthHint, authStore } from "@/store/auth";
import { logOut } from "@/store/auth";
import { User } from "@/types/auth";
import { cn } from "@/utils/cn";
import { Divider } from "./course/course-detail-items";
import { FacebookIcon, InstaIcon, TelegramIcon, TwitterIcon } from "./icons";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { UserAvatar } from "./user-avatar";

interface NavLink {
  to: string;
  label: string;
  auth?: boolean;
}

interface LoginMenuProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  variant?: "dropdown" | "dialog";
}

const getNavLinks = (): NavLink[] => [
  { to: "/dashboard", label: m["common.navigation.dashboard"]() },
  { to: "/courses", label: m["common.navigation.courses"]() },
  { to: "/my-learning", label: m["common.navigation.learning"](), auth: true },
];

export function LoginMenu({
  open,
  onOpenChange,
  variant = "dropdown",
  onSuccess,
}: LoginMenuProps) {
  const handleButtonRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (!node) {
        return;
      }

      if (variant === "dialog" || (variant === "dropdown" && open)) {
        renderGoogleSignIn(node);
        return authStore.sub(atomAuth, () => {
          if (atomAuth) {
            onOpenChange?.(false);
          }
        });
      }
    },
    [open, variant, onOpenChange],
  );

  const content = (
    <div className="flex flex-col items-center">
      <div ref={handleButtonRef} className="w-full text-center text-sm">
        {m["auth.login_with_google"]()}
      </div>
      <div className="inline-flex w-full items-center justify-center">
        <hr className="my-4 w-full" />
        <div className="-translate-x-1/2 absolute left-1/2 bg-card px-2 font-medium text-foreground/40 text-xs">
          {m["auth.or"]()}
        </div>
      </div>
      <FormLoginEmail
        onSuccess={() => {
          onSuccess?.();
          onOpenChange?.(false);
        }}
      />
    </div>
  );

  if (variant === "dialog") {
    return (
      <>
        <h2 className="text-center font-semibold text-lg">
          {m["auth.title"]()}
        </h2>
        {content}
      </>
    );
  }

  return (
    <>
      <div className="px-3 py-1.5 text-center font-medium">
        {m["auth.title"]()}
      </div>
      <DropdownMenuArrow className="fill-foreground/50" />
      {content}
    </>
  );
}

export function Navbar() {
  const profile = useProfile();
  const [loginOpen, setLoginOpen] = useState(false);
  const hinted = useAtomValue(atomAuthHint);

  return (
    <div className="flex w-full justify-between gap-2">
      <div className="flex w-full items-center justify-between gap-2">
        <div className="flex size-full items-center justify-start gap-4 overflow-hidden lg:gap-8">
          <Logo />
          <NavItems className="hidden h-full md:flex" hinted={!!hinted} />
        </div>

        <div className="flex h-full items-center gap-2">
          <LanguageToggle />
          <DropdownMenu open={loginOpen} onOpenChange={setLoginOpen}>
            <DropdownMenuTrigger asChild>
              {profile.data ? (
                <NavUser user={profile.data} />
              ) : (
                !profile.isPending && (
                  <Button
                    size="lg"
                    variant="outline"
                    className="rounded-full border-2 border-primary px-4 font-normal text-sm leading-6 lg:text-lg"
                  >
                    {m["landing.navbar.login"]()}
                  </Button>
                )
              )}
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={5}
              sideOffset={2}
              align="end"
              className="w-60 p-2"
            >
              <LoginMenu open={loginOpen} onOpenChange={setLoginOpen} />
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {profile.data && <MenuMobile user={profile.data} />}
    </div>
  );
}

export function NavItems({
  hinted,
  className,
  onNavigate,
}: {
  hinted: boolean;
  className?: string;
  onNavigate?: () => void;
}) {
  const navLinks = getNavLinks();
  const items = navLinks.filter((v) => {
    if (!v.auth) {
      return true;
    }

    return hinted;
  });

  return (
    <nav className={cn(className)}>
      {items.map(({ to, label }) => (
        <Link
          key={to}
          to={to}
          onClick={onNavigate}
          className={cn(
            "hover:!text-foreground inline-flex items-center text-foreground/60 transition-all hover:border-b-gray-300 md:px-3 lg:px-4 2xl:px-6",
            "border-transparent lg:border-b-[3px]",
            "text-base xl:text-lg",
          )}
          activeProps={{
            className: "border-b-primary !text-foreground",
          }}
        >
          {label}
        </Link>
      ))}
    </nav>
  );
}

export const MenuMobile = ({ user }: { user: User }) => {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();

  const handleNavigate = (to: string) => {
    setIsOpen(false);
    navigate({ to });
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <div className="flex cursor-pointer items-center justify-center md:hidden">
          <MenuIcon />
        </div>
      </SheetTrigger>
      <SheetContent
        side="right"
        className="flex h-full w-full flex-col overflow-hidden bg-white p-0 [&>button]:hidden"
      >
        <div className="flex w-full items-center px-4 pt-3 pb-6">
          <UserAvatar
            username={user.name}
            url={user.avatar_url}
            className="mr-3 size-12"
          />
          <div className="flex flex-1 flex-col">
            <span className="font-medium text-base">{user.name}</span>
            <span
              className="cursor-pointer text-muted-foreground text-xs underline"
              onClick={() => handleNavigate("/profile/me")}
            >
              {m["common.actions.view_account"]()}
            </span>
          </div>
          <XIcon
            className="size-6 cursor-pointer"
            onClick={() => setIsOpen(false)}
          />
        </div>

        <div className="flex w-full flex-1 flex-col justify-start px-4 sm:gap-8">
          <span
            className="cursor-pointer py-2 font-normal sm:text-2xl"
            onClick={() => handleNavigate("/dashboard")}
          >
            {m["common.navigation.dashboard"]()}
          </span>
          <span
            className="cursor-pointer py-2 font-normal sm:text-2xl"
            onClick={() => handleNavigate("/courses")}
          >
            {m["common.navigation.courses"]()}
          </span>
          <span
            className="cursor-pointer py-2 font-normal sm:text-2xl"
            onClick={() => handleNavigate("/my-learning")}
          >
            {m["common.navigation.learning"]()}
          </span>
        </div>

        <div className="mt-auto w-full">
          <div className="flex flex-col gap-6 px-4 pb-8">
            <Divider />
            <Link
              to="/help-center"
              className="text-sm sm:text-base"
              onClick={() => setIsOpen(false)}
            >
              {m["common.navigation.help_center"]()}
            </Link>
            <button
              type="button"
              className="text-left text-sm sm:text-base"
              onClick={() => {
                logOut();
                setIsOpen(false);
              }}
            >
              {m["common.actions.logout"]()}
            </button>
          </div>
          <div className="flex items-center justify-between border-t px-10 py-6">
            <a
              href="https://www.facebook.com/groups/1304801467284831"
              target="_blank"
              rel="noopener noreferrer"
            >
              <FacebookIcon className="size-6" />
            </a>
            <InstaIcon className="size-6" />
            <TelegramIcon className="size-6" />
            <TwitterIcon className="size-6" />
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
