import { Link } from "@tanstack/react-router";
import { useState } from "react";
import { LanguageToggle } from "@/components/LanguageToggle";
import { Logo } from "@/components/Logo";
import { NavUser } from "@/components/nav-user";
import { LoginMenu } from "@/components/navbar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import * as m from "@/paraglide/messages.js";
import { useProfile } from "@/services/auth";

export function LandingNavbar() {
  const profile = useProfile();
  const [loginOpen, setLoginOpen] = useState(false);

  return (
    <div className="flex w-full items-center justify-between gap-2">
      <Logo />

      <div className="flex h-full items-center gap-2">
        <DropdownMenu open={loginOpen} onOpenChange={setLoginOpen}>
          {profile.data && (
            <Link to="/dashboard">
              <Button
                size="lg"
                variant="outline"
                className="hidden rounded-full border-2 border-primary px-4 font-normal text-sm leading-6 sm:flex lg:text-lg"
              >
                {m["landing.navbar.start_learning"]()}
              </Button>
            </Link>
          )}
          <LanguageToggle />
          <DropdownMenuTrigger asChild>
            {profile.data ? (
              <NavUser user={profile.data} />
            ) : (
              !profile.isPending && (
                <Button
                  size="lg"
                  variant="outline"
                  className="rounded-full border-2 border-primary px-4 font-normal text-sm leading-6 lg:text-lg"
                >
                  {m["landing.navbar.login"]()}
                </Button>
              )
            )}
          </DropdownMenuTrigger>
          <DropdownMenuContent
            collisionPadding={5}
            sideOffset={2}
            align="end"
            className="w-60 p-2"
          >
            <LoginMenu open={loginOpen} onOpenChange={setLoginOpen} />
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
