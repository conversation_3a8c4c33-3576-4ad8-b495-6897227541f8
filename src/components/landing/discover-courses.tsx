import { Link } from "@tanstack/react-router";
import { useState } from "react";
import LandingDiscoverImg1 from "@/assets/images/landing/landing-discover1.png";
import CourseAuthor from "@/components/course/course-author";
import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import * as m from "@/paraglide/messages.js";
import { useCategories } from "@/services/categories";
import { useCourses } from "@/services/courses";
import { Category } from "@/types/app";
import { cn } from "@/utils/cn";

export const DiscoverCourses = () => {
  const { data: categories } = useCategories();
  const [currentCategory, setCurrentCategory] = useState<Category | null>(null);
  const { data: courses } = useCourses({ category_id: currentCategory?.id });

  return (
    <div className="flex min-h-[100dvh] flex-col items-center justify-center overflow-hidden bg-[linear-gradient(90deg,#D4F5DD_0%,#FFFFFF_100%)] px-4 py-10 sm:px-0 md:py-20">
      <div className="flex flex-col items-center justify-center gap-2 text-center">
        <div className="relative">
          <p className="font-baloo text-3xl md:text-5xl">
            {m["landing.discover_courses.heading"]()}
          </p>
          <img
            src={LandingDiscoverImg1}
            className="-top-1/2 lg:-right-56 xl:-right-72 absolute hidden w-56 lg:block"
            alt="landing discover"
          />
        </div>
        <p className="mt-2 text-lg">
          {m["landing.discover_courses.description"]()}
        </p>
      </div>

      <div className="mt-16 w-full">
        <Carousel className="w-full">
          <div className="mb-5 flex flex-col gap-4 px-0 sm:px-6 md:px-10 lg:px-32">
            <div className="flex w-full flex-col items-center justify-between gap-2 sm:flex-row">
              <div className="flex w-full items-center gap-2 overflow-x-auto pb-2">
                {categories?.map((category) => (
                  <Button
                    key={category.id}
                    variant="outline"
                    size="lg"
                    className={cn(
                      "flex-shrink-0 border border-(--background-second-foreground) bg-(--card)",
                      {
                        "bg-(--background-second-foreground) text-white hover:bg-(--background-second-foreground)/90":
                          currentCategory?.id === category.id,
                      },
                    )}
                    onClick={() => setCurrentCategory(category)}
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
              <div className="ml-2 hidden flex-shrink-0 items-center gap-2 sm:flex sm:gap-4">
                <CarouselPrevious className="translate-0 relative inset-0 bg-(--card) md:size-10" />
                <CarouselNext className="translate-0 relative inset-0 bg-(--card) md:size-10" />
              </div>
            </div>
          </div>
          <CarouselContent className="gap-4 pl-0 sm:pl-6 md:pl-10 lg:pl-32">
            {courses?.map((course) => (
              <CarouselItem
                key={course.id}
                className={cn(
                  "flex h-full flex-col items-center justify-center sm:basis-1/2 md:basis-1/2 lg:basis-1/3 lg:gap-10 xl:basis-1/4 2xl:basis-1/5",
                )}
              >
                <Link
                  to="/courses/$courseSlug"
                  params={{ courseSlug: course.slug }}
                  className="mx-auto flex size-full cursor-pointer flex-col items-center justify-center p-[3px]"
                >
                  <div className="mx-auto flex size-full cursor-pointer flex-col items-center justify-center md:items-start">
                    <div className="w-full max-w-[350px] md:max-w-[400px]">
                      <img
                        src={course.image_url}
                        alt={course.name}
                        className="aspect-[4/3] w-full rounded-[10px] border-[#D9E2FF] object-cover"
                      />
                    </div>
                    <CourseAuthor
                      course={course}
                      className="mt-5 bg-transparent px-0"
                    />
                    <div>
                      <p className="mt-6 text-center sm:text-left sm:text-xl">
                        {course.name}
                      </p>
                    </div>
                  </div>
                </Link>
              </CarouselItem>
            )) || <div className="min-h-80" />}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  );
};
