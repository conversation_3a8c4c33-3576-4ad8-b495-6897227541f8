import { Link } from "@tanstack/react-router";
import WayLearn1 from "@/assets/images/landing/way-to-learn.png";
import { ArrowLineRightIcon } from "@/components/icons";
import { ShadowButton } from "@/components/shadow-button";
import * as m from "@/paraglide/messages.js";

const socialLinks = [
  {
    url: "https://www.facebook.com/groups/1304801467284831",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="36"
        height="35"
        fill="none"
        viewBox="0 0 36 35"
      >
        <path
          fill="#fff"
          fillOpacity="0.01"
          d="M0 0h34.893v34.893H0z"
          style={{ mixBlendMode: "multiply" }}
          transform="translate(.28 .035)"
        ></path>
        <path
          fill="#161616"
          d="M29.36 4.396H6.091a1.46 1.46 0 0 0-1.45 1.45v23.27a1.46 1.46 0 0 0 1.45 1.45H18.62V20.447h-3.402V16.49h3.402v-2.911c0-3.38 2.06-5.223 5.092-5.223 1.014 0 2.028 0 3.042.152v3.522h-2.082c-1.647 0-1.963.785-1.963 1.93v2.52h3.925l-.512 3.957h-3.413v10.13h6.651a1.46 1.46 0 0 0 1.45-1.45V5.846a1.46 1.46 0 0 0-1.45-1.45"
        ></path>
      </svg>
    ),
  },
  {
    url: "",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="35"
        height="35"
        fill="none"
        viewBox="0 0 35 35"
      >
        <path
          fill="#fff"
          fillOpacity="0.01"
          d="M0 0h34.893v34.893H0z"
          style={{ mixBlendMode: "multiply" }}
          transform="translate(.04 .035)"
        ></path>
        <path
          fill="#161616"
          d="m19.922 15.478 9.533-11.082h-2.259l-8.277 9.622-6.611-9.622H4.683l9.997 14.55-9.997 11.62h2.259l8.74-10.16 6.983 10.16h7.625zm-3.094 3.596-1.013-1.449-8.06-11.528h3.47l6.505 9.304 1.013 1.448 8.454 12.094h-3.47z"
        ></path>
      </svg>
    ),
  },
  {
    url: "",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="36"
        height="35"
        fill="none"
        viewBox="0 0 36 35"
      >
        <path
          fill="#fff"
          fillOpacity="0.01"
          d="M0 0h34.893v34.893H0z"
          style={{ mixBlendMode: "multiply" }}
          transform="translate(.787 .035)"
        ></path>
        <path
          fill="#161616"
          d="M25.218 12.067a1.57 1.57 0 1 0 0-3.14 1.57 1.57 0 0 0 0 3.14M18.233 10.762a6.72 6.72 0 1 0 0 13.44 6.72 6.72 0 0 0 0-13.44m0 11.08a4.362 4.362 0 1 1 0-8.723 4.362 4.362 0 0 1 0 8.724"
        ></path>
        <path
          fill="#161616"
          d="M18.233 6.754c3.494 0 3.908.013 5.287.076.83.01 1.652.163 2.43.451a4.33 4.33 0 0 1 2.483 2.483c.288.778.44 1.6.45 2.43.064 1.38.077 1.794.077 5.287 0 3.494-.013 3.908-.076 5.288a7.2 7.2 0 0 1-.45 2.43 4.34 4.34 0 0 1-2.484 2.483c-.778.288-1.6.44-2.43.45-1.38.063-1.793.076-5.287.076s-3.908-.013-5.287-.076a7.2 7.2 0 0 1-2.43-.45 4.34 4.34 0 0 1-2.483-2.484 7.2 7.2 0 0 1-.45-2.43c-.064-1.379-.077-1.793-.077-5.287s.013-3.907.076-5.287c.01-.83.162-1.652.45-2.43a4.33 4.33 0 0 1 2.484-2.483c.778-.288 1.6-.44 2.43-.45 1.38-.064 1.793-.077 5.287-.077m0-2.358c-3.554 0-4 .016-5.395.08a9.6 9.6 0 0 0-3.176.607 6.7 6.7 0 0 0-3.827 3.828 9.6 9.6 0 0 0-.608 3.176c-.064 1.395-.079 1.84-.079 5.394s.015 4 .079 5.395a9.6 9.6 0 0 0 .608 3.176 6.7 6.7 0 0 0 3.827 3.828c1.017.38 2.091.586 3.177.608 1.395.063 1.84.078 5.394.078 3.553 0 4-.015 5.395-.079a9.6 9.6 0 0 0 3.176-.608 6.7 6.7 0 0 0 3.827-3.827c.381-1.017.587-2.091.608-3.177.064-1.395.079-1.84.079-5.394 0-3.553-.015-3.999-.079-5.395a9.6 9.6 0 0 0-.608-3.175 6.7 6.7 0 0 0-3.828-3.827 9.6 9.6 0 0 0-3.176-.608c-1.395-.064-1.84-.08-5.394-.08"
        ></path>
      </svg>
    ),
  },
  {
    url: "",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="35"
        height="35"
        fill="none"
        viewBox="0 0 35 35"
      >
        <path
          fill="#000"
          fillRule="evenodd"
          d="M19.817 10.9q-3.46 1.438-13.833 5.958-1.684.67-1.765 1.31c-.09.722.814 1.006 2.045 1.394q.25.078.519.165c1.21.393 2.84.854 3.687.872q1.152.025 2.572-.95 9.691-6.542 10.006-6.613c.148-.034.352-.*************.124.126.357.111.42-.09.381-3.638 3.68-5.474 5.388-.573.532-.979.91-1.062.996-.186.193-.375.376-.557.551-1.126 1.085-1.97 1.898.046 3.227.969.638 1.744 1.166 2.517 1.692a177 177 0 0 0 2.777 1.864c.278.181.543.37.801.555.983.7 1.866 1.33 2.957 1.23.634-.059 1.288-.655 1.62-2.433.787-4.2 2.331-13.303 2.688-17.053a4.2 4.2 0 0 0-.04-.934c-.031-.185-.097-.448-.337-.643-.284-.23-.723-.279-.92-.275-.892.015-2.26.491-8.848 3.231"
          clipRule="evenodd"
        ></path>
      </svg>
    ),
  },
];

export const DiscoverWayLearn = () => {
  return (
    <div className="flex min-h-screen flex-col justify-center bg-(--background) px-10 pt-20 pb-10 text-center md:px-32 md:pt-40">
      <h2 className="relative mb-2 inline font-baloo text-3xl md:text-5xl">
        {m["landing.discover_way_learn.heading"]()}
      </h2>
      <div className="mt-10 flex flex-col items-center justify-center text-center">
        <img
          src={WayLearn1}
          alt="way learn"
          className="w-[365px] translate-y-10"
        />
        <Link to="/courses" className="z-10">
          <ShadowButton
            size="xl"
            className="md:!px-20 !px-5 rounded-full sm:text-xl"
          >
            {m["landing.discover_way_learn.start_learning"]()}{" "}
            <ArrowLineRightIcon />
          </ShadowButton>
        </Link>
      </div>

      <div className="mt-20 flex flex-col">
        <div className="flex justify-center gap-5 sm:justify-end">
          {socialLinks.map((social, index) => (
            <Link
              key={index.toString()}
              to={social.url || "#"}
              target={social.url ? "_blank" : "_self"}
            >
              {social.icon}
            </Link>
          ))}
        </div>
        <div className="my-4 h-[1px] w-full bg-[#E6E8F0]" />
        <div className="flex flex-col items-center justify-between gap-3 sm:flex-row">
          <p className="text-sm">© 2025 aicademy Inc. All rights reserved</p>
          <div className="flex justify-end gap-6">
            <a href="/courses" className="text-sm hover:underline">
              Courses
            </a>
            <a href="/privacy" className="text-sm hover:underline">
              Privacy Policy
            </a>
            <a href="/#faq" className="text-sm hover:underline">
              FAQ
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};
