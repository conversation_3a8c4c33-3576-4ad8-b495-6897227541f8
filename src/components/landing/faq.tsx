import { Link } from "@tanstack/react-router";
import { ArrowLineRightIcon } from "@/components/icons";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import * as m from "@/paraglide/messages.js";
import { ShadowButton } from "../shadow-button";

export const Faq = () => {
  const faqItems = [
    {
      question: m["landing.faq.questions.q1"](),
      answer: m["landing.faq.questions.a1"](),
    },
    {
      question: m["landing.faq.questions.q2"](),
      answer: m["landing.faq.questions.a2"](),
    },
    {
      question: m["landing.faq.questions.q3"](),
      answer: m["landing.faq.questions.a3"](),
    },
    {
      question: m["landing.faq.questions.q4"](),
      answer: m["landing.faq.questions.a4"](),
    },
    {
      question: m["landing.faq.questions.q5"](),
      answer: m["landing.faq.questions.a5"](),
    },
  ];

  return (
    <div
      className="flex min-h-[100dvh] flex-col items-center justify-center gap-8 bg-[linear-gradient(90deg,#FFFFFF_0%,#FFFAEA_100%)] px-4 py-28"
      id="faq"
    >
      <h1 className="mb-5 text-center font-baloo text-3xl md:mb-8 md:text-5xl">
        {m["landing.faq.heading"]()}
      </h1>

      <Accordion type="single" collapsible className="w-full max-w-3xl">
        {faqItems.map((item, index) => (
          <AccordionItem
            key={`item-${index.toString()}`}
            value={`item-${index}`}
          >
            <AccordionTrigger className="font-medium md:text-xl">
              {index + 1}. {item.question}
            </AccordionTrigger>
            <AccordionContent className="text-base">
              {item.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>

      <Link to="/help-center">
        <ShadowButton
          size="xl"
          className="md:!px-20 !px-5 rounded-full sm:text-xl"
        >
          {m["landing.faq.see_more"]()} <ArrowLineRightIcon />
        </ShadowButton>
      </Link>
    </div>
  );
};
