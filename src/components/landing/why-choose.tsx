import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import WhyChooseBig1 from "@/assets/images/landing/why-choose-big-1.png";
import WhyChooseBig2 from "@/assets/images/landing/why-choose-big-2.png";
import WhyChooseBig3 from "@/assets/images/landing/why-choose-big-3.png";
import WhyChoose1 from "@/assets/images/landing/why-choose1.png";
import WhyChoose2 from "@/assets/images/landing/why-choose2.png";
import WhyChoose3 from "@/assets/images/landing/why-choose3.png";
import { WhyChooseArrowIcon } from "@/components/icons";
import * as m from "@/paraglide/messages.js";
import { cn } from "@/utils/cn";
import { TABLET_BREAKPOINT, useIsMobile } from "@/utils/use-mobile";

export const WhyChoose = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const isMobile = useIsMobile(TABLET_BREAKPOINT);

  const reasons = [
    {
      content: {
        img: WhyChoose1,
        title: m["landing.why_choose.reason1.title"](),
        description: m["landing.why_choose.reason1.description"](),
      },
      image: WhyChooseBig1,
      backgroundColor: "", // #FFF4E9
    },
    {
      content: {
        img: WhyChoose2,
        title: m["landing.why_choose.reason2.title"](),
        description: m["landing.why_choose.reason2.description"](),
      },
      image: WhyChooseBig2,
      backgroundColor: "", // #FEF9E9
    },
    {
      content: {
        img: WhyChoose3,
        title: m["landing.why_choose.reason3.title"](),
        description: m["landing.why_choose.reason3.description"](),
      },
      image: WhyChooseBig3,
      backgroundColor: "", // #FBFDEA
    },
  ];

  useEffect(() => {
    if (isMobile) return;

    const options = {
      root: null,
      rootMargin: "-20% 0px",
      threshold: [0.3, 0.5, 0.7],
    };

    const observer = new IntersectionObserver((entries) => {
      let maxVisibility = 0;
      let mostVisibleIndex = activeIndex;

      entries.forEach((entry) => {
        if (entry.intersectionRatio > maxVisibility) {
          maxVisibility = entry.intersectionRatio;
          mostVisibleIndex = sectionRefs.current.findIndex(
            (ref) => ref === entry.target,
          );
        }
      });

      if (maxVisibility > 0 && mostVisibleIndex !== activeIndex) {
        setActiveIndex(mostVisibleIndex);
      }
    }, options);

    sectionRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    const handleScroll = () => {
      if (window.scrollY < 100) {
        setActiveIndex(0);
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      sectionRefs.current.forEach((ref) => {
        if (ref) observer.unobserve(ref);
      });
      window.removeEventListener("scroll", handleScroll);
    };
  }, [activeIndex, isMobile]);

  const imageVariants = {
    enter: {
      opacity: 0,
      y: 30,
      scale: 0.95,
      zIndex: 2,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      zIndex: 2,
      transition: {
        duration: 0.5,
      },
    },
    exit: {
      opacity: 0,
      y: -30,
      scale: 0.95,
      zIndex: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const containerVariants = {
    visible: {
      backgroundColor: reasons[activeIndex].backgroundColor,
      transition: isMobile ? { duration: 0 } : { duration: 0.5 },
    },
  };

  return (
    <div className="px-4 py-10 sm:px-0">
      <div className="mb-5 pt-10 text-center md:mb-12 md:pt-20">
        <h2 className="relative mb-2 inline font-baloo text-3xl md:text-5xl">
          {m["landing.why_choose.heading"]()}
          <div className="-translate-y-1/2 -right-28 absolute top-[160%] hidden sm:block">
            <WhyChooseArrowIcon />
          </div>
        </h2>
        <p className="mt-2">{m["landing.why_choose.description"]()}</p>
      </div>
      <motion.div
        className="grid grid-cols-1 gap-5 gap-y-20 px-0 sm:px-8 md:px-16 md:pt-12 lg:grid-cols-[40%_60%] lg:gap-28 lg:px-32"
        ref={containerRef}
        variants={containerVariants}
        animate="visible"
      >
        {isMobile ? (
          <div className="col-span-1 flex flex-col gap-3 text-center sm:gap-14">
            {reasons.map((reason, index) => (
              <div
                key={index.toString()}
                ref={(el) => {
                  sectionRefs.current[index] = el;
                }}
                className="flex min-h-[40vh] flex-col gap-4 py-8"
              >
                <img
                  src={reason.image}
                  className="w-full object-contain"
                  alt={reason.content.title}
                  loading="lazy"
                />
                <div className="flex flex-col items-center gap-2">
                  <img
                    src={reason.content.img}
                    className="w-full max-w-[12rem]"
                    alt={reason.content.title}
                    draggable={false}
                  />
                  <h3 className="font-baloo text-xl">{reason.content.title}</h3>
                  <p className="text-base">{reason.content.description}</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <>
            <div className="col-span-1">
              <div className="flex flex-col gap-6 md:gap-10">
                {reasons.map((reason, index) => (
                  <div
                    key={index.toString()}
                    ref={(el) => {
                      sectionRefs.current[index] = el;
                    }}
                    className={cn(
                      "flex min-h-[40vh] gap-6 py-10 md:gap-10 md:py-20 xl:py-40",
                    )}
                  >
                    <div className="flex flex-col gap-2">
                      <img
                        src={reason.content.img}
                        className="w-full max-w-[200px] object-cover sm:max-w-[280px]"
                        alt={reason.content.title}
                        draggable={false}
                      />
                      <h3 className="mt-6 font-baloo text-2xl sm:text-4xl">
                        {reason.content.title}
                      </h3>
                      <p className="mt-2 sm:text-xl">
                        {reason.content.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="col-span-1 max-w-[48dvw]">
              <div className="sticky top-[15dvh] mt-10 h-max w-full overflow-hidden p-4 sm:h-[50dvh] md:min-h-[70dvh] md:p-6">
                <AnimatePresence initial={false}>
                  <motion.img
                    key={activeIndex}
                    src={reasons[activeIndex].image}
                    alt={reasons[activeIndex].content.title}
                    className="absolute inset-0 h-auto max-w-full rounded object-contain"
                    variants={imageVariants}
                    initial="enter"
                    animate="visible"
                    exit="exit"
                    loading="lazy"
                    draggable={false}
                    rel="preload"
                  />
                </AnimatePresence>
              </div>
            </div>
          </>
        )}
      </motion.div>
    </div>
  );
};
