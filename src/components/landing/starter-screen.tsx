import { Link } from "@tanstack/react-router";
import { motion } from "framer-motion";
import ReasonChoose1 from "@/assets/images/landing/reason-choose-1.png";
import ReasonChoose2 from "@/assets/images/landing/reason-choose-2.png";
import StarterScreenImage from "@/assets/images/landing/starter-screen.png";
import StarterScreenImageMobile from "@/assets/images/landing/starter-screen-mobile.png";
import { ArrowLineRightIcon } from "@/components/icons";
import { ShadowButton } from "@/components/shadow-button";
import * as m from "@/paraglide/messages.js";

export const StarterScreen = () => {
  return (
    <div className="flex w-full flex-col items-center justify-center gap-10 overflow-hidden bg-[linear-gradient(90deg,#FFFFFF_0%,#FFFAEA_100%)] px-5 py-10 md:py-20">
      <div className="flex flex-col items-center justify-center gap-5">
        <div className="text-center font-baloo">
          <p className="text-6xl md:text-[80px]">
            {m["landing.starter_screen.main_heading"]()}
          </p>
          <p
            style={{
              textShadow:
                "1px 1px 0 #EA9B00, 2px 2px 0 #EA9B00, 2px 2px 0 #EA9B00, 2px 2px 0 #EA9B00, 2px 2px 0 #EA9B00",
            }}
            className="inline text-5xl text-[#FFB800] md:text-[70px]"
          >
            {m["landing.starter_screen.subheading"]()}{" "}
          </p>
        </div>
        <div className="flex flex-col items-center justify-center gap-3">
          <p className="text-center text-xl sm:max-w-2/3">
            {m["landing.starter_screen.description"]()}
          </p>
          <Link to="/courses" className="z-40 mt-3">
            <ShadowButton
              size="xl"
              className="sm:!px-20 rounded-full px-14 sm:text-xl"
            >
              {m["landing.starter_screen.cta_button"]()} <ArrowLineRightIcon />
            </ShadowButton>
          </Link>
        </div>
      </div>

      <img
        src={StarterScreenImage}
        alt="starter screen"
        className="md:-translate-y-20 sm:-translate-y-10 -translate-y-4 xl:-translate-y-20 hidden w-full sm:block sm:max-w-[80dvw] sm:translate-x-5 md:translate-x-10"
        draggable={false}
      />
      <img
        src={StarterScreenImageMobile}
        alt="starter screen"
        className="block w-full sm:hidden sm:max-w-[80dvw]"
        draggable={false}
      />

      <div className="md:-translate-y-10 xl:-translate-y-12 relative my-4 flex w-screen items-center justify-center px-5 sm:max-w-[90dvw] sm:px-0 xl:max-w-[70dvw]">
        <motion.img
          initial={{ translateX: 86 }}
          whileInView={{ translateX: 30 }}
          transition={{ delay: 0.2, ease: "easeInOut", duration: 0.5 }}
          className="mb-14 hidden w-30 sm:block"
          src={ReasonChoose2}
          alt="reason choose 2"
        />
        <div className="z-50 w-full rounded-[10px] border-[2px] border-black bg-[#FFD84B] px-3 py-9 text-center shadow-[0px_4px_0px_0px_#000000] md:px-14 xl:px-32">
          <p className="mb-2 font-baloo text-2xl md:text-4xl">
            {m["landing.starter_screen.section_title"]()}
          </p>
          <p className="text-center text-lg">
            {m["landing.starter_screen.section_description"]()}
          </p>
        </div>
        <motion.img
          initial={{ translateX: -120 }}
          whileInView={{ translateX: -50 }}
          transition={{ delay: 0.2, ease: "easeInOut", duration: 0.5 }}
          className="mt-20 hidden w-40 align-bottom sm:block"
          src={ReasonChoose1}
          alt="reason choose 1"
        />
      </div>
    </div>
  );
};
