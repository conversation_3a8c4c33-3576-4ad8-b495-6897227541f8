import { Loader2 } from "lucide-react";
import { PropsWithChildren, useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  <PERSON>alog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import * as m from "@/paraglide/messages.js";

// Options will be created inside component to use translations

interface ChatDislikeDialogProps extends PropsWithChildren {
  handleFeedback: (comment: string) => Promise<void> | void;
}
export const ChatDislikeDialog = ({
  handleFeedback,
  children,
}: ChatDislikeDialogProps) => {
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [comment, setComment] = useState("");
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const options = [
    m["chatbot.feedback.options.incorrect_information"](),
    m["chatbot.feedback.options.instruction_ignored"](),
    m["chatbot.feedback.options.being_lazy"](),
    m["chatbot.feedback.options.dont_like_style"](),
    m["chatbot.feedback.options.bad_recommendation"](),
    m["chatbot.feedback.options.other"](),
  ];

  const toggleOption = (option: string) => {
    setSelectedOptions((prev) =>
      prev.includes(option)
        ? prev.filter((item) => item !== option)
        : [...prev, option],
    );
  };

  const handleSubmit = async () => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    try {
      const feedback =
        selectedOptions.length > 0
          ? `${selectedOptions.join(", ")}${comment ? `: ${comment}` : ""}`
          : comment;
      await handleFeedback(feedback);
      setSelectedOptions([]);
      setComment("");
      toast.success(m["ui.feedback_submitted"]());
    } catch (error) {
      console.error("Failed to submit feedback:", error);
    } finally {
      setIsSubmitting(false);
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        aria-describedby={undefined}
        className="gap-5 bg-(--card) p-4 sm:max-w-xl sm:p-8"
        onKeyDown={(e) => {
          if (e.key === "Escape") {
            setOpen(false);
          }
          if (e.key === "Enter") {
            if (
              isSubmitting ||
              (selectedOptions.length === 0 && !comment.trim())
            ) {
              return;
            }
            handleSubmit();
          }
        }}
      >
        <DialogTitle className="sr-only">
          {m["chatbot.feedback.title"]()}
        </DialogTitle>
        <div className="flex flex-col gap-y-5">
          <div className="flex flex-col gap-1">
            <p className="font-semibold text-xl sm:text-2xl">
              {m["chatbot.feedback.title"]()}
            </p>
            <p className="text-sm sm:text-base">
              {m["chatbot.feedback.description"]()}
            </p>
          </div>
          <div className="grid grid-cols-2 gap-2 sm:gap-3">
            {options.map((option) => {
              const checkboxId = `checkbox-${option.replace(/\s+/g, "-").toLowerCase()}`;
              return (
                <label
                  key={option}
                  htmlFor={checkboxId}
                  className="flex w-full cursor-pointer items-center justify-between gap-2 rounded-lg border px-3 py-2 font-medium text-sm shadow-xs transition-shadow hover:shadow-md sm:text-base"
                >
                  <p>{option}</p>
                  <Checkbox
                    id={checkboxId}
                    checked={selectedOptions.includes(option)}
                    onCheckedChange={() => toggleOption(option)}
                  />
                </label>
              );
            })}
          </div>
          <div>
            <label htmlFor="feedback-textarea" className="text-sm sm:text-base">
              {m["chatbot.feedback.how_to_improve"]()}{" "}
              <span className="text-sm opacity-60">
                {m["chatbot.feedback.optional"]()}
              </span>
            </label>
            <Textarea
              className="mt-1 text-sm placeholder:text-sm sm:text-base sm:placeholder:text-base"
              id="feedback-textarea"
              placeholder={m["chatbot.feedback.placeholder"]()}
              value={comment}
              onChange={(e) => setComment(e.target.value)}
            />
          </div>
          <div className="flex justify-end gap-2 pt-2">
            <Button
              variant="ghost"
              className="rounded-full"
              onClick={() => setOpen(false)}
              disabled={isSubmitting}
            >
              {m["common.actions.cancel"]()}
            </Button>
            <Button
              className="rounded-full"
              onClick={handleSubmit}
              disabled={
                isSubmitting ||
                (selectedOptions.length === 0 && !comment.trim())
              }
            >
              {isSubmitting
                ? m["chatbot.feedback.submitting"]()
                : m["chatbot.feedback.submit_feedback"]()}
              {isSubmitting && (
                <Loader2 className="ml-2 h-4 w-4 animate-spin" />
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
