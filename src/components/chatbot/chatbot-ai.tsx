import { motion } from "framer-motion";
import Markdown from "react-markdown";
import avatar from "@/assets/images/owls/owl-chat-avatar.png";
import { ChatMessage, ChatStreamingStatus } from "@/types/chat";
import { cn } from "@/utils/cn";
import "@/styles/markdown.css";
import { ChatbotFeedbackAction } from "@/components/chatbot/chatbot-feedback-action";
import { SequentialTyping } from "@/components/chatbot/typing-text";
import { ChatIndicator } from "@/routes/learn/$courseSlug/$lessonSlug/__layout-learning";
import { PropsWithClassName } from "@/types/app";

interface ChatbotAiProps extends PropsWithClassName {
  message: ChatMessage;
  lessonId: string;
  onTypingComplete?: () => void;
  scrollToBottom?: () => void;
}

export const ChatbotAi = ({
  message,
  lessonId,
  onTypingComplete,
  scrollToBottom,
  className,
}: ChatbotAiProps) => {
  if (message.streamStatus === ChatStreamingStatus.PROCESSING) {
    return (
      <div className={className}>
        <ChatIndicator />
      </div>
    );
  }

  return (
    <motion.div
      className={cn("flex w-[90%] flex-col gap-y-1 text-base", className)}
    >
      <div className="mb-1">
        <img src={avatar} alt="aicademy" className="h-5 w-auto" />
      </div>
      {message.streamStatus ? (
        <SequentialTyping
          key={message.id}
          chunks={message.streamChunks || []}
          speed={12}
          onComplete={onTypingComplete}
          streamComplete={!!message.content}
          scrollToBottom={scrollToBottom}
          isPaused={message.streamStatus === ChatStreamingStatus.PAUSED}
        />
      ) : message.type === "markdown" ? (
        <div className="chat-md text-sm">
          <Markdown>{message.content}</Markdown>
        </div>
      ) : (
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.2 }}
          className="whitespace-pre-wrap text-sm"
        >
          {message.content}
        </motion.p>
      )}
      {!message.streamStatus && (
        <ChatbotFeedbackAction message={message} lessonId={lessonId} />
      )}
    </motion.div>
  );
};
