import { InfiniteData, useQueryClient } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  chatProfileQueryOptions,
  messagesQueryOptions,
  SendMessageInput,
} from "@/services/conversations";
import { atomAuth } from "@/store/auth";
import { UUID } from "@/types/app";
import {
  ChatDoneResponse,
  ChatLanguage,
  ChatMessage,
  ChatProcessingResponse,
  ChatResponse,
  ChatStreamErrorResponse,
  ChatStreamingStatus,
  ChatStreamResponse,
} from "@/types/chat";
import { WEBSOCKET_URL } from "@/utils/api";

export enum ConnectionStatus {
  CONNECTING = "connecting",
  CONNECTED = "connected",
  ERROR = "error",
}

interface WebSocketChatContextType {
  connectionStatus: ConnectionStatus;
  sendMessage: (message: SendMessageInput) => boolean;
  isConnected: boolean;
  hasError: boolean;
  onTypingChatSuccess: (isPaused?: boolean) => void;
  isStreaming: boolean;
}

const WebSocketChatContext = createContext<
  WebSocketChatContextType | undefined
>(undefined);

interface WebSocketChatProviderProps {
  children: ReactNode;
  conversationId: UUID | undefined;
}

export const WebSocketChatProvider = ({
  children,
  conversationId,
}: WebSocketChatProviderProps) => {
  const wsRef = useRef<WebSocket | null>(null);
  const token = useAtomValue(atomAuth)?.jwt;
  const queryClient = useQueryClient();
  const [isStreaming, setIsStreaming] = useState(false);
  const pausedRef = useRef(false);

  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(
    ConnectionStatus.CONNECTING,
  );

  const qo = messagesQueryOptions({
    conversationID: conversationId,
    limit: 10,
  });

  const handleUpdateChatQuery = (data: ChatResponse) => {
    queryClient.setQueryData(qo.queryKey, (old) => {
      if (!old) {
        return old;
      }

      if (!("type" in data)) {
        return handleStreamCompletion(old, data);
      }

      switch (data.type) {
        case ChatStreamingStatus.PROCESSING:
          return handleProcessingState(old, data);
        case ChatStreamingStatus.STREAM_CHUNK:
          return handleStreamChunk(old, data);
        case ChatStreamingStatus.STREAM_ERROR:
          return handleStreamError(old, data);
        default:
          return old;
      }
    });
  };

  const handleStreamCompletion = (
    old: InfiniteData<ChatMessage[], unknown>,
    data: ChatDoneResponse,
  ) => {
    const response = data?.data;
    if (!response?.output) return old;

    updateLanguagePreference(response.preferences?.language);

    const lastChatIndex = old.pages[0].length - 1;
    const aiChat = old.pages[0][lastChatIndex];

    old.pages[0][lastChatIndex] = {
      ...(aiChat || {}),
      ...response.output,
    };

    return old;
  };

  const handleProcessingState = (
    old: InfiniteData<ChatMessage[], unknown>,
    data: ChatProcessingResponse,
  ) => {
    const lastChatIndex = old.pages[0].length - 1;
    const userChat = old.pages[0][lastChatIndex];

    old.pages[0][lastChatIndex] = {
      ...(userChat || {}),
      id: data.data.input_id,
      isMock: false,
    };

    old.pages[0].push({
      id: crypto.randomUUID(),
      content: "",
      type: "markdown",
      role: "assistant",
      timestamp: new Date().toISOString(),
      streamStatus: ChatStreamingStatus.PROCESSING,
    });

    return old;
  };

  const handleStreamChunk = (
    old: InfiniteData<ChatMessage[], unknown>,
    data: ChatStreamResponse,
  ) => {
    const lastChatIndex = old.pages[0].length - 1;
    const aiChat = old.pages[0][lastChatIndex];

    if (aiChat.streamStatus === ChatStreamingStatus.PAUSED) {
      return old;
    }

    old.pages[0][lastChatIndex] = {
      ...(aiChat || {}),
      id: data.data.output_id,
      streamChunks: [...(aiChat?.streamChunks || []), data.data.chunk],
      streamStatus: ChatStreamingStatus.STREAM_CHUNK,
    };

    return old;
  };

  const handleStreamError = (
    old: InfiniteData<ChatMessage[], unknown>,
    data: ChatStreamErrorResponse,
  ) => {
    const lastChatIndex = old.pages[0].length - 1;
    const aiChat = old.pages[0][lastChatIndex];

    old.pages[0][lastChatIndex] = {
      ...(aiChat || {}),
      id: data.data.output_id,
      content: data.data.content,
      streamStatus: null,
    };

    return old;
  };

  const updateLanguagePreference = (language?: keyof typeof ChatLanguage) => {
    if (!language) return;

    const key = chatProfileQueryOptions().queryKey;

    queryClient.setQueryData(key, (old) => {
      if (!old) {
        return old;
      }

      return {
        ...old,
        preferences: {
          ...old.preferences,
          language,
        },
      };
    });

    queryClient.invalidateQueries({ queryKey: key });
  };

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN || !conversationId) return;

    setConnectionStatus(ConnectionStatus.CONNECTING);
    const url = `${WEBSOCKET_URL}/api/v1/conversations/ws/${conversationId}/messages?token=${token}`;
    wsRef.current = new WebSocket(url);

    wsRef.current.onopen = () => {
      setConnectionStatus(ConnectionStatus.CONNECTED);
    };

    wsRef.current.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (pausedRef.current) return;
        if ("type" in data) {
          setIsStreaming(data.type === ChatStreamingStatus.STREAM_CHUNK);
        }
        handleUpdateChatQuery(data);
      } catch (error) {
        console.error("Failed to parse WebSocket message:", error);
      }
    };

    wsRef.current.onerror = (error) => {
      console.error("WebSocket error:", error);
      setConnectionStatus(ConnectionStatus.ERROR);
    };

    wsRef.current.onclose = () => {
      setConnectionStatus(ConnectionStatus.ERROR);
    };
  }, [conversationId, token]);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close(1000, "User disconnected");
      wsRef.current = null;
    }
  }, []);

  const sendMessageQueryUpdate = async (variables: SendMessageInput) => {
    await queryClient.cancelQueries({ queryKey: qo.queryKey });

    queryClient.setQueryData(qo.queryKey, (old) => {
      if (!old) return old;

      const userMock: ChatMessage = {
        id: crypto.randomUUID(),
        content: variables.input,
        type: "text",
        role: "user",
        timestamp: new Date().toISOString(),
        isMock: true,
      };

      if ("quote" in variables) userMock.quote = variables.quote;

      if (!old.pages.length) {
        old.pages = [[userMock]];
        return old;
      }

      old.pages[0] = [...old.pages[0], userMock];
      return old;
    });
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: sendMessageQueryUpdate
  const sendMessage = useCallback(
    (message: SendMessageInput) => {
      if (wsRef.current?.readyState !== WebSocket.OPEN) {
        console.warn("WebSocket is not connected");
        return false;
      }
      if (pausedRef.current) {
        pausedRef.current = false;
        setIsStreaming(false);
      }
      wsRef.current.send(JSON.stringify(message));
      sendMessageQueryUpdate(message);
      return true;
    },
    [conversationId],
  );

  useEffect(() => {
    connect();
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  const onTypingChatSuccess = async (isPaused: boolean = false) => {
    pausedRef.current = isPaused;
    setIsStreaming(false);
    await queryClient.cancelQueries({ queryKey: qo.queryKey });

    queryClient.setQueryData(qo.queryKey, (old) => {
      if (!old) {
        return old;
      }
      const lastChatIndex = old.pages[0].length - 1;
      const aiChat = old.pages[0][lastChatIndex];
      old.pages[0][lastChatIndex] = {
        ...aiChat,
        streamStatus: isPaused ? ChatStreamingStatus.PAUSED : null,
      };
      return old;
    });
  };

  const value: WebSocketChatContextType = {
    connectionStatus,
    sendMessage,
    isConnected: connectionStatus === ConnectionStatus.CONNECTED,
    hasError: connectionStatus === ConnectionStatus.ERROR,
    onTypingChatSuccess,
    isStreaming,
  };

  return (
    <WebSocketChatContext.Provider value={value}>
      {children}
    </WebSocketChatContext.Provider>
  );
};

export const useWebSocketChat = (): WebSocketChatContextType => {
  const context = useContext(WebSocketChatContext);
  if (context === undefined) {
    throw new Error(
      "useWebSocketChat must be used within a WebSocketChatProvider",
    );
  }
  return context;
};
