import { motion } from "framer-motion";
import * as m from "@/paraglide/messages.js";
import { SendMessageInput } from "@/services/conversations";
import { UUID } from "@/types/app";
import { ChatbotAction } from "@/types/chat";
import { cn } from "@/utils/cn";

interface FaqSuggestionsDropdownProps {
  faqs: Array<{ id: string; question: string }> | undefined;
  isLoading: boolean;
  isOpen: boolean;
  full?: boolean;
  onSubmit: (msg: SendMessageInput) => void;
}

export const FaqSuggestionsDropdown = ({
  faqs,
  isLoading,
  isOpen,
  full,
  onSubmit,
}: FaqSuggestionsDropdownProps) => {
  const handleFaqClick = (faq: { id: string; question: string }) => {
    onSubmit({
      input: faq.question,
      type: ChatbotAction.CHAT,
      hints: {
        faq_id: faq.id as UUID,
      },
    });
  };

  return (
    <motion.div
      className={cn(
        "-translate-y-[100%] absolute w-full rounded-t-lg bg-gradient-to-b from-transparent to-(--course-chat-bg)",
        !full && "!hidden",
        isOpen
          ? "-top-2 right-2 h-auto via-(--course-chat-bg) via-40% pt-8 pl-2"
          : "right-0 h-8",
      )}
      initial={{ opacity: 0, y: -10 }}
      animate={{
        opacity: isOpen ? 1 : 0,
        y: isOpen ? 0 : -10,
      }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <motion.div
        className={cn(
          "flex-col items-end gap-2 text-right text-sm",
          isOpen ? "flex" : "hidden",
        )}
      >
        {isLoading
          ? m["chatbot.loading.loading"]()
          : faqs?.map((faq, index) => (
              <motion.div
                className="flex cursor-pointer justify-end gap-2 rounded-lg border bg-card/50 px-3 py-2 shadow-xs backdrop-blur-sm hover:text-green-600 hover:shadow-md"
                key={faq.id}
                onClick={() => handleFaqClick(faq)}
                initial={{ opacity: 0, x: 10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
              >
                {faq.question}
              </motion.div>
            ))}
      </motion.div>
    </motion.div>
  );
};
