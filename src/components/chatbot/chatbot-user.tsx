import { AnimatePresence, motion } from "framer-motion";
import { ChevronDown } from "lucide-react";
import { useState } from "react";
import { PropsWithClassName } from "@/types/app";
import { ChatMessage } from "@/types/chat";
import { cn } from "@/utils/cn";

interface ChatbotUserProps extends PropsWithClassName {
  message: ChatMessage;
}

export const ChatbotUser = ({ message, className }: ChatbotUserProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [shouldShowExpand, setShouldShowExpand] = useState(false);

  const measureContent = (el: HTMLDivElement | null) => {
    if (!el) return;

    const measure = () => {
      const computedStyle = getComputedStyle(el);
      const lineHeight = parseFloat(computedStyle.lineHeight) || 24;
      const height = el.scrollHeight;
      const lines = Math.ceil(height / lineHeight);

      setShouldShowExpand(lines > 3);
    };

    measure();
  };

  const toggleExpand = () => {
    setIsExpanded((prev) => !prev);
  };

  return (
    <AnimatePresence>
      <motion.div
        className={cn(
          "flex w-full flex-col items-end justify-end text-sm",
          className,
        )}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 20,
        }}
      >
        {message.quote && (
          <div>
            <blockquote className="rounded-[20px] rounded-br-none border-foreground/10 bg-muted px-3 py-2 text-left text-foreground/75 text-xs">
              <p className="line-clamp-2">{message.quote}</p>
            </blockquote>
          </div>
        )}
        <div className="relative flex w-fit max-w-[90%] flex-col items-end gap-1 rounded-[20px] rounded-tr-none bg-[var(--course-author-bg)] px-4 py-3">
          <div
            ref={measureContent}
            className={`overflow-hidden whitespace-pre-wrap break-all transition-all duration-200 ease-out ${
              shouldShowExpand && !isExpanded ? "line-clamp-3" : ""
            }`}
            style={{
              lineHeight: "1.5em",
              display:
                shouldShowExpand && !isExpanded ? "-webkit-box" : undefined,
              WebkitBoxOrient:
                shouldShowExpand && !isExpanded ? "vertical" : undefined,
              WebkitLineClamp: shouldShowExpand && !isExpanded ? 3 : undefined,
            }}
          >
            {message.content.trim()}
          </div>

          {/* Gradient overlay */}
          {shouldShowExpand && !isExpanded && (
            <div className="pointer-events-none absolute right-0 bottom-0 left-0 h-8 rounded-b-[20px] bg-gradient-to-t from-[var(--course-author-bg)] to-transparent transition-opacity duration-200" />
          )}

          {/* Expand/collapse button */}
          {shouldShowExpand && (
            <motion.button
              onClick={toggleExpand}
              className="absolute right-2 bottom-2 z-10 flex h-6 w-6 items-center justify-center rounded-full bg-black/5 hover:bg-black/20"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
              transition={{
                duration: 0.15,
                ease: "easeOut",
              }}
              aria-label={isExpanded ? "Thu gọn nội dung" : "Mở rộng nội dung"}
            >
              <motion.div
                animate={{ rotate: isExpanded ? 180 : 0 }}
                transition={{
                  duration: 0.2,
                  ease: "easeInOut",
                }}
              >
                <ChevronDown className="h-3 w-3 text-foreground/70" />
              </motion.div>
            </motion.button>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
