import GraphemeSplitter from "grapheme-splitter";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import Markdown from "react-markdown";
import { ChatIndicator } from "@/routes/learn/$courseSlug/$lessonSlug/__layout-learning";
import { cn } from "@/utils/cn";

interface TypingTextProps {
  text: string;
  speed?: number;
  onComplete?: () => void;
  className?: string;
  shouldStart?: boolean;
  isPaused?: boolean;
}

export const TypingText = ({
  text,
  speed = 15,
  onComplete,
  className,
  shouldStart = true,
  isPaused = false,
}: TypingTextProps) => {
  const [displayedText, setDisplayedText] = useState("");
  const [isTypingComplete, setIsTypingComplete] = useState(false);
  const indexRef = useRef(0);
  const splitter = useRef(new GraphemeSplitter());
  const animationCompleteRef = useRef(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const graphemes = useMemo(() => {
    return text.length > 0 ? splitter.current.splitGraphemes(text) : [];
  }, [text]);

  useEffect(() => {
    if (isPaused && timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, [isPaused]);

  useEffect(() => {
    if (!shouldStart || isPaused) return;

    if (text.length === 0) {
      setIsTypingComplete(true);
      return;
    }

    if (displayedText === "") {
      setDisplayedText("");
      setIsTypingComplete(false);
      indexRef.current = 0;
      animationCompleteRef.current = false;
    }

    const typeNextChar = () => {
      if (isPaused) {
        if (!timeoutRef.current) {
          return;
        }
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
        return;
      }

      const chunkSize =
        graphemes.length > 500 ? 3 : graphemes.length > 100 ? 2 : 1;
      const newIndex = Math.min(indexRef.current + chunkSize, graphemes.length);
      setDisplayedText(graphemes.slice(0, newIndex).join(""));
      indexRef.current = newIndex;

      if (newIndex < graphemes.length && !isPaused) {
        timeoutRef.current = setTimeout(typeNextChar, speed);
        return;
      }

      setIsTypingComplete(true);
    };

    timeoutRef.current = setTimeout(typeNextChar, speed);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [text, speed, shouldStart, graphemes, isPaused, displayedText]);

  // Handle completion
  useEffect(() => {
    if (!isTypingComplete || animationCompleteRef.current) return;

    animationCompleteRef.current = true;
    onComplete?.();
  }, [isTypingComplete, onComplete]);

  if (text.trim().length === 0 && isTypingComplete) {
    return <br />;
  }

  if (text.trim().length === 0) {
    return null;
  }

  return (
    <div className={cn("chat-md text-sm", className)}>
      <Markdown>{displayedText}</Markdown>
    </div>
  );
};

interface SequentialTypingProps {
  chunks: string[];
  speed?: number;
  className?: string;
  onComplete?: () => void;
  streamComplete: boolean;
  scrollToBottom: (() => void) | undefined;
  isPaused?: boolean;
}

export const SequentialTyping = ({
  chunks,
  speed = 15,
  className,
  onComplete,
  streamComplete,
  scrollToBottom,
  isPaused = false,
}: SequentialTypingProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [completedChunks, setCompletedChunks] = useState<number[]>([]);
  const [allComplete, setAllComplete] = useState(false);
  const onCompleteCalledRef = useRef(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const processedChunks = useMemo(() => {
    const newText = chunks.join("");
    if (!newText.includes("\n") && !streamComplete) {
      return [];
    }
    return newText.split("\n").filter((line, index) => {
      if (index === 0) {
        return line.trim().length > 0;
      }
      return true;
    });
  }, [chunks, streamComplete]);

  const handleChunkComplete = useCallback(
    (index: number) => {
      setCompletedChunks((prev) => {
        const newCompleted = [...prev, index];
        if (newCompleted.length === processedChunks.length) {
          setAllComplete(true);
          return newCompleted;
        }

        const nextIndex = index + 1;
        if (
          nextIndex < processedChunks.length &&
          !newCompleted.includes(nextIndex)
        ) {
          setCurrentIndex(nextIndex);
        }

        return newCompleted;
      });
    },
    [processedChunks],
  );

  useEffect(() => {
    if (
      scrollToBottom &&
      !allComplete &&
      !onCompleteCalledRef.current &&
      !isPaused
    ) {
      intervalRef.current = setInterval(() => {
        scrollToBottom();
      }, 100);
    } else if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Handle completion
    if (allComplete && !onCompleteCalledRef.current) {
      onCompleteCalledRef.current = true;
      onComplete?.();
    }

    // General cleanup for scrolling
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [scrollToBottom, allComplete, onComplete, isPaused]);

  if (!processedChunks.length) return <ChatIndicator />;

  return (
    <div className={cn("mb-10", className)} ref={containerRef}>
      {processedChunks.map((chunk, index) => (
        <TypingText
          key={`chunk-${index.toString()}`}
          text={chunk}
          speed={speed}
          shouldStart={
            index === currentIndex && !completedChunks.includes(index)
          }
          onComplete={() => handleChunkComplete(index)}
          isPaused={isPaused}
        />
      ))}
    </div>
  );
};
