import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { SendMessageInput } from "@/services/conversations";
import { UUID } from "@/types/app";
import { ChatbotAction } from "@/types/chat";
import { cn } from "@/utils/cn";
import { SparklesIcon } from "../icons/sparkles-icon";

interface FaqSuggestionsScrollProps {
  faqs: Array<{ id: string; question: string }> | undefined;
  isLoading: boolean;
  full?: boolean;
  onSubmit: (msg: SendMessageInput) => void;
}

export const FaqSuggestionsScroll = ({
  faqs,
  isLoading,
  full,
  onSubmit,
}: FaqSuggestionsScrollProps) => {
  const refScrollContainer = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  const handleFaqClick = (faq: { id: string; question: string }) => {
    onSubmit({
      input: faq.question,
      type: ChatbotAction.CHAT,
      hints: {
        faq_id: faq.id as UUID,
      },
    });
  };

  const updateScrollButtons = useCallback(() => {
    if (!refScrollContainer.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = refScrollContainer.current;
    setCanScrollLeft(scrollLeft > 0);
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth);
  }, []);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!refScrollContainer.current) return;

    setIsDragging(true);
    setStartX(e.pageX - refScrollContainer.current.offsetLeft);
    setScrollLeft(refScrollContainer.current.scrollLeft);
  }, []);

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!isDragging || !refScrollContainer.current) return;

      e.preventDefault();
      const x = e.pageX - refScrollContainer.current.offsetLeft;
      const walk = (x - startX) * 2;
      refScrollContainer.current.scrollLeft = scrollLeft - walk;
    },
    [isDragging, startX, scrollLeft],
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const scrollToNext = useCallback(() => {
    if (!refScrollContainer.current) return;

    const scrollAmount = refScrollContainer.current.clientWidth * 0.8;
    refScrollContainer.current.scrollBy({
      left: scrollAmount,
      behavior: "smooth",
    });
  }, []);

  const scrollToPrev = useCallback(() => {
    if (!refScrollContainer.current) return;

    const scrollAmount = refScrollContainer.current.clientWidth * 0.8;
    refScrollContainer.current.scrollBy({
      left: -scrollAmount,
      behavior: "smooth",
    });
  }, []);

  useEffect(() => {
    updateScrollButtons();

    const handleScroll = () => updateScrollButtons();
    const container = refScrollContainer.current;

    if (container) {
      container.addEventListener("scroll", handleScroll);
      return () => container.removeEventListener("scroll", handleScroll);
    }
  }, [updateScrollButtons]);

  useEffect(() => {
    updateScrollButtons();
  }, [updateScrollButtons]);

  if (!faqs?.length) {
    return null;
  }

  return (
    <div className={cn("relative", full && "hidden")}>
      {canScrollLeft && (
        <Button
          variant="outline"
          size="sm"
          className="-translate-y-1/2 !px-2 absolute top-1/2 left-0 z-10 rounded-full shadow-lg"
          onClick={scrollToPrev}
        >
          <ChevronLeftIcon className="size-4" />
        </Button>
      )}

      <div
        ref={refScrollContainer}
        className="flex cursor-grab flex-nowrap gap-4 overflow-auto text-sm active:cursor-grabbing [&::-webkit-scrollbar]:hidden"
        style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {isLoading ? (
          <>
            <div className="flex min-w-fit cursor-pointer items-center justify-end gap-2 rounded-lg border px-3 py-2 shadow-xs hover:text-green-600 hover:shadow-md">
              <SparklesIcon className="size-5 flex-none text-foreground/40" />
              <div className="h-4 w-32 rounded-full bg-border"></div>
            </div>
            <div className="flex min-w-fit cursor-pointer items-center justify-end gap-2 rounded-lg border px-3 py-2 shadow-xs hover:text-green-600 hover:shadow-md">
              <SparklesIcon className="size-5 flex-none text-foreground/40" />
              <div className="h-4 w-56 rounded-full bg-border"></div>
            </div>
          </>
        ) : (
          faqs?.map((faq) => (
            <div
              className="flex min-w-fit cursor-pointer items-center justify-end gap-2 rounded-lg border px-3 py-2 shadow-xs hover:text-green-600 hover:shadow-md"
              key={faq.id}
              onClick={() => handleFaqClick(faq)}
            >
              <SparklesIcon className="size-5 flex-none text-foreground/40" />
              <p className="line-clamp-1 select-none md:line-clamp-none">
                {faq.question}
              </p>
            </div>
          ))
        )}
      </div>

      {canScrollRight && (
        <Button
          variant="outline"
          size="sm"
          className="-translate-y-1/2 !px-2 absolute top-1/2 right-0 z-10 rounded-full shadow-lg"
          onClick={scrollToNext}
        >
          <ChevronRightIcon className="size-4" />
        </Button>
      )}
    </div>
  );
};
