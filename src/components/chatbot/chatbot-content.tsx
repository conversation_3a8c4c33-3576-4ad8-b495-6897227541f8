import { motion, useInView } from "framer-motion";
import { ArrowDown } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import owl<PERSON><PERSON> from "@/assets/images/owls/owl-hello.png";
import { ChatbotAi } from "@/components/chatbot/chatbot-ai";
import { ChatbotUser } from "@/components/chatbot/chatbot-user";
import { useWebSocketChat } from "@/components/chatbot/websocket-chat-provider";
import * as m from "@/paraglide/messages.js";
import { ChatMessageQuery } from "@/services/conversations";
import { PropsWithClassName } from "@/types/app";
import { ChatMessage } from "@/types/chat";
import { cn } from "@/utils/cn";
import { useIntersectionObserver } from "@/utils/use-intersection-observer";
import { Button } from "../ui/button";

interface ChatbotContentProps extends PropsWithClassName {
  messages: ChatMessageQuery;
  lessonId: string;
}

export const ChatbotContent = ({
  messages,
  className,
  lessonId,
}: ChatbotContentProps) => {
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const anchor = useRef<HTMLDivElement | null>(null);
  const lastMessage = useRef<ChatMessage | undefined>(undefined);
  const { onTypingChatSuccess, isStreaming } = useWebSocketChat();
  const isAnchorInView = useInView(anchor);
  const { ref: refTop } = useIntersectionObserver({
    threshold: 0.5,
    onChange(isIntersecting, entry) {
      if (!isIntersecting) return;
      const target = entry.target;
      const sibling = target.nextElementSibling;
      if (!target || !sibling) return;

      if (messages.isLoading || messages.isPending) return;

      if (messages.fetchStatus === "fetching") {
        sibling.scrollIntoView({ behavior: "instant" });
        return;
      }

      if (!messages.hasNextPage) return;

      sibling.scrollIntoView({ behavior: "instant" });
      messages.fetchNextPage();
    },
    initialIsIntersecting: false,
  });

  const messagesChronological = useMemo(() => {
    return (messages.data?.pages || []).slice().reverse().flat();
  }, [messages]);

  const scrollToBottom = useCallback((behavior: ScrollBehavior = "smooth") => {
    if (!anchor.current) return;
    anchor.current.scrollIntoView({ behavior: behavior });
  }, []);

  useEffect(() => {
    setShowScrollToBottom(!isAnchorInView && !isStreaming);
  }, [isAnchorInView, isStreaming]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: idgaf
  useEffect(() => {
    if (!messages.data?.pages?.length) return;

    const items = messages.data.pages[0];
    const last = items.at(-1);

    if (!lastMessage.current) {
      lastMessage.current = last;
      scrollToBottom("instant");
      return;
    }

    if (last?.id === lastMessage.current.id) return;

    lastMessage.current = last;
    scrollToBottom("smooth");
  }, [messages.dataUpdatedAt]);

  return (
    <div className="relative h-full">
      <div
        className={cn(
          "flex h-full flex-col gap-y-4 overflow-y-auto overflow-x-hidden",
          className,
        )}
      >
        <div ref={refTop} />
        {messages.isFetching ? (
          <ChatMessageRender skeleton />
        ) : (
          !messagesChronological.length && <ChatEmpty />
        )}
        {messagesChronological.map((message, index) => (
          <div key={message.id}>
            <ChatMessageRender
              message={message}
              lessonId={lessonId}
              onTypingComplete={
                index === messagesChronological.length - 1
                  ? onTypingChatSuccess
                  : undefined
              }
              scrollToBottom={scrollToBottom}
              className={cn(
                index === messagesChronological.length - 1 &&
                  "xxl:!min-h-[55dvh] min-h-[45dvh] md:min-h-[42dvh]",
              )}
            />
          </div>
        ))}
        <div ref={anchor} className="min-h-[1rem]" />
      </div>
      {showScrollToBottom && (
        <div className="-translate-x-1/2 absolute bottom-0 left-1/2 z-10">
          <Button
            variant="outline"
            size="icon"
            className="rounded-full bg-sidebar hover:bg-accent"
            onClick={() => scrollToBottom("smooth")}
          >
            <ArrowDown />
          </Button>
        </div>
      )}
    </div>
  );
};

function ChatEmpty() {
  const text = m["chatbot.intro.greeting"]();
  const splittedText = text.split("");

  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center font-medium">
        <img src={owlHello} alt="Alio" className="size-12" />
        {m["chatbot.intro.name"]()}
      </div>
      <div ref={ref}>
        {splittedText.map((letter, index) => (
          <motion.span
            key={index.toString()}
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : {}}
            transition={{ duration: 0.05, delay: index * 0.05 }}
          >
            {letter}
          </motion.span>
        ))}
      </div>
    </div>
  );
}

export function ChatMessageRender(
  params:
    | {
        message: ChatMessage;
        lessonId: string;
        onTypingComplete?: () => void;
        scrollToBottom?: () => void;
        className?: string;
      }
    | {
        skeleton: true;
      },
) {
  if ("skeleton" in params) {
    return (
      <div className="flex w-full flex-col gap-2">
        <div className="flex w-full justify-end text-sm">
          <div className="flex h-11 w-24 animate-pulse flex-col items-end gap-1 rounded-[20px] rounded-tr-[3px] bg-background"></div>
        </div>
        <div className="flex w-full text-sm">
          <div className="flex h-11 w-36 animate-pulse flex-col items-end gap-1 rounded-[20px] rounded-tl-[3px] bg-background"></div>
        </div>
      </div>
    );
  }

  if (params.message.isMock) {
    return <div className={params.className} />;
  }

  if (params.message.role === "user") {
    return (
      <ChatbotUser message={params.message} className={params.className} />
    );
  }

  return <ChatbotAi {...params} />;
}

export function ChatCooking() {
  return <p>{m["chatbot.loading.cooking"]()}</p>;
}
