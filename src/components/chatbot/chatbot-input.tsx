import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { useAtomValue, useSet<PERSON>tom } from "jotai/index";
import { ArrowUpIcon, Square, XIcon } from "lucide-react";
import { KeyboardEvent, memo, useEffect, useRef, useState } from "react";
import { useWebSocketChat } from "@/components/chatbot/websocket-chat-provider";
import { ChatHistory } from "@/components/icons";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import * as m from "@/paraglide/messages.js";
import {
  chatProfileQueryOptions,
  faqQueryOptions,
  SendMessageInput,
} from "@/services/conversations";
import {
  atomCurrentChat,
  atomCurrentGameUserAnswer,
  atomCurrentQuizId,
  atomCurrentSection,
  atomMappingQuizIdToUserAnswer,
} from "@/store/chat";
import { atomSidebarCourseChat } from "@/store/sidebar";
import { ChatbotAction, Conversation } from "@/types/chat";
import { SectionType } from "@/types/lessons";
import { cn } from "@/utils/cn";
import { useIsMobile } from "@/utils/use-mobile";
import { SparklesIcon } from "../icons/sparkles-icon";
import { useSaidbar } from "../saidbar";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";
import { ChatPreferencesSettings } from "./chat-preferences-settings";
import { FaqSuggestionsDropdown } from "./faq-suggestions-dropdown";
import { FaqSuggestionsScroll } from "./faq-suggestions-scroll";

const getDefaultPlaceholder = () => ({
  mobile: m["chatbot.input.placeholder_mobile"](),
  desktop: m["chatbot.input.placeholder_desktop"](),
});

interface ChatbotInputProps {
  conversation: Conversation | undefined;
  placeholder?: string;
  full?: boolean;
}

export const ChatbotInput = memo(
  ({ conversation, placeholder, full }: ChatbotInputProps) => {
    const [focused, setFocused] = useState(false);
    const isMobile = useIsMobile();
    const { setOpen, open } = useSaidbar(atomSidebarCourseChat);
    const [chatValue, setChatValue] = useState("");
    const currentMessage = useAtomValue(atomCurrentChat);
    const setCurrentMessage = useSetAtom(atomCurrentChat);
    const [suggestionsOpened, openSuggestions] = useState(false);
    const section = useAtomValue(atomCurrentSection);
    const quizId = useAtomValue(atomCurrentQuizId);
    const mappingQuizIdToUserAnswer = useAtomValue(
      atomMappingQuizIdToUserAnswer,
    );
    const currentGameUserAnswer = useAtomValue(atomCurrentGameUserAnswer);
    const queryChatProfile = useQuery(chatProfileQueryOptions());
    const { sendMessage, isStreaming, onTypingChatSuccess } =
      useWebSocketChat();

    const faqs = useQuery(
      faqQueryOptions({
        sectionID: section?.id,
        language: queryChatProfile.data?.preferences.language,
      }),
    );

    useEffect(() => {
      if (!currentMessage) {
        return;
      }
      openSuggestions(false);
      setChatValue(currentMessage.input);
      if (refInput.current) {
        refInput.current.focus();
      }
    }, [currentMessage]);

    const refInput = useRef<HTMLTextAreaElement>(null);

    const [debug, setDebug] = useState(false);

    const submit = (msg: SendMessageInput) => {
      if (!msg.hints) {
        msg.hints = {};
      }

      msg.hints.section_id = section?.id;

      // Send exact hint base on current section type
      switch (section?.type) {
        case SectionType.CHALLENGE:
          if (!quizId) return;
          msg.hints.quiz = {
            id: quizId,
            user_answer: mappingQuizIdToUserAnswer[quizId]?.answer || [],
          };
          break;
        case SectionType.GAME:
          if (!currentGameUserAnswer) return;
          msg.hints.game = {
            user_answer: currentGameUserAnswer,
          };
          break;
        default:
          break;
      }

      sendMessage(msg);
      setChatValue("");
      setCurrentMessage(null);
      setOpen(true);
      openSuggestions(false);
    };

    const handleSubmit = (v: string) => {
      const msg: SendMessageInput = !currentMessage
        ? {
            type: ChatbotAction.CHAT,
            input: v,
          }
        : {
            ...currentMessage,
            input: v,
          };

      submit(msg);
      setFocused(false);
    };

    const handleKeyDown = (event: KeyboardEvent<HTMLTextAreaElement>) => {
      if (event.shiftKey || isStreaming) {
        return;
      }

      if (event.key === "Enter") {
        event.preventDefault();
        if (!chatValue.trim()) {
          return;
        }
        handleSubmit(chatValue);
      }
    };

    return (
      <>
        <FaqSuggestionsDropdown
          faqs={faqs.data}
          isLoading={faqs.isPending}
          isOpen={suggestionsOpened}
          full={full}
          onSubmit={submit}
        />
        <motion.div
          className={cn(
            "relative w-full rounded-(--size-2xs) border border-(--learning-input-border) bg-(--card) shadow-lg transition-all duration-150",
          )}
        >
          {currentMessage && "quote" in currentMessage && (
            <div className="group/reply m-3 mb-0 flex gap-2">
              <blockquote className="rounded-sm border-l-4 bg-muted/50 px-3 py-2 text-left text-xs">
                <p className="line-clamp-2">{currentMessage.quote}</p>
              </blockquote>
              <div className="flex items-center">
                <button
                  type="button"
                  className="flex-none rounded-full p-1 hover:cursor-pointer hover:bg-muted"
                  onClick={() => {
                    setCurrentMessage(null);
                  }}
                >
                  <XIcon className="size-5 text-foreground/50" />
                </button>
              </div>
            </div>
          )}
          <Textarea
            ref={refInput}
            onChange={(e) => {
              return setChatValue(e.target.value);
            }}
            onKeyDown={handleKeyDown}
            value={chatValue}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            className={cn(
              "sm:!min-h-12 !min-h-8 !h-11 w-full resize-none border-none p-3 text-sm shadow-none outline-none transition-all duration-150 placeholder:text-sm sm:max-h-18",
              focused && "sm:!min-h-18 !min-h-22 sm:max-h-18",
              full && "!min-h-20 !h-20 max-h-20 bg-(--card)",
            )}
            placeholder={
              placeholder ||
              (isMobile
                ? getDefaultPlaceholder().mobile
                : getDefaultPlaceholder().desktop)
            }
          />
          <div className="flex gap-2 p-3 pt-0">
            <ChatPreferencesSettings />
            {!open && (
              <Button
                variant="outline"
                className="!px-2 bg-transparent font-normal md:hidden"
                onClick={() => setOpen(true)}
              >
                <ChatHistory />
                Chat history
              </Button>
            )}
            {full && !!faqs.data?.length && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    onClick={() => openSuggestions((v) => !v)}
                    className="!px-2 bg-transparent text-foreground/65 hover:text-foreground data-[active=true]:border-primary data-[active=true]:bg-transparent data-[active=true]:text-primary"
                    data-active={suggestionsOpened}
                  >
                    <SparklesIcon className="size-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Suggestions</TooltipContent>
              </Tooltip>
            )}

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="secondary"
                  className="!px-2 !pointer-events-auto ml-auto bg-(--learning-input-btn-bg) hover:bg-(--learning-input-btn-bg)/90"
                  onClick={() => {
                    if (isStreaming) {
                      onTypingChatSuccess(true);
                      return;
                    }
                    handleSubmit(chatValue);
                  }}
                  disabled={!chatValue.trim() && !isStreaming}
                >
                  {isStreaming ? (
                    <Square fill="#fff" />
                  ) : (
                    <ArrowUpIcon className="size-5" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent collisionPadding={{ right: 10 }}>
                <span className="rounded bg-background px-1 text-foreground">
                  Shift
                </span>{" "}
                +{" "}
                <span className="rounded bg-background px-1 text-foreground">
                  Enter
                </span>{" "}
                {m["chatbot.shortcuts.shift_enter"]()}
              </TooltipContent>
            </Tooltip>
          </div>
        </motion.div>
        <FaqSuggestionsScroll
          faqs={faqs.data}
          isLoading={faqs.isPending}
          full={full}
          onSubmit={submit}
        />

        {full && (
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
            className={cn("text-center text-foreground/50 text-xs")}
            onClick={(e) => {
              if (e.detail !== 3) {
                return;
              }

              setDebug((v) => !v);
            }}
          >
            {debug
              ? conversation?.id || ""
              : m["chatbot.shortcuts.enhanced_by_ai"]()}
          </motion.p>
        )}
      </>
    );
  },
);
