import { ChatDislikeDialog } from "@/components/chatbot/chat-dislike-dialog";
import { DislikeActiveIcon, LikeActiveIcon } from "@/components/icons";
import { DislikeIcon } from "@/components/icons/dislike-icon";
import { LikeIcon } from "@/components/icons/like-icon";
import { useMessageFeedback } from "@/services/conversations";
import { ChatMessage, FeedbackType } from "@/types/chat";

interface ChatbotFeedbackActionProps {
  message: ChatMessage;
  lessonId: string;
}

export const ChatbotFeedbackAction = ({
  message,
  lessonId,
}: ChatbotFeedbackActionProps) => {
  const { feedbackQuery, mutateFeedback } = useMessageFeedback({
    messageId: message.id,
    lessonId,
  });

  const handleFeedback = (feedbackType: FeedbackType, comment?: string) => {
    const currentFeedback = feedbackQuery.data?.feedback ?? FeedbackType.NONE;
    if (currentFeedback === feedbackType && !comment) {
      mutateFeedback({
        feedback: FeedbackType.NONE,
        comment: comment || "",
      });
      return;
    }
    mutateFeedback({
      feedback: feedbackType,
    });
  };

  const feedback = feedbackQuery.data?.feedback ?? FeedbackType.NONE;

  return (
    <div className="flex gap-2">
      <div
        className={`flex cursor-pointer items-center justify-center rounded-sm p-1 hover:bg-(--muted)`}
        onClick={() => handleFeedback(FeedbackType.POSITIVE)}
      >
        {feedback === FeedbackType.POSITIVE ? (
          <LikeActiveIcon className="size-4 cursor-pointer" />
        ) : (
          <LikeIcon className="size-4 cursor-pointer" />
        )}
      </div>
      <ChatDislikeDialog
        handleFeedback={(comment) =>
          handleFeedback(FeedbackType.NEGATIVE, comment)
        }
      >
        <div
          className={`flex cursor-pointer items-center justify-center rounded-sm p-1 hover:bg-(--muted)`}
        >
          {feedback === FeedbackType.NEGATIVE ? (
            <DislikeActiveIcon className="size-4 cursor-pointer" />
          ) : (
            <DislikeIcon className="size-4 cursor-pointer" />
          )}
        </div>
      </ChatDislikeDialog>
    </div>
  );
};
