import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { SettingsIcon } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import * as m from "@/paraglide/messages.js";
import {
  chatProfileQueryOptions,
  updateChatPreferences,
} from "@/services/conversations";
import {
  ChatLanguage,
  ChatPreferences,
  ChatTone,
  ChatVerbosity,
} from "@/types/chat";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";

export const ChatPreferencesSettings = () => {
  const [settingsOpened, openSettings] = useState(false);
  const queryClient = useQueryClient();
  const queryChatProfile = useQuery(chatProfileQueryOptions());

  const mutationPreferences = useMutation({
    mutationKey: ["chat:update_preferences"],
    mutationFn: (v: Partial<ChatPreferences>) => {
      return updateChatPreferences(v);
    },
    async onMutate(variables) {
      const key = chatProfileQueryOptions().queryKey;
      await queryClient.cancelQueries({ queryKey: key });

      const snapshot = queryClient.getQueryData(key);
      queryClient.setQueryData(key, (old) => {
        if (!old) {
          return old;
        }

        old.preferences = {
          ...old.preferences,
          ...variables,
        };
        return old;
      });

      return { snapshot };
    },
    onError: (_err, _variables, context) => {
      const key = chatProfileQueryOptions().queryKey;
      queryClient.setQueryData(key, context?.snapshot);
    },
    onSettled: () => {
      const key = chatProfileQueryOptions().queryKey;
      queryClient.invalidateQueries({ queryKey: key });
    },
  });

  function updatePreferences(type: keyof ChatPreferences, v: string) {
    return mutationPreferences.mutateAsync({
      [type]: !v ? null : v,
    });
  }

  return (
    <DropdownMenu open={settingsOpened} onOpenChange={openSettings}>
      <DropdownMenuTrigger asChild>
        <div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                data-active={settingsOpened}
                className="!px-2 bg-transparent text-foreground/65 hover:text-foreground data-[active=true]:border-primary data-[active=true]:bg-transparent data-[active=true]:text-primary"
              >
                <SettingsIcon className="size-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Settings</TooltipContent>
          </Tooltip>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="start">
        <DropdownMenuLabel>Chat Preferences</DropdownMenuLabel>
        {queryChatProfile.isPending ? (
          <DropdownMenuItem>Loading...</DropdownMenuItem>
        ) : (
          <DropdownMenuGroup>
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                {m["chatbot.settings.language"]()}
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  <DropdownMenuRadioGroup
                    value={queryChatProfile.data?.preferences?.language || ""}
                    onValueChange={(v) => updatePreferences("language", v)}
                  >
                    <DropdownMenuRadioItem value={""} key={null}>
                      {m["chatbot.settings.auto"]()}
                    </DropdownMenuRadioItem>
                    {Object.entries(ChatLanguage).map(([k, v]) => {
                      return (
                        <DropdownMenuRadioItem value={k} key={k}>
                          {v}
                        </DropdownMenuRadioItem>
                      );
                    })}
                  </DropdownMenuRadioGroup>
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>Tone</DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  <DropdownMenuRadioGroup
                    value={queryChatProfile.data?.preferences?.tone}
                    onValueChange={(v) => updatePreferences("tone", v)}
                  >
                    {Object.entries(ChatTone).map(([k, v]) => {
                      return (
                        <DropdownMenuRadioItem value={k} key={k}>
                          {v}
                        </DropdownMenuRadioItem>
                      );
                    })}
                  </DropdownMenuRadioGroup>
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>Verbosity</DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  <DropdownMenuRadioGroup
                    value={queryChatProfile.data?.preferences?.verbosity}
                    onValueChange={(v) => updatePreferences("verbosity", v)}
                  >
                    {Object.entries(ChatVerbosity).map(([k, v]) => {
                      return (
                        <DropdownMenuRadioItem value={k} key={k}>
                          {v}
                        </DropdownMenuRadioItem>
                      );
                    })}
                  </DropdownMenuRadioGroup>
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
          </DropdownMenuGroup>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
