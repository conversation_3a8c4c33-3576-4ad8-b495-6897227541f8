import { toast } from "sonner";
import * as m from "@/paraglide/messages.js";
import { cn } from "@/utils/cn";
import { createShareUrl, SHARE_PLATFORMS } from "@/utils/shareUrl";

interface ShareButtonProps {
  platform: (typeof SHARE_PLATFORMS)[number];
  shareUrl: string;
  children: React.ReactNode;
  shareMessage: string;
  className?: string;
}

export const ShareButton = ({
  platform,
  shareUrl,
  children,
  shareMessage,
  className,
}: ShareButtonProps) => {
  return (
    <div
      className={cn(
        "flex items-center justify-center rounded-full bg-(--background-disabled-secondary) p-1 transition-all duration-150 hover:shadow-sm md:p-2",
        className,
      )}
    >
      {platform.name === "Link" ? (
        <p
          className="cursor-pointer"
          onClick={() => {
            const url = createShareUrl(
              platform.name,
              `${window.location.origin}/${shareUrl}`,
              shareMessage,
            );
            navigator.clipboard.writeText(url);
            toast.success(m["ui.url_copied"]());
          }}
        >
          {children}
        </p>
      ) : (
        <a
          href={`#${platform.name}`}
          target="_blank"
          rel="noopener noreferrer"
          aria-label={`Share on ${platform.name}`}
          onClick={(e) => {
            e.preventDefault();
            const url = createShareUrl(
              platform.name,
              `${window.location.origin}/${shareUrl}`,
              shareMessage,
            );
            window.open(url, "_blank", "noopener,noreferrer");
          }}
        >
          {children}
        </a>
      )}
    </div>
  );
};
