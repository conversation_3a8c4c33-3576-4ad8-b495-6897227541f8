import { MoveRight } from "lucide-react";
import { useState } from "react";
import OwlSpeaker from "@/assets/images/owls/owl-speaker.png";
import { LoginFormDialog } from "@/components/login-form-dialog";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { GradientText } from "@/components/ui/gradient-text";
import * as m from "@/paraglide/messages.js";

type InviteDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  referral: string | undefined;
};

export default function InviteDialog({
  open,
  onOpenChange,
  referral,
}: InviteDialogProps) {
  const [showLoginForm, setOpenLoginForm] = useState(false);
  const referralTruncated = (referral || "").slice(0, 20);

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent>
          <DialogTitle className="sr-only">
            {m["ui.invite.title"]()} {referralTruncated}
          </DialogTitle>
          <div>
            <img src={OwlSpeaker} alt="owl-speaker" />
          </div>
          <div>
            <p className="text-base md:text-xl">{m["ui.invite.title"]()}</p>
            <GradientText className="font-light text-2xl md:text-4xl">
              @{referralTruncated}
            </GradientText>
          </div>
          <p className="mt-2 text-sm md:mt-4 md:text-base">
            {m["ui.invite.description"]()}
          </p>
          <p className="mt-2 text-sm md:mt-4 md:text-base">
            {m["ui.invite.start_exploring"]()}
          </p>
          <Button
            type="button"
            className="flex max-w-max items-center justify-center rounded-full font-normal"
            size="xl"
            onClick={() => {
              setOpenLoginForm(true);
              onOpenChange(false);
            }}
          >
            {m["ui.invite.agree_join"]()} <MoveRight />
          </Button>
        </DialogContent>
      </Dialog>
      <LoginFormDialog open={showLoginForm} onOpenChange={setOpenLoginForm} />
    </>
  );
}
