import { Link } from "@tanstack/react-router";
import aicademy<PERSON>ogo from "@/assets/images/aicademy-logo.png";
import aicademyLogoMobile from "@/assets/images/aicademy-logo-mobile.png";
import { SUBLOGO_URL } from "@/utils/api";

export function Logo() {
  return (
    <div className="flex items-end gap-2">
      <Link to="/" className="flex h-16 flex-none items-center py-2 xl:h-20">
        <img
          src={aicademyLogoMobile}
          alt="aicademy-logo-mobile"
          className="size-11 sm:hidden"
        />
        <img
          src={aicademyLogo}
          alt="aicademy-logo"
          className="hidden h-full sm:block"
        />
      </Link>
      {SUBLOGO_URL && (
        <Link to="/" className="flex h-16 flex-none items-end xl:h-20">
          <img
            src={SUBLOGO_URL}
            alt="sub-logo"
            className="h-2/3 max-w-24 object-contain opacity-60 sm:h-3/4 sm:max-w-20 xl:max-w-32"
          />
        </Link>
      )}
    </div>
  );
}
