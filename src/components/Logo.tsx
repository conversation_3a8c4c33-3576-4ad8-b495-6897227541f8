import { Link } from "@tanstack/react-router";
import aicademy<PERSON>ogo from "@/assets/images/aicademy-logo.png";
import aicademyLogoMobile from "@/assets/images/aicademy-logo-mobile.png";
import { SUBLOGO_URL } from "@/utils/api";

export function Logo() {
  return (
    <div className="flex items-center gap-3">
      <Link to="/" className="flex flex-none items-center gap-2">
        <img
          src={aicademyLogoMobile}
          alt="aicademy-logo-mobile"
          className="size-11 sm:hidden"
        />
        <img
          src={aicademyLogo}
          alt="aicademy-logo"
          className="hidden w-40 sm:block"
        />
      </Link>
      <div className="h-8 w-[1px] bg-gray-300"></div>

      {SUBLOGO_URL && (
        <Link to="/" className="flex flex-none items-end">
          <img
            src={SUBLOGO_URL}
            alt="sub-logo"
            className="block w-28 sm:w-40"
          />
        </Link>
      )}
    </div>
  );
}
