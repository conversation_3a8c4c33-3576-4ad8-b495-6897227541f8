import type { PropsWithClassNameAndChildren } from "@/types/app";
import { cn } from "@/utils/cn";

export const GradientText = ({
  children,
  className,
}: PropsWithClassNameAndChildren) => {
  return (
    <span
      className={cn(
        "inline-block bg-clip-text font-semibold text-transparent bg-gradient-to-r from-[#21AA4B] to-[#9FAD00]",
        className,
      )}
    >
      {children}
    </span>
  );
};
