import {cn} from "@/utils/cn";
import {CategoryEnum} from "@/types/app";

export const CategoryColor = {
  [CategoryEnum.AI]: "text-(--text-ai-foreground) bg-(--bg-ai)",
  [CategoryEnum.ENGLISH_LEARNING]: "text-(--text-blockchain-foreground) bg-(--bg-blockchain)",
  [CategoryEnum.CYBER_SECURITY]: "text-(--text-business-foreground) bg-(--bg-business)",
  [CategoryEnum.CANVA]:"text-(--text-canva-foreground) bg-(--bg-canva)",
  [CategoryEnum.SECURITY]: "text-(--text-security-foreground) bg-(--bg-security)",
  [CategoryEnum.BLOCKCHAIN]: "text-(--text-blockchain-foreground) bg-(--bg-blockchain)",
} as const;

export type CategoryColorKey = keyof typeof CategoryColor;

export const FallbackCategoryColor = "text-(--text-fallback-tag-foreground) bg-(--bg-fallback-tag-foreground)"
export const CourseCategoryTag = ({
  tag,
}: { tag?: string }) => {
  function isCategoryColorKey(tag: string): tag is CategoryColorKey {
    return tag in CategoryColor;
  }

  if(!tag) {
    return null
  }
  return (
    <p className={cn("py-1.5 px-2.5 inline w-max rounded-xs text-xs", isCategoryColorKey(tag) ? CategoryColor[tag] : FallbackCategoryColor)}>
      #{tag.trim().replace(/-/g, " ").toUpperCase()}
    </p>
  );
};
