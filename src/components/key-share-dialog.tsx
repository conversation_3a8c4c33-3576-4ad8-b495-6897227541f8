import inviteFriendsImg from "@/assets/images/invite-friends.png";
import keyAvailableImg from "@/assets/images/key-available.png";
import keyLockImg from "@/assets/images/key-lock.png";
import { ButtonCopy } from "@/components/button-copy";
import { ShareButton } from "@/components/share-button";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { GradientText } from "@/components/ui/gradient-text";
import { Input } from "@/components/ui/input";
import * as m from "@/paraglide/messages.js";
import { User } from "@/types/auth";
import { Lesson } from "@/types/lessons";
import { BASE_URL } from "@/utils";
import { SHARE_PLATFORMS, SharePlatformName } from "@/utils/shareUrl";

type LessonUnlockDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  lesson: Lesson;
  user: User;
};

export const KeyShareDialog = ({
  open,
  onOpenChange,
  lesson,
  user,
}: LessonUnlockDialogProps) => {
  const { username = "", ref_code = "" } = user || {};
  const refLink = `?invite=${username || ref_code || ""}`;
  return (
    <div>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent
          aria-describedby={undefined}
          className="gap-5 bg-(--card) p-4 sm:max-w-xl sm:p-8"
        >
          <DialogTitle className="sr-only">
            {m["courses.lesson_unlock.invite_friends_title"]()}
          </DialogTitle>
          <img
            src={inviteFriendsImg}
            alt="invite-friends"
            className="mb-1 w-32"
          />
          <div>
            <GradientText className="font-light text-2xl sm:text-4xl">
              {m["courses.lesson_unlock.invite_friends_title"]()}
            </GradientText>
            <p className="mt-2">
              {m["courses.lesson_unlock.no_keys_message"]()}
            </p>
          </div>
          <div className="flex max-w-sm gap-5">
            <div className="flex-1 rounded-(--size-2xs) bg-[#FFF2C2] p-5">
              <div className="flex items-center gap-2">
                <img
                  src={keyLockImg}
                  alt="key-lock"
                  className="h-auto max-w-[66px] object-cover"
                />
                <p className="text-3xl sm:text-[42px]">
                  0{lesson.requirements?.keys}
                </p>
              </div>
              <p className="mt-2 rounded-lg bg-[#FFBF00] px-4 py-1 text-center">
                {m["courses.lesson_unlock.required_key"]()}
              </p>
            </div>
            <div className="flex-1 rounded-(--size-2xs) bg-[#ECF5FF] p-5">
              <div className="flex items-center gap-2">
                <img
                  src={keyAvailableImg}
                  alt="key-lock"
                  className="h-auto max-w-[66px] object-cover"
                />
                <p className="text-3xl text-[#F4364B] sm:text-[42px]">
                  {user?.keys}
                </p>
              </div>
              <p className="mt-2 rounded-lg bg-[#B7D9FF] px-4 py-1 text-center">
                {m["courses.lesson_unlock.your_keys"]()}
              </p>
            </div>
          </div>
          <p>{m["courses.lesson_unlock.key_reward_message"]()}</p>
          <div className="relative w-full">
            <Input
              value={`${BASE_URL}${refLink}`}
              readOnly
              className="!py-6.5 w-full pr-6"
            />
            <div className="-translate-y-1/2 absolute top-1/2 right-2">
              <ButtonCopy
                content={`${BASE_URL}${refLink}`}
                label="Copy"
                className="rounded-md bg-secondary text-secondary-foreground hover:bg-secondary/80"
              />
            </div>
          </div>
          <div className="flex items-center gap-5">
            <p>{m["courses.lesson_unlock.or_share"]()}</p>
            <div className="flex items-center gap-3">
              {SHARE_PLATFORMS.filter(
                ({ name }) => name !== SharePlatformName.Instagram,
              ).map((platform) => (
                <ShareButton
                  key={platform.name}
                  platform={platform}
                  shareUrl={refLink}
                  shareMessage={m["courses.lesson_unlock.share_message"]()}
                  className="bg-(--bg-key-social-share)"
                >
                  <platform.Icon width={22} height={22} />
                </ShareButton>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
