import * as m from "@/paraglide/messages.js";
import { cn } from "@/utils/cn";

interface EmptyDataProps {
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  className?: string;
  children?: React.ReactNode;
}

export const EmptyData = ({
  title = m["ui.empty_data.no_data_found"](),
  description = m["ui.empty_data.no_data_description"](),
  icon,
  className,
  children,
}: EmptyDataProps) => {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center px-4 py-8 text-center",
        className,
      )}
    >
      {icon && <div className="mb-4 text-gray-400">{icon}</div>}

      <h3 className="mb-2 font-medium text-gray-900 text-lg dark:text-gray-100">
        {title}
      </h3>

      <p className="mb-4 max-w-md text-gray-500 text-sm dark:text-gray-400">
        {description}
      </p>

      {children}
    </div>
  );
};
