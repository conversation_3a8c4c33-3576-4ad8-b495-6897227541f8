import { motion } from "framer-motion";
import { useCallback, useEffect, useRef, useState } from "react";
import { ArrowLeftIcon, ArrowRightIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import { cn } from "@/utils/cn";
import { useIsMobile } from "@/utils/use-mobile";

interface CustomCarouselProps<T> {
  data: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  itemKey: (item: T, index: number) => string | number;
  className?: string;
  itemClassName?: string;
  showGradient?: boolean;
  leftArrowClass?: string;
  rightArrowClass?: string;
}

export function CustomCarousel<T>({
  data,
  renderItem,
  itemKey,
  className,
  itemClassName,
  showGradient = true,
  leftArrowClass = "",
  rightArrowClass = "",
}: CustomCarouselProps<T>) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftButton, setShowLeftButton] = useState(false);
  const [showRightButton, setShowRightButton] = useState(false);
  const isMobile = useIsMobile();

  const checkScrollButtons = useCallback(() => {
    if (!scrollContainerRef.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
    setShowLeftButton(scrollLeft > 20);
    // Ensure we check if we're close enough to the end to show the right button
    // Adding a larger threshold to ensure the last item is fully visible
    setShowRightButton(scrollLeft < scrollWidth - clientWidth - 5);
  }, []);

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    scrollContainer.addEventListener("scroll", checkScrollButtons);
    checkScrollButtons();

    return () => {
      scrollContainer.removeEventListener("scroll", checkScrollButtons);
    };
  }, [checkScrollButtons]);

  const scroll = useCallback((direction: "left" | "right") => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;
    const scrollAmount =
      direction === "left"
        ? -container.clientWidth / 2
        : container.clientWidth / 2;

    const targetScroll = container.scrollLeft + scrollAmount;
    container.scrollTo({
      left: targetScroll,
      behavior: "smooth",
    });
  }, []);

  return (
    <div className={cn("relative")}>
      {showGradient && showRightButton && !isMobile && (
        <motion.div className="pointer-events-none absolute top-0 right-0 bottom-0 z-10 w-8 bg-gradient-to-l from-gray-50 to-transparent lg:w-20" />
      )}

      <motion.div
        ref={scrollContainerRef}
        className={cn(
          "scrollbar-hide flex flex-1 snap-x snap-mandatory gap-4 overflow-x-auto p-1 pr-4",
          className,
        )}
        style={{
          scrollbarWidth: "none",
          msOverflowStyle: "none",
          willChange: "scroll-position",
          scrollBehavior: "smooth",
          paddingRight: "20px", // Add extra padding on the right to ensure last item is fully visible
        }}
      >
        {data.map((item, index) => (
          <motion.div
            key={itemKey(item, index)}
            className={cn("flex-shrink-0 snap-start", itemClassName)}
            style={{ willChange: "transform" }}
          >
            {renderItem(item, index)}
          </motion.div>
        ))}
        {/* Add an invisible spacer at the end to ensure last item can be fully scrolled to */}
        <div className="w-4 flex-shrink-0" aria-hidden="true" />
      </motion.div>

      {showLeftButton && (
        <Button
          variant="outline"
          onClick={() => scroll("left")}
          className={cn(
            "-left-5 -translate-y-1/2 absolute top-1/2 z-10 size-7 transform rounded-full border-gray-200 p-0 shadow-lg lg:size-10",
            leftArrowClass,
          )}
          aria-label="Scroll left"
        >
          <ArrowLeftIcon className="size-4 text-black md:size-6" />
        </Button>
      )}

      {showRightButton && (
        <Button
          variant="outline"
          onClick={() => scroll("right")}
          className={cn(
            "-right-6 -translate-y-1/2 absolute top-1/2 z-10 size-7 transform rounded-full border-gray-200 p-0 shadow-lg lg:size-10",
            rightArrowClass,
          )}
          aria-label="Scroll right"
        >
          <ArrowRightIcon className="size-4 text-black md:size-6" />
        </Button>
      )}
    </div>
  );
}
