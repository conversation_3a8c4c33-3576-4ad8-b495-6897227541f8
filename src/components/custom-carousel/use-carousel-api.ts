import type React from "react";
import { useCallback, useEffect, useState } from "react";
import type { CarouselApi } from "@/components/ui/carousel";

interface UseCarouselApiReturn {
  api: CarouselApi | undefined;
  setApi: React.Dispatch<React.SetStateAction<CarouselApi | undefined>>;
  selectedIndex: number;
  scrollTo: (index: number) => void;
}

export function useCarouselApi(): UseCarouselApiReturn {
  const [api, setApi] = useState<CarouselApi>();
  const [selectedIndex, setSelectedIndex] = useState(0);

  // Update the selected index when the carousel changes
  useEffect(() => {
    if (!api) return;

    const updateSelectedIndex = () => {
      setSelectedIndex(api.selectedScrollSnap());
    };

    // Initial update
    updateSelectedIndex();

    // Listen for changes
    api.on("select", updateSelectedIndex);

    // Cleanup
    return () => {
      api.off("select", updateSelectedIndex);
    };
  }, [api]);

  // Helper function to programmatically scroll to a specific slide
  const scrollTo = useCallback(
    (index: number) => {
      api?.scrollTo(index);
    },
    [api],
  );

  return { api, setApi, selectedIndex, scrollTo };
}
