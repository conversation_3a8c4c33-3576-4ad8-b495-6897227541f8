import { useCallback, useEffect, useRef, useState } from "react";
import { fetchJsxCode } from "@/services/jsx-preview";
import NavigationControl from "./lesson/exam/navigation-control";

type Props = {
  sectionContent: string;
  navigateBackSection?: () => void;
  sectionIndex: number;
  navigateSection: (newIndex: number) => void;
  isGameNext: boolean;
};

const JsxPreview = ({
  sectionContent,
  navigateBackSection,
  sectionIndex,
  navigateSection,
  isGameNext,
}: Props) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [loading, setLoading] = useState(false);
  const [jsxCode, setJsxCode] = useState("");

  const sendToIframe = useCallback((data: string) => {
    const dataToSend = {
      target: "aicademy-previewer",
      code: data,
    };
    if (iframeRef.current?.contentWindow) {
      const targetOrigin = new URL(iframeRef.current.src).origin;
      iframeRef.current.contentWindow.postMessage(dataToSend, targetOrigin);
    } else {
      console.error("Iframe chưa sẵn sàng để nhận dữ liệu.");
    }
  }, []);

  useEffect(() => {
    if (!sectionContent) return;
    try {
      setLoading(true);
      const parsedContent = JSON.parse(sectionContent);
      if (parsedContent.jsx_url) {
        fetchJsxCode({ url: parsedContent.jsx_url }).then((e) => {
          setJsxCode(e);
          setTimeout(() => sendToIframe(e), 5000);
        });
      }
      setLoading(false);
    } catch (e) {
      setLoading(false);
      console.warn(`invalid content format. recreating`);
    }
  }, [sectionContent, sendToIframe]);

  return (
    <div className="flex h-full flex-col overflow-hidden rounded-sm border border-slate-200/60 bg-white p-2">
      {!jsxCode && (
        <p className="flex-1 p-3 italic">
          Your visualized content will be displayed here...
        </p>
      )}
      <iframe
        ref={iframeRef}
        id="reactAppIframe"
        src="https://jsx-previewer.pages.dev/"
        title="React App Iframe"
        hidden={!jsxCode}
        className="h-full w-full"
      />
      <NavigationControl
        navigateBackSection={navigateBackSection}
        sectionIndex={sectionIndex}
        loading={loading}
        navigateSection={navigateSection}
        isGameNext={isGameNext}
      />
    </div>
  );
};

export default JsxPreview;
