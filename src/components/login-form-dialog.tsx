import { PropsWithChildren } from "react";
import { LoginMenu } from "@/components/navbar";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import * as m from "@/paraglide/messages.js";

export const LoginFormDialog = ({
  open,
  onOpenChange,
  children,
  onSuccess,
}: {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  referral?: string;
} & PropsWithChildren) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-(--card) sm:max-w-sm">
        <DialogHeader>
          <DialogTitle className="sr-only">
            {m["common.actions.login"]()}
          </DialogTitle>
        </DialogHeader>
        <LoginMenu
          onOpenChange={onOpenChange}
          onSuccess={onSuccess}
          variant="dialog"
        />
      </DialogContent>
    </Dialog>
  );
};
