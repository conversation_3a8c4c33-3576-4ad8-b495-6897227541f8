import { useAtomValue } from "jotai";
import { Navbar } from "@/components/navbar";
import { atomAuth } from "@/store/auth";

export function SiteHeader() {
  const auth = useAtomValue(atomAuth);

  return (
    <div className="!pb-0 sticky top-0 z-50 m-auto bg-(--background) xl:container lg:p-4 xl:px-0">
      <div className="flex items-center border border-b-(--line-border) bg-primary-foreground px-4 lg:rounded-(--size-2xs) lg:px-6 lg:shadow-[0_4px_6px_0_rgba(216,216,216,0.25)] 2xl:px-8">
        <Navbar key={auth?.jwt} />
      </div>
    </div>
  );
}
