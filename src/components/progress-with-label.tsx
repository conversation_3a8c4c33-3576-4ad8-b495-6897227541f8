import * as ProgressPrimitive from "@radix-ui/react-progress";
import { cn } from "@/utils/cn";

export function ProgressWithLabel({
  className,
  value,
  indicatorColor,
  labelClassName,
  ...props
}: React.ComponentProps<typeof ProgressPrimitive.Root> & {
  indicatorColor?: string;
  labelClassName?: string;
}) {
  const progressValue = value || 0;

  return (
    <div className="relative w-full">
      <ProgressPrimitive.Root
        data-slot="progress"
        className={cn(
          "relative h-[3px] w-full overflow-hidden rounded-full bg-[#E6E8F0]",
          className,
        )}
        {...props}
      >
        <ProgressPrimitive.Indicator
          data-slot="progress-indicator"
          className={cn(
            "h-full w-full flex-1 bg-primary transition-all",
            indicatorColor && `${indicatorColor}`,
          )}
          style={{ transform: `translateX(-${100 - progressValue}%)` }}
        />
      </ProgressPrimitive.Root>

      <div
        className="-translate-y-1/2 absolute top-1/2 transition-all duration-300 ease-out"
        style={{
          left: `${progressValue}%`,
          transform: progressValue === 0 ? undefined : "translateX(-100%)",
        }}
      >
        <div
          className={cn(
            "whitespace-nowrap rounded-full bg-primary px-4 py-1 font-medium text-white text-xs shadow-sm",
            "relative",
            labelClassName,
          )}
        >
          {Math.round(progressValue)}%
        </div>
      </div>
    </div>
  );
}
