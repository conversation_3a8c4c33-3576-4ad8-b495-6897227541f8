import { BookMarked, UsersRound } from "lucide-react";
import StarIcon from "@/assets/images/star.svg";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import * as m from "@/paraglide/messages.js";
import { Course, CourseStats } from "@/types/courses";

interface CourseInstructorProps {
  course: Course;
  stat?: CourseStats | null;
}

export default function CourseInstructor({
  course,
  stat,
}: CourseInstructorProps) {
  const instructor = course.instructor;

  if (!stat) return null;

  const { learners, stars } = stat;
  return (
    <div>
      <h4 className="text-xl">{m["courses.instructor"]()}</h4>
      <div className="mt-5 grid gap-x-12 md:grid-cols-10">
        <div className="flex flex-col gap-1.5 md:col-span-4">
          <Avatar className="h-14 w-14 object-cover md:h-20 md:w-20">
            <AvatarImage src={instructor?.avatar_url} />
            <AvatarFallback className="uppercase">
              {instructor?.name.slice(0, 2)}
            </AvatarFallback>
          </Avatar>
          <div className="flex items-center justify-between">
            <div className="flex items-center justify-center gap-2">
              <UsersRound className="h-4 w-4" />
              <span className="text-xs leading-8">
                {m["courses.total_learners"]()}
              </span>
            </div>
            <span className="text-xs">{learners}+</span>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center justify-center gap-2">
              <BookMarked className="h-4 w-4" />
              <span className="text-xs leading-8">
                {m["courses.total_courses"]()}
              </span>
            </div>
            <span className="flex items-center gap-0.5 text-xs">
              3 ({stars > 0 ? stars.toFixed(1) : "Not yet"}{" "}
              <img src={StarIcon} alt="star" className="h-3 w-3" />)
            </span>
          </div>
        </div>
        <div className="flex flex-col gap-2 md:col-span-6">
          <h4 className="font-semibold">
            {m["courses.about_instructor"]()} {instructor?.name}
          </h4>
          <p className="text-sm">{instructor?.bio}</p>
        </div>
      </div>
    </div>
  );
}
