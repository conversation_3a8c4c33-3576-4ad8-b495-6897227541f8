import { useQuery } from "@tanstack/react-query";
import { BookMarked } from "lucide-react";
import { useMemo, useState } from "react";
import * as m from "@/paraglide/messages.js";
import { fetchCourseVote } from "@/services/courses";
import { formatDate } from "@/utils/formatDate";
import { formatRating } from "@/utils/formaters";
import { StarIcon } from "../icons";
import { StarRating } from "../star-rating";
import ExtendableText from "../ui/extendable-text";
import { UserAvatar } from "../user-avatar";

export default function CourseReview(
  { courseId }: { courseId: string } = { courseId: "" },
) {
  const [showAllReviews, setShowAllReviews] = useState(false);

  const { data: votes = [] } = useQuery({
    queryKey: ["course-votes", courseId],
    queryFn: () => fetchCourseVote(courseId),
  });

  const overall = useMemo(() => {
    if (votes.length === 0) {
      return {
        rating: 0,
        courseQuality: 0,
        aiQuality: 0,
        learningEngagement: 0,
      };
    }
    const totals = votes.reduce(
      (acc, vote) => {
        acc.general += vote.general;
        acc.course_quality += vote.course_quality;
        acc.ai_mentor += vote.ai_mentor;
        acc.learning_engagement += vote.learning_engagement;
        return acc;
      },
      { general: 0, course_quality: 0, ai_mentor: 0, learning_engagement: 0 },
    );
    return {
      rating: formatRating(totals.general / votes.length),
      courseQuality: formatRating(totals.course_quality / votes.length),
      aiQuality: formatRating(totals.ai_mentor / votes.length),
      learningEngagement: formatRating(
        totals.learning_engagement / votes.length,
      ),
    };
  }, [votes]);

  if (votes.length === 0) {
    return null;
  }

  const handleShowAllReviews = () => {
    setShowAllReviews((prev) => !prev);
  };

  return (
    <div>
      <h4 className="text-xl leading-10" id={`course-reviews-${courseId}`}>
        {m["course.reviews.title"]()}
      </h4>
      <div className="mt-5 grid gap-x-12 md:grid-cols-10">
        <div className="flex flex-col gap-4 md:col-span-4">
          <div className="flex items-center gap-2.5">
            <StarIcon className="size-6 text-(--color-star-active)" />
            <span className="font-semibold text-3xl">{overall.rating}</span>
            <span className="text-sm">
              {m["course.reviews.course_rating"]()}
            </span>
          </div>
          <div className="h-[1px] w-full bg-(--secondary)" />
          <div className="flex flex-col gap-0.5">
            <div className="flex items-center justify-between">
              <div className="flex items-center justify-center gap-1.5">
                <BookMarked className="h-4 w-4" />
                <span className="text-xs leading-8">
                  {m["course.reviews.course_quality"]()}
                </span>
              </div>
              <span className="flex items-center gap-0.5 text-xs">
                {overall.courseQuality}{" "}
                <StarIcon className="size-3 text-(--color-star-active)" />
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center justify-center gap-1.5">
                <BookMarked className="h-4 w-4" />
                <span className="text-xs leading-8">
                  {m["course.reviews.ai_mentor"]()}
                </span>
              </div>
              <span className="flex items-center gap-0.5 text-xs">
                {overall.aiQuality}{" "}
                <StarIcon className="size-3 text-(--color-star-active)" />
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center justify-center gap-1.5">
                <BookMarked className="h-4 w-4" />
                <span className="text-xs leading-8">
                  {m["course.reviews.learning_engagement"]()}
                </span>
              </div>
              <span className="flex items-center gap-0.5 text-xs">
                {overall.learningEngagement}{" "}
                <StarIcon className="size-3 text-(--color-star-active)" />
              </span>
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-1.5 md:col-span-6">
          <h6 className="text-base leading-8">
            {votes.length} {m["course.reviews.reviews_count"]()}
          </h6>
          <div className="mb-4 flex flex-col gap-y-4">
            {votes.slice(0, showAllReviews ? votes.length : 2).map((vote) => (
              <div key={vote.id} className="flex flex-col gap-2">
                <div className="flex items-center">
                  <UserAvatar
                    username={vote.user?.username || ""}
                    url={vote.user?.avatar_url || ""}
                    className="size-10"
                  />
                  <span className="truncate text-sm">
                    {vote.user?.username || vote.user?.name}
                  </span>
                </div>
                <div className="flex items-center gap-2.5">
                  <StarRating initialValue={vote.general} size={16} readOnly />
                  <span className="text-xs">{formatDate(vote.created_at)}</span>
                </div>
                <ExtendableText value={vote.comment} className="text-sm" />
              </div>
            ))}
          </div>
          {votes.length > 2 && (
            <p
              className="cursor-pointer text-sm underline"
              onClick={handleShowAllReviews}
            >
              {showAllReviews
                ? m["course.reviews.show_less_reviews"]()
                : m["course.reviews.show_all_reviews"]()}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
