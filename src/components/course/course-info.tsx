import BookmarkIcon from "@/assets/images/bookmark.svg";
import DrawlIcon from "@/assets/images/drawl.svg";
import VideoGameIcon from "@/assets/images/video-game.svg";
import * as m from "@/paraglide/messages.js";
import { PropsWithClassName } from "@/types/app";
import { CourseStats } from "@/types/courses";
import { cn } from "@/utils/cn";

interface CourseInfoProps extends PropsWithClassName {
  wrapperClassName?: string;
  classNameIcon?: string;
  stats?: CourseStats | null;
  isBookmark?: boolean;
}

export default function CourseInfo(props: CourseInfoProps) {
  const { stats, wrapperClassName, classNameIcon, className, isBookmark } =
    props;
  if (!stats) {
    return null;
  }
  const { games, lessons, modules } = stats;

  return (
    <div
      className={cn(
        "flex flex-wrap items-center gap-x-4 gap-y-3 text-xs md:text-[13px]",
        wrapperClassName,
      )}
    >
      <div className={cn("flex items-center gap-2 rounded-sm", className)}>
        <img src={DrawlIcon} alt="drawl" className={classNameIcon} />
        <span>
          {!!modules && `${modules} ${m["course_info.modules"]()} -`}{" "}
          {`${lessons} ${m["course_info.lessons"]()}`}
        </span>
      </div>
      <div className={cn("flex items-center justify-center gap-2", className)}>
        <img src={VideoGameIcon} alt="video-game" className={classNameIcon} />
        <span>
          {games} {m["course_info.games"]()}
        </span>
      </div>
      {isBookmark && (
        <div className="flex items-center justify-center gap-2.5 rounded-sm border border-(--card-border) bg-(--learning-bookmark-bg) px-3 py-1 md:px-4 md:py-2">
          <span>{m["course_info.my_bookmark"]()}</span>
          <img src={BookmarkIcon} alt="bookmark" className="max-h-5" />
        </div>
      )}
    </div>
  );
}
