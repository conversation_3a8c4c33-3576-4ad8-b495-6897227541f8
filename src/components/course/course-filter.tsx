import { ChevronDown } from "lucide-react";
import { Check } from "lucide-react";
import { useState } from "react";
import { Categories } from "src/components/categories";
import { Button } from "@/components/ui/button";
import { Command, CommandGroup, CommandItem } from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import * as m from "@/paraglide/messages.js";
import { Route } from "@/routes/__layout/courses";
import { cn } from "@/utils/cn";

export function CourseFilters() {
  const sortOptions = [
    { value: "newest", label: m["courses.sort_newest"]() },
    { value: "top-rated", label: m["courses.sort_top_rated"]() },
    { value: "top-pick", label: m["courses.sort_top_pick"]() },
  ];
  const [open, setOpen] = useState(false);
  const [sortValue, setSortValue] = useState(sortOptions[0].value);
  const navigate = Route.useNavigate();
  const { category } = Route.useSearch();

  const handleSortChange = (value: string) => {
    setSortValue(value);
    setOpen(false);
    navigate({
      search: (prev) => ({
        ...prev,
        sort: value,
      }),
    });
  };

  return (
    <div>
      <div className="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
        <div className="w-full md:w-[70%] lg:w-[85%]">
          <Categories route="/courses" includeAll category={category} />
        </div>
        <div className="flex flex-1 items-center">
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                aria-expanded={open}
                className="h-max w-full border-(--card-border-secondary) bg-transparent font-medium"
              >
                <div className="!p-[1px] flex w-full items-center justify-between gap-9">
                  <div className="flex flex-col items-start justify-start">
                    <p className="mr-2 text-gray-500 text-sm">
                      {m["courses.sort_by"]()}
                    </p>
                    <p>
                      {sortOptions.find((option) => option.value === sortValue)
                        ?.label || m["courses.select"]()}
                    </p>
                  </div>
                  <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </div>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-40 p-0 md:w-48">
              <Command>
                <CommandGroup>
                  {sortOptions.map((option) => (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={() => handleSortChange(option.value)}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          sortValue === option.value
                            ? "opacity-100"
                            : "opacity-0",
                        )}
                      />
                      {option.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  );
}
