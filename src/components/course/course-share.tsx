import { memo, ReactNode } from "react";
import { ShareButton } from "@/components/share-button";
import * as m from "@/paraglide/messages.js";
import { Course } from "@/types/courses";
import { SHARE_PLATFORMS, SharePlatformName } from "@/utils/shareUrl";

export const CourseShare = memo<{
  head: ReactNode;
  course: Course;
}>(({ head, course }) => {
  const shareUrl = `courses/${course.slug}`;

  return (
    <div className="flex flex-wrap items-center gap-3">
      {head}

      <div className="flex items-center gap-3">
        {SHARE_PLATFORMS.filter(
          ({ name }) => name !== SharePlatformName.Instagram,
        ).map((platform) => (
          <ShareButton
            key={platform.name}
            platform={platform}
            shareUrl={shareUrl}
            shareMessage={m["courses.share_message"]()}
          >
            <platform.Icon width={22} height={22} />
          </ShareButton>
        ))}
      </div>
    </div>
  );
});
