import { CourseFilters } from "@/components/course/course-filter";
import { CourseItem } from "@/components/course/course-item";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { GradientText } from "@/components/ui/gradient-text";
import { Category, PropsWithClassName } from "@/types/app";
import type { Course } from "@/types/courses";
import { cn } from "@/utils/cn";

interface CourseContainerProps {
  headerImage: string;
  headerTitle: string;
  headerContent: string;
  data?: Course[];
  showFilter?: boolean;
}

export const CourseContainer = (props: CourseContainerProps) => {
  const {
    headerContent,
    headerTitle,
    headerImage,
    data = [],
    showFilter,
  } = props;

  return (
    <div className="flex flex-col">
      <header className="mb-5 flex flex-col md:mb-0 md:flex-row md:gap-6 lg:items-center">
        <div className="flex items-center gap-4 md:gap-8">
          {headerImage && (
            <img
              src={headerImage}
              alt="owl all course"
              className="size-14 md:size-20"
            />
          )}
          <GradientText className="font-light text-3xl lg:text-[42px]">
            {headerTitle}
          </GradientText>
        </div>
        <p className="translate-y-2 text-sm md:text-base lg:max-w-[60%]">
          {headerContent}
        </p>
      </header>

      <div
        className={cn(
          "rounded-(--size-2xs) border border-(--card-border-secondary) bg-primary-foreground",
        )}
      >
        <div className="relative flex flex-col gap-2 p-3 md:gap-5 md:p-6 lg:p-10">
          {showFilter && <CourseFilters />}
          <CourseMapper data={data} />
        </div>
      </div>
    </div>
  );
};

interface CourseMapperProps extends PropsWithClassName {
  data: Course[];
  showButton?: boolean;
  isCarousel?: boolean;
  category?: Category;
}

export function CourseMapper({
  data,
  className,
  showButton = true,
  isCarousel = false,
  category,
}: CourseMapperProps) {
  if (isCarousel) {
    return (
      <Carousel className="w-full">
        {!!category && (
          <div className="mb-2 flex items-start justify-between gap-2">
            <h4 className="text-lg lg:text-xl">
              Recommended if you're interested in {category?.name}
            </h4>
            <div className="flex items-center gap-2 sm:gap-4">
              <CarouselPrevious className="translate-0 relative inset-0 bg-(--card) md:size-10" />
              <CarouselNext className="translate-0 relative inset-0 bg-(--card) md:size-10" />
            </div>
          </div>
        )}
        <CarouselContent>
          {data.map((course) => (
            <CarouselItem
              key={course.id}
              className={cn(
                "flex flex-col items-center justify-center sm:basis-1/2 md:basis-1/3 lg:basis-1/4 lg:gap-10 xl:basis-1/6",
                className,
              )}
            >
              <CourseItem course={course} />
            </CarouselItem>
          ))}
        </CarouselContent>
        {showButton && !category && (
          <>
            <CarouselPrevious className="lg:-left-15 md:-left-10 -left-5 top-1/2 md:size-10" />
            <CarouselNext className="lg:-right-15 -right-8 md:-right-10 top-1/2 md:size-10" />
          </>
        )}
      </Carousel>
    );
  }

  return (
    <div className="@container w-full">
      <div className="grid @[25rem]:grid-cols-2 @[48rem]:grid-cols-4 @[80rem]:grid-cols-5 grid-cols-1 gap-4 md:gap-x-7">
        {data.map((course) => (
          <div
            key={course.id}
            className={cn(
              "flex flex-col items-center justify-center sm:basis-1/2 md:basis-1/3 lg:basis-1/4 lg:gap-10 xl:basis-1/5",
              className,
            )}
          >
            <CourseItem course={course} showPopover />
          </div>
        ))}
      </div>
    </div>
  );
}
