import { Course } from "@/types/courses";
import { cn } from "@/utils/cn";

export enum CourseTagEnum {
  NEW = "NEW",
  TOP_PICK = "TOP_PICK",
  RECOMMENDED = "RECOMMENDED",
  TOP_RATED = "TOP_RATED",
  TRENDING = "TRENDING",
  SOON = "SOON",
}

export const CourseTagColor = {
  [CourseTagEnum.TOP_PICK]: "bg-(--top-pick-tag-bg)",
  [CourseTagEnum.NEW]: "bg-(--new-tag-bg)",
  [CourseTagEnum.RECOMMENDED]: "bg-(--recommended-tag-bg)",
  [CourseTagEnum.TOP_RATED]: "bg-(--top-rated-tag-bg)",
  [CourseTagEnum.TRENDING]: "bg-(--trending-tag-bg)",
  [CourseTagEnum.SOON]: "bg-(--soon-tag-bg)",
};

export const calculateCourseTag = (course: Course): CourseTagEnum => {
  const now = new Date();
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const { published_at, stats } = course;
  const { learners, stars, reviews } = stats || {};

  if (course.unpublished) {
    return CourseTagEnum.SOON;
  }

  if (!learners || !stars || !reviews) {
    return CourseTagEnum.RECOMMENDED;
  }

  if (new Date(published_at) > oneWeekAgo) {
    return CourseTagEnum.NEW;
  }

  if (learners > 20) {
    return CourseTagEnum.TOP_PICK;
  }

  if (stars > 4.5 && reviews > 10) {
    return CourseTagEnum.TOP_RATED;
  }

  return CourseTagEnum.RECOMMENDED;
};

export const CourseTag = ({ tag }: { tag: CourseTagEnum }) => {
  return (
    <div
      className={cn(
        "flex w-max items-center justify-center rounded-xs px-1 py-1 md:px-2",
        CourseTagColor[tag],
      )}
    >
      <span className="font-bold text-secondary-foreground uppercase [font-size:var(--size-2xs)]">
        {tag.replace("_", " ")}
      </span>
    </div>
  );
};
