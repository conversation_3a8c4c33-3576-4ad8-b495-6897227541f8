import CheckIcon from "@/assets/images/check.svg";
import * as m from "@/paraglide/messages.js";
import { PropsWithClassName } from "@/types/app";
import { Course } from "@/types/courses";
import { cn } from "@/utils/cn";

export default function CourseAuthor({
  className,
  course,
}: { course?: Course } & PropsWithClassName) {
  return (
    <div className={cn("flex items-center gap-1.5", className)}>
      <div className="flex h-10 w-10 items-center justify-center overflow-hidden rounded-full border bg-(--card-tag-color)">
        <img
          src={
            course?.instructor?.avatar_url ||
            "https://i.ibb.co/7NxWvcwT/Group-14-1.png"
          }
          className="size-full"
          alt="owl"
        />
      </div>
      <p className="text-xs">
        {m["course.by"]()} {course?.instructor?.name || "<PERSON><PERSON>"}
      </p>
      <img src={CheckIcon} alt="check" />
    </div>
  );
}
