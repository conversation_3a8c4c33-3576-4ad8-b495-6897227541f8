import { Link } from "@tanstack/react-router";
import { useEffect, useMemo, useRef, useState } from "react";
import StarIcon from "@/assets/images/star.svg";
import {
  CourseActionButtons,
  CourseDescriptionSection,
} from "@/components/course/course-detail-items";
import CourseInfo from "@/components/course/course-info";
import CourseRating from "@/components/course/course-rating";
import { CourseTag, calculateCourseTag } from "@/components/course/course-tag";
import { CategoryColorKey, CourseCategoryTag } from "@/components/ui/hashtag";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Progress } from "@/components/ui/progress";
import { useUserCourseProgresses } from "@/services/courses";
import { getLearningCourseProgresses } from "@/services/courses/getLearningCourseProgresses";
import { useLessons } from "@/services/lessons";
import type { Course, UserCourse } from "@/types/courses";
import type { Lesson } from "@/types/lessons";
import { cn } from "@/utils/cn";
import { parseCourseDescription } from "@/utils/course";
import { formatDuration } from "@/utils/formatDuration";
import { Image } from "../ui/image";

interface CourseItemProps {
  course: Course;
  showPopover?: boolean;
}

export const CourseItem = ({ course, showPopover }: CourseItemProps) => {
  const [open, setOpen] = useState(false);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const { data: userCourse } = useUserCourseProgresses(course.id);
  const { data: lessons = [] } = useLessons(course.slug);

  const isCourseCompleted = useMemo(
    () =>
      !!userCourse?.progresses &&
      lessons.length > 0 &&
      lessons.every(
        (lesson) => userCourse.progresses[lesson.id] === "COMPLETED",
      ),
    [lessons, userCourse?.progresses],
  );

  const learningProgresses = useMemo(
    () =>
      getLearningCourseProgresses({
        lessons,
        userCourse,
        isCourseCompleted,
      }),
    [lessons, userCourse, isCourseCompleted],
  );

  const handleMouseEnter = () => {
    if (open) return;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setOpen(true);
    }, 500);
  };

  const handleMouseLeave = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    if (!open) return;
    setOpen(false);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, []);

  return (
    <div
      onMouseLeave={handleMouseLeave}
      className="relative size-full outline-none"
    >
      <div
        className={cn(
          "-right-10 absolute top-0 hidden h-full w-[50px] bg-transparent sm:block",
          open ? "z-50" : "z-0",
        )}
      />
      <div
        className={cn(
          "-left-10 absolute top-0 hidden h-full w-[50px] bg-transparent sm:block",
          open ? "z-50" : "z-0",
        )}
      />
      <Popover open={open && !course.unpublished} data-side="right">
        <PopoverTrigger asChild onMouseEnter={handleMouseEnter}>
          <Link
            to={!course.unpublished ? "/courses/$courseSlug" : "/courses"}
            params={{ courseSlug: course.slug }}
            className="relative flex size-full flex-col justify-between p-[3px]"
          >
            <div className="relative">
              <div className="absolute inset-0 h-full w-full rounded-lg bg-(--card-border-secondary)" />
              <div className="hover:-translate-[3px] relative flex flex-col items-end justify-end rounded-lg border border-(--card-border-secondary) bg-(--course-card-bg) px-3 pt-12 pb-6 transition-all duration-150">
                <div className="relative aspect-[4/3] w-full overflow-hidden rounded-md">
                  <Image
                    src={course.image_url}
                    alt={course.name}
                    className="absolute inset-0 rounded-md"
                    objectFit="contain"
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    fallbackText="Course image"
                  />
                </div>

                <div className="-translate-x-1/2 absolute top-3 left-1/2 flex w-[90%] items-center justify-between">
                  {course.stats?.stars ? (
                    <div className="flex items-center gap-1">
                      <img src={StarIcon} alt="star" />
                      <p className="font-semibold text-xs">
                        {course.stats.stars > 0
                          ? course.stats.stars.toFixed(1)
                          : course.stats.stars}
                      </p>
                    </div>
                  ) : (
                    <div></div>
                  )}
                  <CourseTag tag={calculateCourseTag(course)} />
                </div>
                {learningProgresses > 0 && (
                  <Progress
                    indicatorColor="bg-primary"
                    className="-translate-x-1/2 absolute bottom-[15px] left-1/2 z-50 h-1 w-[90%]"
                    value={learningProgresses}
                  />
                )}
              </div>
            </div>
            <div className="flex flex-1 flex-col justify-between gap-2 py-3">
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <Image
                    className="h-6 w-6 rounded-full"
                    src={course.instructor?.avatar_url || ""}
                    alt="course image"
                  />
                  <p className="text-xs lg:text-sm">
                    by {course.instructor?.name}
                  </p>
                </div>
                {course.categories?.[0] && (
                  <CourseCategoryTag
                    tag={course.categories[0].slug as CategoryColorKey}
                  />
                )}
                <p className="line-clamp-2 text-ellipsis text-sm lg:text-base">
                  {course.name}
                </p>
              </div>
              <div className="flex items-center gap-1 text-xs">
                <p>{course.level}</p>
                <p>-</p>
                <p>{formatDuration(course.duration)}</p>
              </div>
            </div>
          </Link>
        </PopoverTrigger>
        {showPopover && (
          <CourseItemPopover
            course={course}
            handleMouseEnter={handleMouseEnter}
            userCourse={userCourse}
            lessons={lessons}
          />
        )}
      </Popover>
    </div>
  );
};

const CourseItemPopover = ({
  course,
  handleMouseEnter,
  userCourse,
  lessons,
}: {
  course: Course;
  handleMouseEnter: () => void;
  userCourse: UserCourse | undefined;
  lessons: Lesson[];
}) => {
  const isCourseCompleted = useMemo(
    () =>
      !!userCourse?.progresses &&
      lessons.length > 0 &&
      lessons.every(
        (lesson) => userCourse.progresses[lesson.id] === "COMPLETED",
      ),
    [lessons, userCourse?.progresses],
  );

  const continueLessonSlug = useMemo(
    () =>
      isCourseCompleted
        ? lessons[0]?.slug
        : userCourse?.continue_lesson?.slug || lessons[0]?.slug,
    [isCourseCompleted, lessons, userCourse?.continue_lesson?.slug],
  );

  return (
    <PopoverContent
      onMouseEnter={handleMouseEnter}
      side="right"
      align="start"
      className="relative hidden max-h-[60dvh] w-[450px] overflow-y-auto overflow-x-hidden border-(--card-border-secondary) bg-white sm:block"
    >
      <div className="flex w-full flex-col gap-4 py-1">
        <div className="flex items-center justify-between">
          <CourseCategoryTag tag={course.categories?.[0]?.slug} />
          <CourseTag tag={calculateCourseTag(course)} />
        </div>
        <div className="flex flex-col gap-2">
          <h4 className="text-base">{course.name}</h4>
          <CourseRating stats={course.stats} courseId={course.id} />
          <CourseInfo
            stats={course.stats}
            wrapperClassName="flex-row items-start"
            className="[font-size:var(--size-2xs)] md:text-xs"
            classNameIcon="w-5 h-5"
          />
        </div>
        <CourseDescriptionSection
          isPreview
          description={parseCourseDescription(course.description)}
          course={course}
        />
        <div className="max-w-[60%]">
          <CourseActionButtons
            isPreview
            course={course}
            continueLessonSlug={continueLessonSlug}
            isCourseCompleted={isCourseCompleted}
            userCourse={userCourse}
          />
        </div>
      </div>
    </PopoverContent>
  );
};
