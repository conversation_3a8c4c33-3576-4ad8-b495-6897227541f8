import StarIcon from "@/assets/images/star.svg";
import * as m from "@/paraglide/messages.js";
import { PropsWithClassName } from "@/types/app";
import { CourseStats } from "@/types/courses";
import { cn } from "@/utils/cn";

interface CourseRatingProps extends PropsWithClassName {
  stats?: CourseStats | null;
  courseId: string;
}

export default function CourseRating({
  className,
  stats,
  courseId,
}: CourseRatingProps) {
  if (!stats) {
    return null;
  }
  const { stars, reviews, learners } = stats;

  return (
    <div className={cn("flex items-center gap-0.5 md:gap-1.5", className)}>
      <div className="flex items-center gap-1">
        <img src={StarIcon} alt="star" />
        <p
          className={cn(
            "font-semibold text-sm md:text-base",
            stars <= 0 && "font-normal md:text-sm",
          )}
        >
          {stars > 0 ? stars.toFixed(1) : m["course.rating.not_yet"]()}
        </p>
      </div>
      <p>·</p>
      <p className="text-xs leading-5 md:text-sm">
        {learners}+{" "}
        {learners === 1
          ? m["course.rating.learner"]()
          : m["course.rating.learners"]()}
      </p>
      <p>·</p>
      <p
        className={cn(
          "cursor-pointer text-xs md:text-sm",
          !reviews ? "text-black/40" : "underline",
        )}
        onClick={() => {
          if (!reviews) {
            return;
          }
          const el = document.getElementById(`course-reviews-${courseId}`);
          if (el) {
            el.scrollIntoView({ behavior: "smooth" });
          }
        }}
      >
        {reviews}{" "}
        {reviews === 1
          ? m["course.rating.review"]()
          : m["course.rating.reviews"]()}
      </p>
    </div>
  );
}
