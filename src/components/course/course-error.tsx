import { Link } from "@tanstack/react-router";
import CourseEmptyImg from "@/assets/images/course-empty.png";
import CourseErrorImg from "@/assets/images/course-error.png";
import * as m from "@/paraglide/messages.js";
import { ArrowLineRightIcon } from "../icons";
import { ShadowButton } from "../shadow-button";
import { GradientText } from "../ui/gradient-text";

export const CourseError = ({
  content,
  status,
}: {
  content: string;
  status: "error" | "not-found";
}) => {
  return (
    <div className="flex h-[60dvh] flex-col items-center justify-center gap-8">
      <GradientText className="font-light text-4xl sm:text-6xl">
        {m["course.oops"]()}
      </GradientText>
      <p className="text-xl sm:text-3xl">{content}</p>
      <img
        src={status === "not-found" ? CourseEmptyImg : CourseErrorImg}
        alt={m["course.course_error_alt"]()}
        className="max-w-28"
      />
      <Link to="/dashboard">
        <ShadowButton
          size="lg"
          className="md:!px-16 flex justify-center rounded-full"
        >
          {m["course.go_back_home"]()}
          <ArrowLineRightIcon />
        </ShadowButton>
      </Link>
    </div>
  );
};
