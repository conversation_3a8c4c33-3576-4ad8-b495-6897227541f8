import { useNavigate } from "@tanstack/react-router";
import { useAtomValue, useSet<PERSON>tom } from "jotai/index";
import { ArrowRight } from "lucide-react";
import { memo, useMemo } from "react";
import CourseInfo from "@/components/course/course-info";
import { CourseTag, calculateCourseTag } from "@/components/course/course-tag";
import { CheckIcon } from "@/components/icons";
import { LoginFormDialog } from "@/components/login-form-dialog";
import { ShadowButton } from "@/components/shadow-button";
import * as m from "@/paraglide/messages.js";
import { useLessons } from "@/services/lessons";
import { atomAuthHint } from "@/store/auth";
import { atomLessonUnlock } from "@/store/lesson";
import { Course, CourseDescription, UserCourse } from "@/types/courses";
import { cn } from "@/utils/cn";

export const Divider = memo(() => (
  <div className="mt-4 mb-2.5 h-[1px] w-full bg-[var(--line-border)] md:mt-8 md:mb-5" />
));

export interface CourseImageSectionProps {
  course: Course;
}

export const CourseImageSection = memo(
  ({ course }: CourseImageSectionProps) => (
    <div className="flex xs:flex-row flex-col items-center gap-3 sm:flex-col md:items-start">
      <div className="h-auto w-auto sm:max-w-[50dvw]">
        <img src={course.image_url} alt="course-image" className="w-full" />
      </div>
      <CourseInfo
        stats={course.stats}
        wrapperClassName="items-start"
        className="[font-size:var(--size-2xs)] md:px-4 md:text-xs"
        classNameIcon="w-5 h-5"
      />
    </div>
  ),
);

interface CourseDescriptionSectionProps {
  description: CourseDescription;
  course: Course;
  isPreview?: boolean;
}

export const CourseDescriptionSection = memo(
  ({ course, description, isPreview }: CourseDescriptionSectionProps) => (
    <div className="flex flex-col items-start gap-2 md:gap-5">
      {!isPreview && <CourseTag tag={calculateCourseTag(course)} />}
      <div className="flex flex-col gap-4">
        <div>
          <p className="font-semibold text-base">
            {m["courses.what_you_will_learn"]()}
          </p>
          <p className="text-sm leading-5">{description.overview}</p>
        </div>
        <div className="flex flex-col gap-2">
          {description.goals.map((goal, i) =>
            isPreview && i > 1 ? null : (
              <p key={i.toString()} className="flex items-start gap-1 text-sm">
                <CheckIcon className="mr-1 size-4 min-w-4" />
                {goal}
                {isPreview && i === 1 && " ..."}
              </p>
            ),
          )}
        </div>
      </div>
    </div>
  ),
);

export interface ActionButtonsProps {
  course: Course;
  continueLessonSlug?: string;
  isCourseCompleted: boolean;
  userCourse?: UserCourse;
  isFixedBottom?: boolean;
  isPreview?: boolean;
}

export function CourseActionButtons({
  course,
  continueLessonSlug,
  isCourseCompleted,
  userCourse,
  isFixedBottom,
  isPreview,
}: ActionButtonsProps) {
  const { data: lessons } = useLessons(course.slug);
  const navigate = useNavigate();
  const currentLesson = !userCourse
    ? lessons?.[0]
    : userCourse?.continue_lesson;
  const isRequiredUnlockLesson =
    currentLesson &&
    !userCourse?.progresses?.[currentLesson.id] &&
    currentLesson?.requirements?.keys;
  const setLessonUnlock = useSetAtom(atomLessonUnlock);
  const buttonText = useMemo(() => {
    if (isCourseCompleted) return m["courses.revise"]();
    if (isRequiredUnlockLesson) return m["courses.unlock_to_continue"]();
    if (!userCourse) {
      return m["courses.start_learning"]();
    }
    return m["courses.continue_learning"]();
  }, [isCourseCompleted, userCourse, isRequiredUnlockLesson]);

  const authHint = useAtomValue(atomAuthHint);

  const handleButtonClick = () => {
    if (!isRequiredUnlockLesson) {
      navigate({
        to: "/learn/$courseSlug/$lessonSlug",
        params: {
          courseSlug: course.slug,
          lessonSlug: continueLessonSlug || "",
        },
      });
      return;
    }
    setLessonUnlock(currentLesson);
    if (isPreview) {
      navigate({
        to: "/courses/$courseSlug",
        params: {
          courseSlug: course.slug,
        },
      });
    }
  };

  if (!authHint) {
    return (
      <LoginFormDialog>
        <ShadowButton
          size={isPreview ? "lg" : isFixedBottom ? "default" : "xl"}
          className={cn(
            "flex w-full items-center justify-center gap-2.5 rounded-full py-6 text-primary-foreground ",
            isFixedBottom && "w-full py-5 text-sm",
          )}
        >
          <span className="font-normal text-xl">
            {m["courses.start_course"]()}
          </span>
          <ArrowRight />
        </ShadowButton>
      </LoginFormDialog>
    );
  }

  return (
    <ShadowButton
      size={isPreview ? "lg" : isFixedBottom ? "default" : "xl"}
      className={cn(
        "flex w-full items-center justify-center gap-2.5 rounded-full py-6 text-primary-foreground",
        isFixedBottom && "w-full py-5 text-sm",
      )}
      onClick={handleButtonClick}
    >
      <span className="font-normal sm:text-xl">{buttonText}</span>
      <ArrowRight />
    </ShadowButton>
  );
}
