import { Link, useNavigate } from "@tanstack/react-router";
import { motion } from "framer-motion";
import { useSet<PERSON>tom } from "jotai/index";
import { ChevronRight } from "lucide-react";
import { memo, useMemo } from "react";
import CourseInfo from "@/components/course/course-info";
import { CourseShare } from "@/components/course/course-share";
import { LearningCourseRating } from "@/components/dashboard/learning-course-rating";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import * as m from "@/paraglide/messages.js";
import { useCourse } from "@/services/courses";
import { getLearningCourseProgresses } from "@/services/courses/getLearningCourseProgresses";
import { useLessons } from "@/services/lessons";
import { useMyVoting } from "@/services/my-learning";
import { atomLessonUnlock } from "@/store/lesson";
import { UserOther } from "@/types/auth";
import { Course, UserCourse } from "@/types/courses";
import { UserVoting } from "@/types/rating";
import { formatDuration } from "@/utils/formatDuration";
import { CourseTag, calculateCourseTag } from "../course/course-tag";
import { Image } from "../ui/image";
import { Progress } from "../ui/progress";

interface LearningItemProps {
  userCourse: UserCourse;
  isCompleted: boolean;
}

interface CourseViewProps {
  course: Course;
  userCourse: UserCourse;
  currentVote?: UserVoting;
}

export const LearningItemSkeleton = memo(
  ({ isCompleted }: { isCompleted: boolean }) => {
    return isCompleted ? (
      <CompletedCourseViewSkeleton />
    ) : (
      <InProgressCourseViewSkeleton />
    );
  },
);

const CompletedCourseViewSkeleton = memo(() => (
  <div className="rounded-(--size-2xs) bg-(--card) p-5 md:px-9 md:py-12">
    <div className="flex flex-1 flex-col items-start justify-between gap-3 gap-y-3 md:flex-row md:gap-x-5 xl:gap-x-14">
      <div className="flex flex-1 flex-col gap-y-2 md:gap-y-3">
        <Skeleton className="h-6 w-20 rounded-full" />
        <Skeleton className="h-8 w-3/4" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-6 w-6 rounded-full" />
          <Skeleton className="h-4 w-24" />
        </div>
        <div className="flex flex-col gap-1">
          <Skeleton className="h-2 w-full" />
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-4 w-40" />
        </div>
      </div>
      <div className="flex w-full flex-col gap-2 md:max-w-[40%] md:gap-3 xl:max-w-[30%]">
        <div className="flex flex-col gap-7 rounded-lg border border-(--card-border-secondary) p-4 md:gap-10 md:p-6">
          <Skeleton className="h-10 w-full rounded-full" />
          <div className="flex flex-col gap-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-3 w-24" />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-32" />
          <div className="flex gap-1">
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i.toString()} className="h-4 w-4" />
            ))}
          </div>
        </div>
      </div>
    </div>
  </div>
));

const InProgressCourseViewSkeleton = memo(() => (
  <div className="flex flex-col justify-between gap-x-10 gap-y-4 rounded-(--size-2xs) bg-(--card) p-5 md:gap-x-14 md:gap-y-6 md:px-9 md:py-12 lg:flex-row lg:gap-x-20">
    <div className="flex flex-1 flex-row-reverse items-start justify-between gap-3 gap-y-2 md:justify-start md:gap-x-5 lg:flex-row xl:gap-x-11">
      <div className="flex flex-col gap-3">
        <Skeleton className="h-44 max-h-44 w-40 min-w-40 rounded-lg" />
        <div className="hidden lg:block">
          <div className="flex flex-col gap-1">
            <Skeleton className="h-2 w-full" />
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-y-2 md:gap-y-3">
        <Skeleton className="h-6 w-20 rounded-full" />
        <Skeleton className="h-8 w-3/4" />
        <div className="flex flex-col gap-3">
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-full" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="block xs:max-w-2/3 lg:hidden">
            <div className="flex flex-col gap-1">
              <Skeleton className="h-2 w-full" />
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
        </div>
        <div className="hidden flex-col gap-3 sm:flex">
          <div className="flex items-center gap-4">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-16" />
          </div>
          <div className="flex flex-col gap-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-3 w-24" />
          </div>
        </div>
      </div>
    </div>

    <div className="flex flex-col gap-3 sm:hidden">
      <div className="flex items-center gap-4">
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-16" />
      </div>
    </div>

    {/* Current Lesson Section Skeleton */}
    <div className="w-full lg:w-[30%]">
      <div className="flex h-max flex-col justify-between gap-5 rounded-lg border border-(--card-border-secondary) p-4 sm:flex-row sm:items-center md:p-6 lg:flex-col lg:items-start">
        <div className="flex flex-col gap-2">
          <Skeleton className="h-6 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
        <Skeleton className="h-10 w-full rounded-full lg:w-48" />
      </div>
    </div>
  </div>
));

export const LearningItem = memo(
  ({ userCourse, isCompleted }: LearningItemProps) => {
    const { data: course } = useCourse(userCourse.course_id);
    const { data: myVoting } = useMyVoting({ enabled: isCompleted });
    if (!course)
      return (
        <LearningItemSkeleton
          key={`skeleton-${userCourse.course_id.toString()}`}
          isCompleted={isCompleted}
        />
      );

    return isCompleted ? (
      <CompletedCourseView
        currentVote={myVoting?.find((v) => v.course_id === course.id)}
        course={course}
        userCourse={userCourse}
      />
    ) : (
      <InProgressCourseView course={course} userCourse={userCourse} />
    );
  },
);

const CompletedCourseView = memo(
  ({ course, userCourse, currentVote }: CourseViewProps) => (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2, ease: "easeOut" }}
      className="rounded-(--size-2xs) bg-(--card) p-5 md:px-9 md:py-12"
    >
      <div className="flex flex-1 flex-col items-start justify-between gap-3 gap-y-3 md:flex-row md:gap-x-5 xl:gap-x-14">
        <div className="flex flex-1 flex-col gap-y-2 md:gap-y-3">
          <CourseTag tag={calculateCourseTag(course)} />
          <Link to="/courses/$courseSlug" params={{ courseSlug: course.slug }}>
            <p className="inline-block text-xl md:text-[28px]">{course.name}</p>
          </Link>
          <CourseAuthorInfo instructor={course.instructor} />
          <LearningProgress
            level={course.level}
            duration={course.duration}
            progress={100}
            completionDate={userCourse?.completed_at || ""}
          />
        </div>
        <div className="flex w-full flex-col gap-2 md:max-w-[40%] md:gap-3 xl:max-w-[30%]">
          <CompletedLesson userCourse={userCourse} course={course} />
          <LearningCourseRating
            courseId={course.id}
            currentVote={currentVote}
          />
        </div>
      </div>
    </motion.div>
  ),
);

const InProgressCourseView = ({ course, userCourse }: CourseViewProps) => {
  const { data: lessons = [] } = useLessons(course.slug);
  const learningProgresses = useMemo(
    () =>
      getLearningCourseProgresses({
        lessons,
        userCourse,
        isCourseCompleted: false,
      }),
    [lessons, userCourse],
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2, ease: "easeOut" }}
      className="flex flex-col justify-between gap-x-10 gap-y-4 rounded-(--size-2xs) bg-(--card) p-5 md:gap-x-14 md:gap-y-6 md:px-9 md:py-12 lg:flex-row lg:gap-x-20"
    >
      <div className="flex flex-1 flex-row-reverse items-start justify-between gap-3 gap-y-2 md:justify-start md:gap-x-5 lg:flex-row xl:gap-x-11">
        <CourseImageSection course={course} progress={learningProgresses} />
        <CourseDetailsSection course={course} progress={learningProgresses} />
      </div>
      <div className="flex flex-col gap-3 sm:hidden">
        <LearningInformation course={course} />
      </div>
      <div className="w-full lg:w-[30%]">
        <CurrentLesson courseSlug={course.slug} userCourse={userCourse} />
      </div>
    </motion.div>
  );
};

const CourseImageSection = ({
  course,
  progress,
}: { progress: number } & Pick<CourseViewProps, "course">) => (
  <div className="flex flex-col gap-3">
    <img
      src={course?.image_url}
      alt="learning-item"
      className="lg:translate-0 max-h-44 min-w-40 max-w-40 translate-y-6 rounded-lg border border-(--card-border-secondary) bg-(--course-card-bg) object-cover px-1 py-5 sm:px-3 sm:py-10"
    />
    <div className="hidden lg:block">
      <LearningProgress
        level={course.level}
        duration={course.duration}
        progress={progress}
      />
    </div>
  </div>
);

const CourseDetailsSection = ({
  course,
  progress,
}: { progress: number } & Pick<CourseViewProps, "course">) => (
  <div className="flex flex-col gap-y-2 md:gap-y-3">
    <CourseTag tag={calculateCourseTag(course)} />
    <Link to="/courses/$courseSlug" params={{ courseSlug: course.slug }}>
      <p className="inline-block text-xl md:text-[28px]">{course.name}</p>
    </Link>
    <div className="flex flex-col gap-3">
      <CourseAuthorInfo instructor={course.instructor} />
      <div className="block xs:max-w-2/3 lg:hidden">
        <LearningProgress
          level={course.level}
          duration={course.duration}
          progress={progress}
        />
      </div>
    </div>
    <div className="hidden flex-col gap-3 sm:flex">
      <LearningInformation course={course} />
    </div>
  </div>
);

const CourseAuthorInfo = memo(({ instructor }: { instructor: UserOther }) => (
  <div className="flex items-center gap-2">
    <Image
      className="h-6 w-6 rounded-full"
      src={instructor?.avatar_url || ""}
      alt="course image"
    />
    <p className="text-xs lg:text-sm">by {instructor?.name}</p>
  </div>
));

const CurrentLesson = memo(
  ({
    courseSlug,
    userCourse,
  }: {
    courseSlug: string;
    userCourse: UserCourse;
  }) => {
    const setLessonUnlock = useSetAtom(atomLessonUnlock);
    const lesson = userCourse.continue_lesson;
    const currentLesson = userCourse?.continue_lesson;
    const isRequiredUnlockLesson =
      currentLesson &&
      !userCourse?.progresses?.[currentLesson.id] &&
      currentLesson?.requirements?.keys;
    const navigate = useNavigate();

    const buttonText = () => {
      if (isRequiredUnlockLesson) return m["courses.unlock_to_continue"]();
      return m["dashboard.continue_learning"]();
    };

    return (
      <div className="flex h-max flex-col justify-between gap-5 rounded-lg border border-(--card-border-secondary) p-4 sm:flex-row sm:items-center md:p-6 lg:flex-col lg:items-start">
        <div className="flex flex-col gap-2">
          <div>
            <p className="text-sm md:text-xl md:leading-6">{lesson.name}</p>
          </div>
          <p className="text-xs md:text-sm">{lesson.description}</p>
        </div>

        <Button
          variant="secondary"
          size="lg"
          className="!px-10 rounded-full bg-(--learning-btn-bg) font-normal text-(--learning-btn-foreground) hover:bg-current/20"
          onClick={() => {
            if (isRequiredUnlockLesson) {
              setLessonUnlock(lesson);
            }
            navigate({ to: "/courses/$courseSlug", params: { courseSlug } });
          }}
        >
          {buttonText()} <ChevronRight />
        </Button>
      </div>
    );
  },
);

const LearningProgress = memo(
  ({
    duration,
    progress,
    completionDate,
    level,
  }: {
    duration: number;
    progress: number;
    completionDate?: string;
    level: string;
  }) => (
    <div className="flex flex-col items-start gap-1 text-sm lg:gap-(--size-2xs)">
      <Progress value={progress} />
      <p>{progress}% completed</p>
      {completionDate ? (
        <div>
          Completed Date:{" "}
          {new Intl.DateTimeFormat("en-GB", {
            day: "numeric",
            month: "short",
            year: "numeric",
          }).format(new Date(completionDate))}
        </div>
      ) : (
        <div className="flex flex-col items-start gap-1 sm:flex-row sm:items-center">
          <p>{level}</p>
          <p className="hidden sm:inline">-</p>
          <p>{formatDuration(duration.toString())}</p>
        </div>
      )}
    </div>
  ),
);

const LearningInformation = memo(({ course }: { course?: Course }) => {
  if (!course?.stats) return null;

  return (
    <div className="flex flex-col gap-3">
      <CourseInfo stats={course.stats} />
      <CourseShare
        course={course}
        head={
          <div>
            <p className="text-base">Share & Learn</p>
            <p className="text-xs">with your friends</p>
          </div>
        }
      />
    </div>
  );
});

const CompletedLesson = memo(
  ({ course }: { userCourse: UserCourse; course: Course }) => {
    return (
      <div className="flex flex-col gap-7 rounded-lg border border-(--card-border-secondary) p-4 md:gap-10 md:p-6">
        <div className="flex flex-wrap items-center justify-between gap-3">
          <Link
            to="/courses/$courseSlug"
            params={{
              courseSlug: course.slug,
            }}
            className="w-full flex-1"
          >
            <Button
              variant="secondary"
              size="lg"
              className="!px-8 w-full rounded-full bg-(--learning-btn-bg) font-normal text-(--learning-btn-foreground) hover:bg-current/20 lg:h-10"
            >
              Revise <ChevronRight />
            </Button>
          </Link>
          {/*<Button*/}
          {/*  variant="secondary"*/}
          {/*  size="lg"*/}
          {/*  className="!px-8 rounded-full bg-(--learning-btn-bg) font-normal text-(--learning-btn-foreground) hover:bg-current/20 lg:h-10"*/}
          {/*>*/}
          {/*  View Certificate <ChevronRight />*/}
          {/*</Button>*/}
        </div>
        <CourseShare
          course={course}
          head={
            <div>
              <p className="text-base">Share</p>
              <p className="text-xs">your achievement</p>
            </div>
          }
        />
      </div>
    );
  },
);
