import owlAiMentorImg from "@/assets/images/owls/owl-ai-mentor.png";
import { Card, CardContent } from "@/components/ui/card";
import { GradientText } from "@/components/ui/gradient-text";
import * as m from "@/paraglide/messages.js";

export default function AiMentorCard() {
  return (
    <Card className="relative border-(--card-border-secondary) p-0 shadow-none">
      <CardContent className="p-3 pr-0 md:p-4">
        <h3 className="mb-2 flex h-11 items-center font-normal text-foreground text-sm lg:text-base">
          {m["dashboard.cards.ai_mentor.dive_in_full"]()} <br />{" "}
          {m["dashboard.cards.ai_mentor.access_mode"]()}
        </h3>
        <img
          src={owlAiMentorImg}
          className="-right-4 -top-2 absolute w-28"
          alt="owl-ai-mentor"
        />
        <GradientText className="font-light sm:text-2xl">
          {m["dashboard.cards.ai_mentor.alphas_free"]()}
        </GradientText>
      </CardContent>
    </Card>
  );
}
