import { calculateAttendanceLogic } from "./calculateAttendance";

describe("Attendance Logic Test Cases", () => {
  const createTimestamp = (daysAgo: number, hour: number = 10): string => {
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    date.setHours(hour, 0, 0, 0);
    return date.toISOString();
  };

  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date("2025-06-13T14:00:00.000Z")); // Friday, June 13, 2025
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe("First time user (no previous claim)", () => {
    test("should allow claim for first time user with null lastClaimedAt", () => {
      const result = calculateAttendanceLogic(0, null);

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(1);
      expect(result.completedDays).toBe(0);
      expect(result.startDay).toBe(1);
      expect(result.nextDayToClaim).toBe(1);
    });

    test("should allow claim for first time user with undefined lastClaimedAt", () => {
      const result = calculateAttendanceLogic(0, undefined);

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(1);
      expect(result.completedDays).toBe(0);
    });

    test("should allow claim for first time user with empty string", () => {
      const result = calculateAttendanceLogic(0, "");

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(1);
      expect(result.completedDays).toBe(0);
    });
  });

  describe("Same day claim (daysDiff = 0)", () => {
    test("should not allow claim if already claimed today - same hour", () => {
      const todayTimestamp = createTimestamp(0, 10); // Today at 10:00
      const result = calculateAttendanceLogic(3, todayTimestamp);

      expect(result.canClaimToday).toBe(false);
      expect(result.isClaimedToday).toBe(true);
      expect(result.dayInSet).toBe(4); // currentStreak(3) % 6 + 1 = 4
      expect(result.completedDays).toBe(3); // 3 % 6 = 3
      expect(result.nextDayToClaim).toBe(null);
    });

    test("should not allow claim if already claimed today - different hour", () => {
      const todayEarlyMorning = createTimestamp(0, 6); // Today at 6:00 AM
      const result = calculateAttendanceLogic(5, todayEarlyMorning);

      expect(result.canClaimToday).toBe(false);
      expect(result.isClaimedToday).toBe(true);
      expect(result.dayInSet).toBe(6); // currentStreak(5) % 6 + 1 = 6
      expect(result.completedDays).toBe(5); // 5 % 6 = 5
    });

    test("should not allow claim for high streak same day", () => {
      const todayTimestamp = createTimestamp(0, 20); // Today at 8:00 PM
      const result = calculateAttendanceLogic(15, todayTimestamp);

      expect(result.canClaimToday).toBe(false);
      expect(result.isClaimedToday).toBe(true);
      expect(result.dayInSet).toBe(4); // 15 % 6 + 1 = 4 (day 4 of set 3)
      expect(result.completedDays).toBe(3); // 15 % 6 = 3
    });
  });

  describe("Next consecutive day (daysDiff = 1)", () => {
    test("should allow claim for next day - maintain streak", () => {
      const yesterdayTimestamp = createTimestamp(1, 10); // Yesterday at 10:00
      const result = calculateAttendanceLogic(4, yesterdayTimestamp);

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(5); // currentStreak(4) % 6 + 1 = 5
      expect(result.completedDays).toBe(4); // 4 % 6 = 4
      expect(result.nextDayToClaim).toBe(5);
    });

    test("should allow claim for next day - different time", () => {
      const yesterdayEarly = createTimestamp(1, 6); // Yesterday at 6:00 AM
      const result = calculateAttendanceLogic(6, yesterdayEarly);

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(1); // currentStreak(6) % 6 + 1 = 1 (new set)
      expect(result.completedDays).toBe(0); // 6 % 6 = 0
      expect(result.nextDayToClaim).toBe(1);
    });

    test("should handle streak continuation across sets", () => {
      const yesterdayTimestamp = createTimestamp(1, 15); // Yesterday at 3:00 PM
      const result = calculateAttendanceLogic(6, yesterdayTimestamp); // Complete first set

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(1); // 6 % 6 + 1 = 1 (start of new set)
      expect(result.completedDays).toBe(0); // 6 % 6 = 0
      expect(result.startDay).toBe(7); // Second set starts at day 7
      expect(result.nextDayToClaim).toBe(1);
    });

    test("should handle high streak continuation", () => {
      const yesterdayTimestamp = createTimestamp(1, 12); // Yesterday at noon
      const result = calculateAttendanceLogic(20, yesterdayTimestamp);

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(3); // 20 % 6 + 1 = 3
      expect(result.completedDays).toBe(2); // 20 % 6 = 2
      expect(result.startDay).toBe(19); // Set 4: days 19-24 (Math.floor(20/6) * 6 + 1 = 3 * 6 + 1 = 19)
    });
  });

  describe("Missed days - Reset streak (daysDiff > 1)", () => {
    test("should reset streak after missing 2 days", () => {
      const twoDaysAgo = createTimestamp(2, 10); // 2 days ago
      const result = calculateAttendanceLogic(5, twoDaysAgo);

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(1); // Reset to day 1
      expect(result.completedDays).toBe(0); // Reset to 0
      expect(result.startDay).toBe(1); // Back to first set
      expect(result.nextDayToClaim).toBe(1);
    });

    test("should reset streak after missing 3 days", () => {
      const threeDaysAgo = createTimestamp(3, 8); // 3 days ago at 8:00 AM
      const result = calculateAttendanceLogic(10, threeDaysAgo);

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(1);
      expect(result.completedDays).toBe(0);
      expect(result.startDay).toBe(1);
      expect(result.nextDayToClaim).toBe(1);
    });

    test("should reset high streak after missing days", () => {
      const fourDaysAgo = createTimestamp(4, 18); // 4 days ago at 6:00 PM
      const result = calculateAttendanceLogic(25, fourDaysAgo);

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(1);
      expect(result.completedDays).toBe(0);
      expect(result.startDay).toBe(1);
    });

    test("should reset streak after missing 1 week", () => {
      const oneWeekAgo = createTimestamp(7, 12); // 1 week ago
      const result = calculateAttendanceLogic(15, oneWeekAgo);

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(1);
      expect(result.completedDays).toBe(0);
      expect(result.startDay).toBe(1);
    });

    test("should reset streak after missing 1 month", () => {
      const oneMonthAgo = createTimestamp(30, 14); // 30 days ago
      const result = calculateAttendanceLogic(50, oneMonthAgo);

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(1);
      expect(result.completedDays).toBe(0);
      expect(result.startDay).toBe(1);
    });
  });

  describe("Edge cases and boundary conditions", () => {
    test("should handle exactly 24 hours difference", () => {
      const exactly24HoursAgo = new Date();
      exactly24HoursAgo.setHours(exactly24HoursAgo.getHours() - 24);

      const result = calculateAttendanceLogic(
        3,
        exactly24HoursAgo.toISOString(),
      );

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
    });

    test("should handle streak at set boundary (day 6)", () => {
      const yesterdayTimestamp = createTimestamp(1, 10);
      const result = calculateAttendanceLogic(5, yesterdayTimestamp); // Will become day 6

      expect(result.dayInSet).toBe(6);
      expect(result.completedDays).toBe(5);
      expect(result.startDay).toBe(1);
    });

    test("should handle streak crossing to new set (day 7)", () => {
      const yesterdayTimestamp = createTimestamp(1, 10);
      const result = calculateAttendanceLogic(6, yesterdayTimestamp); // Will start new set

      expect(result.dayInSet).toBe(1);
      expect(result.completedDays).toBe(0);
      expect(result.startDay).toBe(7);
    });

    test("should handle zero streak with yesterday claim", () => {
      const yesterdayTimestamp = createTimestamp(1, 10);
      const result = calculateAttendanceLogic(0, yesterdayTimestamp);

      expect(result.canClaimToday).toBe(true);
      expect(result.dayInSet).toBe(1);
      expect(result.completedDays).toBe(0);
    });

    test("should handle invalid date string", () => {
      const result = calculateAttendanceLogic(5, "invalid-date");

      // Should treat as no previous claim
      expect(result.canClaimToday).toBe(true);
      expect(result.dayInSet).toBe(1);
      expect(result.completedDays).toBe(0);
    });

    test("should handle future date (edge case)", () => {
      const futureDate = createTimestamp(-1, 10); // Tomorrow
      const result = calculateAttendanceLogic(5, futureDate);

      // Should reset because daysDiff would be negative
      expect(result.canClaimToday).toBe(true);
      expect(result.dayInSet).toBe(1);
      expect(result.completedDays).toBe(0);
    });
  });

  describe("Real world scenarios", () => {
    test("User claims every day for 1 week", () => {
      let currentStreak = 0;
      let lastClaimedAt: string | null = null;

      // Simulate 6 consecutive days (one complete set)
      for (let day = 6; day >= 1; day--) {
        const timestamp = createTimestamp(day - 1, 10);
        const result = calculateAttendanceLogic(currentStreak, lastClaimedAt);

        if (result.canClaimToday) {
          currentStreak++;
          lastClaimedAt = timestamp;
        }
      }

      // Final check - should be at day 6 (last day of first set)
      const finalResult = calculateAttendanceLogic(
        currentStreak - 1,
        createTimestamp(1, 10),
      );
      expect(finalResult.dayInSet).toBe(6);
      expect(finalResult.completedDays).toBe(5);
    });

    test("User has streak, misses weekend, comes back Monday", () => {
      // Friday claim (day 5 of streak)
      const fridayTimestamp = createTimestamp(5, 17); // 5 days ago (Friday evening)

      // Now it's Wednesday (5 days later)
      const result = calculateAttendanceLogic(5, fridayTimestamp);

      expect(result.canClaimToday).toBe(true);
      expect(result.dayInSet).toBe(1); // Reset
      expect(result.completedDays).toBe(0); // Reset
    });

    test("User with original data from issue", () => {
      // Using the original problematic data
      const result = calculateAttendanceLogic(0, "2025-06-12T06:46:14.211454Z");

      expect(result.canClaimToday).toBe(true);
      expect(result.isClaimedToday).toBe(false);
      expect(result.dayInSet).toBe(1);
    });
  });
});
