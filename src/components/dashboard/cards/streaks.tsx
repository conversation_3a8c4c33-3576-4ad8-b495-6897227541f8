import { Maximize2 } from "lucide-react";
import StreakIcon from "@/assets/images/streak.svg";
import { Card, CardContent } from "@/components/ui/card";

export default function StreaksCard() {
  return (
    <Card className="border-(--card-border-secondary) p-0">
      <CardContent className="p-4">
        <div className="mb-2 flex items-center justify-between">
          <div>
            <h3 className="font-normal text-2xl leading-9 md:text-3xl">
              2 Streaks
            </h3>
            <div className="mt-1.5 text-xs">
              Get 1 Streak by completing 1 lesson
            </div>
          </div>
          <div className="flex h-10 w-10 items-center justify-center rounded-(--size-2xs) border border-(--card-border-muted)">
            <Maximize2 className="h-6 w-6" />
          </div>
        </div>
        <div className="grid grid-cols-6 gap-3 md:gap-5">
          {[...Array(6).keys()].map((day) => (
            <div key={day} className="flex flex-col items-center">
              <div
                className={`flex h-10 w-10 items-center justify-center rounded-full ${day <= 2 ? "border-(--border-secondary) border-2 bg-(--background-secondary) text-primary-foreground" : "border border-(--border-disabled-secondary) bg-(--background-disabled-secondary)"}`}
              >
                <img src={StreakIcon} alt="streak" />
              </div>
              <div
                className={`mt-2.5 text-xs ${day <= 2 ? "" : "text-(--text-disabled)"}`}
              >
                Day {day}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
