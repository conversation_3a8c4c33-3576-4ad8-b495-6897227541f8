import { useMutation } from "@tanstack/react-query";
import { AnimationSequence, useAnimate } from "framer-motion";
import { useCallback, useMemo, useState } from "react";
import { toast } from "sonner";
import {
  EmojiDay1Icon,
  EmojiDay2Icon,
  EmojiDay3Icon,
  EmojiDay4Icon,
  EmojiDay5Icon,
  EmojiDay6Icon,
  StarIcon,
} from "@/components/icons";
import { Card, CardContent } from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import * as m from "@/paraglide/messages.js";
import { checkin } from "@/services/users";
import { User } from "@/types/auth";
import { calculateAttendanceLogic } from "./calculateAttendance";

const EMOJI_ICONS = [
  EmojiDay1Icon,
  EmojiDay2Icon,
  EmojiDay3Icon,
  EmojiDay4Icon,
  EmojiDay5Icon,
  EmojiDay6Icon,
] as const;

export const DAYS_IN_SET = 6;
const DAILY_REWARD_XP = 5;

interface DayState {
  globalDay: number;
  isCompleted: boolean;
  isCurrentDay: boolean;
  isLockedDay: boolean;
  EmojiComponent: typeof EmojiDay1Icon;
}

export interface AttendanceLogic {
  startDay: number;
  dayInSet: number;
  nextDayToClaim: number | null;
  canClaimToday: boolean;
  isClaimedToday: boolean;
  completedDays: number;
}

interface DailyAttendanceCardProps {
  profile: User;
  refetchProfile: () => void;
}

const generateDayStates = (attendanceLogic: AttendanceLogic): DayState[] => {
  return Array.from({ length: DAYS_IN_SET }, (_, index) => {
    const dayNumber = index + 1;
    const globalDay = attendanceLogic.startDay + index;
    const isCompleted = dayNumber <= attendanceLogic.completedDays;
    const isCurrentDay = dayNumber === attendanceLogic.nextDayToClaim;
    const isLockedDay = dayNumber > attendanceLogic.completedDays;

    return {
      dayNumber,
      globalDay,
      isCompleted,
      isCurrentDay,
      isLockedDay,
      EmojiComponent: EMOJI_ICONS[index],
    };
  });
};

const DayItem = ({
  day,
  isLastDay,
  isPending,
  onClaim,
  isNextDay,
}: {
  isNextDay?: boolean;
  day: DayState;
  isLastDay: boolean;
  isPending: boolean;
  onClaim: () => void;
}) => {
  const { globalDay, isCompleted, isCurrentDay, isLockedDay, EmojiComponent } =
    day;

  const getDayClassName = (): string => {
    const baseClasses =
      "relative z-1 flex h-10 w-10 cursor-pointer items-center justify-center rounded-full transition-all duration-200";

    if (isCompleted) {
      return `${baseClasses} border-2 border-[#00FF9F] text-primary-foreground`;
    }

    if (isCurrentDay) {
      const interactiveClasses = isPending
        ? "cursor-not-allowed opacity-50"
        : "transform cursor-pointer hover:scale-105";
      return `${baseClasses} bg-primary ${interactiveClasses}`;
    }

    return `${baseClasses} border bg-(--background-disabled-secondary) ${isLastDay ? "border-2" : ""}`;
  };

  const getTooltipText = (): string => {
    if (isCurrentDay) return `Claim your ${DAILY_REWARD_XP} XP today!`;
    if (isNextDay) return `Come back tomorrow for ${DAILY_REWARD_XP} XP!`;
    return "";
  };

  const shouldShowTooltip = isCurrentDay || isNextDay;

  const starPositions = [
    { x: 30, y: -8, scale: 1.2 },
    { x: 0, y: -11, scale: 1.4 },
    { x: 20, y: -12, scale: 0.8 },
    { x: -8, y: -4, scale: 1.0 },
    { x: 40, y: -4, scale: 1.1 },
    { x: -12, y: 2, scale: 0.6 },
  ];

  const [scope, animate] = useAnimate();
  const [isAnimating, setIsAnimating] = useState(false);

  const playAnimation = useCallback(() => {
    if (isAnimating) return;
    setIsAnimating(true);
    const stars = Array.from({ length: 6 });
    const starsAnimation: AnimationSequence = stars.map((_, index) => [
      `.star-${index}`,
      {
        x: starPositions[index].x,
        y: starPositions[index].y,
        scale: starPositions[index].scale,
        opacity: 0.8,
      },
      {
        duration: 0.3,
        at: "<",
      },
    ]);

    const startsFadeOut: AnimationSequence = stars.map((_, index) => [
      `.star-${index}`,
      {
        opacity: 0,
      },
      {
        duration: 0.05,
        at: "1",
      },
    ]);

    const starsReset: AnimationSequence = stars.map((_, index) => [
      `.star-${index}`,
      {
        x: 0,
        y: 0,
      },
      {
        duration: 0,
      },
    ]);

    animate([
      ...starsReset,
      [".letter", { y: -20, opacity: 1 }, { duration: 0.3, delay: 0.2 }],
      ...starsAnimation,
      ...startsFadeOut,
      [
        ".letter",
        { y: -20, opacity: 0 },
        {
          duration: 0.3,
          delay: 1,
          at: "1",
          onComplete: () => setIsAnimating(false),
        },
      ],
    ]).then(() => setIsAnimating(false));
  }, [animate, isAnimating]);

  const handleClaim = () => {
    if (isAnimating) return;
    onClaim();
    playAnimation();
  };

  return (
    <div className="relative z-0 flex flex-col items-center" ref={scope}>
      <div
        onClick={isCurrentDay && !isPending ? handleClaim : undefined}
        className={getDayClassName()}
      >
        {isLastDay && (
          <>
            <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 flex size-[130%] items-center justify-center rounded-full border border-[#FFE495]" />
            <div className="-top-2 -right-1 absolute rounded-xs bg-[#FFE495] px-1 text-[9px]">
              2x
            </div>
          </>
        )}

        <Tooltip>
          <TooltipTrigger>
            <div className="relative flex size-6 items-center justify-center">
              <EmojiComponent
                className={`z-1 h-6 w-6 cursor-pointer ${isLockedDay ? "opacity-60" : ""}`}
                color={isCompleted ? "#00FF9F" : undefined}
              />
              {isCurrentDay && !isPending && (
                <span className="absolute z-0 inline-flex size-7 animate-ping rounded-full bg-primary" />
              )}
            </div>
          </TooltipTrigger>
          {shouldShowTooltip && (
            <TooltipContent>{getTooltipText()}</TooltipContent>
          )}
        </Tooltip>
      </div>

      <div
        className={`mt-2.5 text-xs ${!isLockedDay ? "" : "text-(--text-disabled)"}`}
      >
        Day {globalDay}
      </div>

      <span
        aria-hidden
        className="letter -translate-x-1/2 absolute top-0 left-1/2 z-0 w-20 text-center font-semibold text-(--border-correct) text-md opacity-0"
      >
        +{DAILY_REWARD_XP} XP
      </span>

      <div aria-hidden className="pointer-events-none absolute inset-0">
        {Array.from({ length: 6 }).map((_, index) => (
          <StarIcon
            key={index.toString()}
            className={`star-${index} color-(--border-correct) absolute left-0 size-2 text-(--border-correct) opacity-1`}
          />
        ))}
      </div>
    </div>
  );
};

export default function DailyAttendanceCard({
  profile,
  refetchProfile,
}: DailyAttendanceCardProps) {
  const attendanceLogic = useMemo(
    () =>
      calculateAttendanceLogic(
        profile.current_streak,
        profile.last_streak_claimed_at,
      ),
    [profile.current_streak, profile.last_streak_claimed_at],
  );

  const dayStates = useMemo(
    () => generateDayStates(attendanceLogic),
    [attendanceLogic],
  );

  const checkinMutation = useMutation({
    mutationFn: checkin,
    onSuccess: () => {
      refetchProfile();
    },
  });

  const handleClaimReward = useCallback(() => {
    if (!attendanceLogic.canClaimToday) {
      toast.error(m["ui.already_claimed"]());
      return;
    }

    checkinMutation.mutate();
  }, [attendanceLogic.canClaimToday, checkinMutation]);

  return (
    <Card className="border-(--card-border-secondary) p-0 shadow-none">
      <CardContent className="relative p-4">
        <header className="mb-2 flex flex-col items-start md:flex-row md:items-end md:gap-4">
          <h3 className="font-normal text-2xl leading-9 md:text-[28px]">
            Free {DAILY_REWARD_XP} XP Daily
          </h3>
          <span className="text-xs leading-5">Double up weekly!</span>
        </header>

        <div className="grid grid-cols-6 gap-3 md:gap-5">
          {dayStates.map((day, index) => (
            <DayItem
              isNextDay={index === attendanceLogic.dayInSet - 1}
              key={day.globalDay}
              day={day}
              isLastDay={index === DAYS_IN_SET - 1}
              isPending={checkinMutation.isPending}
              onClaim={handleClaimReward}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
