import { Link } from "@tanstack/react-router";
import { motion } from "framer-motion";
import CheckIcon from "@/assets/images/check.svg";
import { Image } from "@/components/ui/image";
import { useCourse } from "@/services/courses";
import { UserCourse } from "@/types/courses";

export const LearningCourse = ({
  learningCourse,
}: {
  learningCourse: UserCourse[];
}) => {
  if (!learningCourse.length) {
    return null;
  }
  return (
    <>
      <motion.div
        initial={{ scaleX: 0 }}
        animate={{ scaleX: 1 }}
        transition={{ duration: 0.4 }}
        className="border-(--line-border) border-t"
      />
      <div className="px-5 lg:px-8">
        <motion.h4
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="mb-4 text-xl"
        >
          Continue learning
        </motion.h4>
        <div className="flex min-h-20 flex-col gap-5 lg:flex-row">
          {learningCourse.map(
            (userCourse, index) =>
              index < 2 && (
                <motion.div
                  key={userCourse.course_id}
                  initial={{ opacity: 0, y: 15 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.4,
                    delay: 0.2 + index * 0.1,
                    ease: "easeOut",
                  }}
                >
                  <LearningItem userCourse={userCourse} />
                </motion.div>
              ),
          )}
        </div>
      </div>
    </>
  );
};

const LearningItem = ({ userCourse }: { userCourse: UserCourse }) => {
  const { data: course } = useCourse(userCourse.course_id);

  if (!course) {
    return null;
  }
  return (
    <Link
      to={`/courses/$courseSlug`}
      params={{ courseSlug: course.slug }}
      className="flex flex-1 cursor-pointer items-center gap-2.5"
    >
      <div>
        <div className="flex h-20 w-20 items-center justify-center rounded-xl border border-(--card-border) px-1">
          <Image
            src={course.image_url}
            alt="owl-teach"
            className="object-cover"
          />
        </div>
      </div>
      <div className="flex flex-col">
        <p className="text-sm">{course.name}</p>
        <div className="mt-1 xs:flex hidden items-center gap-1.5 rounded-lg py-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full border bg-(--card-tag-color) ">
            <img
              src={course.instructor.avatar_url}
              alt="owl"
              className="h-4 w-4"
            />
          </div>
          <p className="text-xs">by {course.instructor.name}</p>
          <div>
            <img src={CheckIcon} alt="check" className="size-3" />
          </div>
        </div>
      </div>
    </Link>
  );
};
