import ExplorerLeagueActiveImg from "@/assets/images/league/explorer-active.png";
import ExplorerLeagueImg from "@/assets/images/league/explorer-league.png";
import LearnerLeagueActiveImg from "@/assets/images/league/learner-active.png";
import LearnerLeagueImg from "@/assets/images/league/learner-league.png";
import LockLeagueImg from "@/assets/images/league/lock-league.png";
import StrategistLeagueActiveImg from "@/assets/images/league/strategist-active.png";
import StrategistLeagueImg from "@/assets/images/league/strategist-league.png";
import ThinkerLeagueActiveImg from "@/assets/images/league/thinker-active.png";
import ThinkerLeagueImg from "@/assets/images/league/thinker-league.png";
import { LeaderboardType } from "@/types/leaderboard";

export const LEAGUE_ORDER = [
  LeaderboardType.LEARNER,
  LeaderboardType.EXPLORER,
  LeaderboardType.THINKER,
  LeaderboardType.STRATEGIST,
  LeaderboardType.MASTERMIND,
  LeaderboardType.AILUMINATI,
] as const;

export interface LeagueImageConfig {
  src: string;
  alt: string;
  type: LeaderboardType;
  activeSrc: string;
  name: string;
}

export const LEAGUE_IMAGES: LeagueImageConfig[] = [
  {
    src: LearnerLeagueImg,
    alt: "Learner League",
    type: LeaderboardType.LEARNER,
    activeSrc: LearnerLeagueActiveImg,
    name: "Learner",
  },
  {
    src: ExplorerLeagueImg,
    alt: "Explorer League",
    type: LeaderboardType.EXPLORER,
    activeSrc: ExplorerLeagueActiveImg,
    name: "Explorer",
  },
  {
    src: ThinkerLeagueImg,
    alt: "Thinker League",
    type: LeaderboardType.THINKER,
    activeSrc: ThinkerLeagueActiveImg,
    name: "Thinker",
  },
  {
    src: StrategistLeagueImg,
    alt: "Strategist League",
    type: LeaderboardType.STRATEGIST,
    activeSrc: StrategistLeagueActiveImg,
    name: "Strategist",
  },
  {
    src: LockLeagueImg,
    alt: "Mastermind League",
    type: LeaderboardType.MASTERMIND,
    activeSrc: LockLeagueImg,
    name: "Mastermind",
  },
  {
    src: LockLeagueImg,
    alt: "AIluminati League",
    type: LeaderboardType.AILUMINATI,
    activeSrc: LockLeagueImg,
    name: "AIluminati",
  },
];

export const leagueConfig = {
  order: LEAGUE_ORDER,
  images: LEAGUE_IMAGES,
  visibleCount: 5,
} as const;

export const getVisibleLeagues = (activeLeague: LeaderboardType | null) => {
  const currentIndex = activeLeague ? LEAGUE_ORDER.indexOf(activeLeague) : 0;
  const totalLeagues = LEAGUE_ORDER.length;
  const visibleCount = leagueConfig.visibleCount;

  let startIndex: number;
  let endIndex: number;

  if (currentIndex <= 1) {
    startIndex = 0;
    endIndex = visibleCount;
  } else if (currentIndex >= totalLeagues - 2) {
    startIndex = totalLeagues - visibleCount;
    endIndex = totalLeagues;
  } else {
    startIndex = currentIndex - 2;
    endIndex = currentIndex + 3;
  }

  return LEAGUE_ORDER.slice(startIndex, endIndex).map(
    (type) => LEAGUE_IMAGES.find((img) => img.type === type)!,
  );
};
