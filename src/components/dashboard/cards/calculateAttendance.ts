const DAYS_IN_SET = 6;

export const calculateAttendanceLogic = (
  currentStreak: number = 0,
  lastClaimedAt?: string | null,
) => {
  const today = new Date();

  if (!lastClaimedAt) {
    return {
      startDay: 1,
      dayInSet: 1,
      nextDayToClaim: 1,
      canClaimToday: true,
      isClaimedToday: false,
      completedDays: 0,
    };
  }

  const lastClaimedDate = new Date(lastClaimedAt);

  // Use date-only comparison to avoid timezone/time issues
  const todayDateOnly = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate(),
  );
  const lastClaimedDateOnly = new Date(
    lastClaimedDate.getFullYear(),
    lastClaimedDate.getMonth(),
    lastClaimedDate.getDate(),
  );

  const daysDiff = Math.floor(
    (todayDateOnly.getTime() - lastClaimedDateOnly.getTime()) /
      (1000 * 60 * 60 * 24),
  );

  // Already claimed today
  if (daysDiff === 0) {
    const currentSet = Math.floor(currentStreak / DAYS_IN_SET);
    const startDay = currentSet * DAYS_IN_SET + 1;
    const dayInSet = (currentStreak % DAYS_IN_SET) + 1;
    const completedDays = currentStreak % DAYS_IN_SET;

    return {
      startDay,
      dayInSet,
      nextDayToClaim: null,
      canClaimToday: false,
      isClaimedToday: true,
      completedDays,
    };
  }

  // Next consecutive day - can claim and maintain streak
  if (daysDiff === 1) {
    const currentSet = Math.floor(currentStreak / DAYS_IN_SET);
    const startDay = currentSet * DAYS_IN_SET + 1;
    const dayInSet = (currentStreak % DAYS_IN_SET) + 1;
    const completedDays = currentStreak % DAYS_IN_SET;

    return {
      startDay,
      dayInSet,
      nextDayToClaim: dayInSet,
      canClaimToday: true,
      isClaimedToday: false,
      completedDays,
    };
  }

  // Missed days (daysDiff > 1) - reset streak
  const resetStreak = 0;
  const resetCurrentSet = Math.floor(resetStreak / DAYS_IN_SET);
  const resetStartDay = resetCurrentSet * DAYS_IN_SET + 1;
  const resetDayInSet = (resetStreak % DAYS_IN_SET) + 1;
  const resetCompletedDays = resetStreak % DAYS_IN_SET;

  return {
    startDay: resetStartDay,
    dayInSet: resetDayInSet,
    nextDayToClaim: resetDayInSet,
    canClaimToday: true,
    isClaimedToday: false,
    completedDays: resetCompletedDays,
  };
};
