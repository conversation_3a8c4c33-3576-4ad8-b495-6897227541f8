import { AnimatePresence, motion } from "framer-motion";
import { Lock, Maximize2, Minimize2, Trophy } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { EmptyData } from "@/components/empty-data";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { UserAvatar } from "@/components/user-avatar";
import { useProfile } from "@/services/auth";
import { fetchLeaderboard, useMyLeaderboard } from "@/services/leaderboard";
import {
  Leaderboard,
  LeaderboardType,
  MyLeaderboard,
} from "@/types/leaderboard";
import { cn } from "@/utils/cn";
import { useInfiniteScroll } from "@/utils/use-infinite-scroll";
import { useIsMobile } from "@/utils/use-mobile";
import { getVisibleLeagues, leagueConfig } from "./league-config";

const useLeaderboardLogic = (expandLeaderboard: boolean) => {
  const { data: myLeaderboard } = useMyLeaderboard();
  const [activeLeague, setActiveLeague] = useState<LeaderboardType | null>(
    null,
  );

  useEffect(() => {
    if (!myLeaderboard) return;
    if (myLeaderboard?.rank && myLeaderboard.rank >= 1 && myLeaderboard.type) {
      setActiveLeague(myLeaderboard.type);
    } else {
      setActiveLeague(LeaderboardType.LEARNER);
    }
  }, [myLeaderboard]);

  const {
    data: leaderboards,
    refetch,
    isFetching,
    isError,
    ref: infiniteScrollRef,
    isInitialLoading: pendingLeaderboard,
  } = useInfiniteScroll<Leaderboard>({
    callbackFn: (pageParam) => {
      const limit = expandLeaderboard ? 15 : 5;
      const offset = (pageParam - 1) * limit;
      return fetchLeaderboard({
        type: activeLeague || undefined,
        limit,
        offset,
      });
    },
    limit: expandLeaderboard ? undefined : 1,
    queryKey: ["leaderboard-infinite", activeLeague || ""],
  });

  const handleLeagueClick = useCallback(
    (type: LeaderboardType) => {
      setActiveLeague(type);
      setTimeout(() => {
        refetch();
      }, 100);
    },
    [refetch],
  );

  const currentLeagueIndex = myLeaderboard?.type
    ? leagueConfig.order.indexOf(myLeaderboard.type)
    : 0;

  const visibleLeagues = getVisibleLeagues(activeLeague);
  const leagueImagesWithLockStatus = visibleLeagues.map((img) => ({
    ...img,
    locked: leagueConfig.order.indexOf(img.type) > currentLeagueIndex,
  }));

  return {
    activeLeague,
    myLeaderboard,
    leaderboards,
    pendingLeaderboard,
    isFetching,
    isError,
    refetch,
    handleLeagueClick,
    leagueImagesWithLockStatus,
    infiniteScrollRef,
  };
};

const ANIMATION_CONFIG = {
  containerVariants: {
    collapsed: {
      height: "100%",
      transition: {
        duration: 0.2,
        ease: "easeInOut",
        when: "afterChildren" as const,
      },
    },
    expanded: {
      height: "80%",
      transition: {
        duration: 0.2,
        ease: "easeInOut",
        when: "beforeChildren" as const,
      },
    },
  },
  itemVariants: {
    collapsed: {
      opacity: 0,
      height: 0,
      transition: { duration: 0.2, ease: "easeInOut" },
    },
    expanded: {
      opacity: 1,
      height: "auto",
      transition: { duration: 0.3, ease: "easeOut" },
    },
  },
} as const;

// Layout constants
const LAYOUT_CONFIG = {
  skeletonCount: { expanded: 8, collapsed: 5 },
  leaderboardLimits: { expanded: 50, collapsed: 5 },
  gridCols: 5,
  fixedPositionThreshold: 12,
} as const;

export default function LeaderboardCard({
  expandLeaderboard,
  setExpandLeaderboard,
}: {
  expandLeaderboard: boolean;
  setExpandLeaderboard: (isExpanded: boolean) => void;
}) {
  const isMobile = useIsMobile();
  const { data: profile } = useProfile();
  const {
    activeLeague,
    myLeaderboard,
    leaderboards,
    pendingLeaderboard,
    isFetching,
    refetch,
    handleLeagueClick,
    leagueImagesWithLockStatus,
    infiniteScrollRef,
  } = useLeaderboardLogic(expandLeaderboard);

  const showMyLeaderboardFixed =
    myLeaderboard?.rank &&
    myLeaderboard.rank > LAYOUT_CONFIG.gridCols &&
    leaderboards &&
    myLeaderboard.type === activeLeague;

  const handleExpandClick = useCallback(() => {
    setExpandLeaderboard(!expandLeaderboard);
    setTimeout(() => {
      refetch();
    }, 100);
  }, [expandLeaderboard, setExpandLeaderboard, refetch]);

  const displayItems = leaderboards || [];

  if (!profile) {
    return null;
  }

  return (
    <Card
      className={cn(
        "relative h-full border-(--card-border-secondary) p-0 shadow-none",
      )}
    >
      <CardContent className="h-full p-4">
        <LeaderboardHeader
          expandLeaderboard={expandLeaderboard}
          onExpandClick={handleExpandClick}
        />

        <LeagueSelector
          leagues={leagueImagesWithLockStatus}
          activeLeague={activeLeague}
          onLeagueClick={handleLeagueClick}
          isMobile={isMobile}
        />

        <motion.div
          className={cn(
            "mt-2 space-y-2 md:space-y-3",
            expandLeaderboard
              ? "max-h-[80dvh] min-h-0 flex-1 overflow-y-auto sm:h-auto"
              : "h-full overflow-y-auto",
            showMyLeaderboardFixed && expandLeaderboard && "pb-16",
          )}
          variants={ANIMATION_CONFIG.containerVariants}
          animate={expandLeaderboard ? "expanded" : "collapsed"}
        >
          {pendingLeaderboard ? (
            Array.from({
              length: expandLeaderboard
                ? LAYOUT_CONFIG.skeletonCount.expanded
                : LAYOUT_CONFIG.skeletonCount.collapsed,
            }).map((_, index) => (
              <UserRowSkeleton key={`skeleton-${index.toString()}`} />
            ))
          ) : displayItems.length === 0 && leaderboards !== undefined ? (
            <EmptyData
              title="No players in this league"
              description="Be the first to join this league and start competing!"
              icon={<Trophy className="h-12 w-12" />}
              className="py-12"
            />
          ) : (
            <div>
              {displayItems.map((user) => (
                <motion.div
                  key={user?.user_id || ""}
                  variants={ANIMATION_CONFIG.itemVariants}
                  initial="collapsed"
                  animate="expanded"
                  exit="collapsed"
                  layout
                >
                  <UserRow user={user} myLeaderboard={myLeaderboard} />
                </motion.div>
              ))}
              {expandLeaderboard && (
                <>
                  <div ref={infiniteScrollRef} className="h-4" />
                  {isFetching &&
                    !pendingLeaderboard &&
                    Array.from({
                      length: 2,
                    }).map((_, index) => (
                      <UserRowSkeleton key={`skeleton-${index.toString()}`} />
                    ))}
                </>
              )}
            </div>
          )}
        </motion.div>

        <AnimatePresence>
          {showMyLeaderboardFixed && (
            <motion.div
              className="-translate-x-1/2 absolute right-0 bottom-0 left-1/2 w-full px-4 py-2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <UserRow
                user={{
                  ...myLeaderboard,
                  avatar_url: profile.avatar_url,
                  name: profile.name,
                }}
                myLeaderboard={myLeaderboard}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
}

const LeaderboardHeader = ({
  expandLeaderboard,
  onExpandClick,
}: {
  expandLeaderboard: boolean;
  onExpandClick: () => void;
}) => (
  <div className="mb-2 flex flex-shrink-0 justify-between">
    <div>
      <h3 className="font-normal text-2xl leading-9 md:text-[28px]">
        Hall of Fame
      </h3>
      <div className="mt-1 mb-2 text-xs">
        Where top minds rise with every XP earned
      </div>
    </div>
    <Button
      variant="ghost"
      className="cursor-pointer items-center justify-center rounded-(--size-2xs) border border-(--card-border-muted) md:flex md:size-10"
      onClick={onExpandClick}
    >
      {expandLeaderboard ? (
        <Minimize2 className="size-6" />
      ) : (
        <Maximize2 className="size-6" />
      )}
    </Button>
  </div>
);

const LeagueSelector = ({
  leagues,
  activeLeague,
  onLeagueClick,
  isMobile,
}: {
  leagues: Array<(typeof leagueConfig.images)[0] & { locked: boolean }>;
  activeLeague: LeaderboardType | null;
  onLeagueClick: (type: LeaderboardType) => void;
  isMobile: boolean;
}) => (
  <div
    className={cn(
      "mb-1 grid h-16 flex-shrink-0 grid-cols-5 items-center gap-3 md:gap-4",
    )}
  >
    {leagues.map((img, index) => (
      <LeagueImage
        key={img.type + index.toString()}
        index={index}
        img={img}
        isActive={img.type === activeLeague}
        onClick={() => onLeagueClick(img.type)}
        isMobile={isMobile}
      />
    ))}
  </div>
);

const UserRow = ({
  user,
  myLeaderboard,
}: {
  user?: Leaderboard;
  myLeaderboard?: MyLeaderboard;
}) => {
  if (!user) {
    return null;
  }
  const isCurrentUser = user?.user_id === myLeaderboard?.user_id;

  return (
    <div
      key={user.user_id || user.rank}
      className={cn(
        "flex items-center justify-between px-2.5 py-1.5",
        isCurrentUser &&
          "sticky bottom-0 rounded-(--size-2xs) bg-(--background-active)",
      )}
    >
      <div className="flex items-center">
        <div
          className={cn(
            "w-7 text-[#686868] text-sm",
            isCurrentUser && "text-(--text-second-foreground)",
          )}
        >
          {user.rank}
        </div>
        <div className="flex items-center">
          <UserAvatar
            url={user.avatar_url}
            username={user.name}
            className={`${isCurrentUser && "h-10 w-10"}`}
          />
          <div
            className={cn(
              "line-clamp-1 max-w-40 overflow-hidden text-ellipsis text-(--text-disabled) text-sm",
              isCurrentUser && "text-(--text-second-foreground)",
            )}
          >
            {user.name}
            {isCurrentUser && (
              <Badge
                variant="outline"
                className="ml-1 bg-(--background-second-foreground) font-bold text-primary-foreground leading-4 [font-size:var(--size-2xs)]"
              >
                YOU
              </Badge>
            )}
          </div>
        </div>
      </div>

      <div
        className={cn(
          "min-w-14 text-(--text-disabled) text-sm",
          isCurrentUser && "text-(--text-second-foreground)",
        )}
      >
        {user.points} XP
      </div>
    </div>
  );
};

const LeagueImage = ({
  index,
  img,
  isActive,
  onClick,
  isMobile = false,
}: {
  img: (typeof leagueConfig.images)[0] & { locked: boolean };
  index: number;
  isActive: boolean;
  onClick: () => void;
  isMobile?: boolean;
}) => {
  if (!img.src) {
    return (
      <div
        key={`league-${index}`}
        className={cn(
          "mx-auto flex cursor-pointer items-center justify-center rounded-full text-center font-medium transition-transform duration-200 hover:scale-110",
          isMobile ? "h-10 w-10 text-[10px]" : "h-12 w-auto text-xs",
          isActive
            ? "scale-120 bg-(--background-active) text-(--text-second-foreground)"
            : "scale-100 bg-(--background-muted) text-(--text-disabled)",
          img.locked && "opacity-50",
        )}
        onClick={onClick}
      >
        {isMobile ? img.name.charAt(0) : img.name}
        {img.locked && (
          <Lock className="absolute top-0 right-0 h-3 w-3 text-gray-500" />
        )}
      </div>
    );
  }

  return (
    <div className="relative mx-auto" key={`league-${index}`}>
      <img
        src={isActive ? img.activeSrc : img.src}
        alt={img.alt}
        className={cn(
          "cursor-pointer rounded-md transition-all duration-200 hover:scale-110",
          isMobile ? "max-w-10" : "max-w-13",
          isActive ? "scale-125" : "scale-100",
          img.locked ? "opacity-50" : "opacity-100",
        )}
        onClick={onClick}
      />
      {img.locked && (
        <div className="-top-1 -right-1 absolute flex h-4 w-4 items-center justify-center rounded-full bg-white">
          <Lock className="h-2.5 w-2.5 text-gray-500" />
        </div>
      )}
    </div>
  );
};

const UserRowSkeleton = () => (
  <div className="flex items-center justify-between px-2.5 py-1.5">
    <div className="flex items-center space-x-3">
      <Skeleton className="h-4 w-6" />
      <div className="flex items-center space-x-2.5">
        <Skeleton className="h-8 w-8 rounded-full" />
        <Skeleton className="h-4 w-24" />
      </div>
    </div>
    <Skeleton className="h-4 w-12" />
  </div>
);
