import { ButtonCopy } from "@/components/button-copy";
import { Card, CardContent } from "@/components/ui/card";
import { GradientText } from "@/components/ui/gradient-text";
import { User } from "@/types/auth";
import { BASE_URL } from "@/utils";
import * as m from "@/paraglide/messages.js";

export default function InviteFriendCard({ profile }: { profile: User }) {
  const { username = "", ref_code = "" } = profile;
  const refLink = `${BASE_URL}?invite=${username || ref_code || ""}`;

  return (
    <Card className="border-(--card-border-secondary) p-0 shadow-none">
      <CardContent className="flex h-full flex-col justify-between p-3 md:p-4">
        <div className="mb-1">
          <h3 className="text-base text-black md:text-xl">
            {m["dashboard.cards.invite_friend.title"]()}
          </h3>
          <p className="mt-1 text-xs">
            {m["dashboard.cards.invite_friend.description"]()}
          </p>
        </div>
        <div className="flex flex-wrap items-center justify-between">
          <GradientText className="flex-1 truncate font-normal text-base md:text-xl">
            /{username ? username : ref_code}
          </GradientText>
          <ButtonCopy content={refLink} />
        </div>
      </CardContent>
    </Card>
  );
}
