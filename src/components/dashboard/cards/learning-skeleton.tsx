import { Skeleton } from "@/components/ui/skeleton";

export const LearningCourseSkeleton = () => {
  return (
    <>
      <Skeleton className="h-px w-full bg-gray-200" />
      <div className="px-5 lg:px-14">
        <Skeleton className="mb-4 h-6 w-48 rounded-lg bg-gray-200" />
        <div className="flex flex-col gap-5 lg:flex-row">
          {Array.from({ length: 2 }).map((_, index) => (
            <LearningItemSkeleton key={index.toString()} />
          ))}
        </div>
      </div>
    </>
  );
};

export const LearningItemSkeleton = () => {
  return (
    <div className="flex flex-1 gap-2.5">
      <Skeleton className="h-20 w-20 rounded-2xl bg-gray-200" />
      <div className="flex flex-col">
        <Skeleton className="h-4 w-32 rounded-lg bg-gray-200" />
        <div className="mt-1 flex items-center gap-1.5 rounded-lg py-1">
          <Skeleton className="h-6 w-6 rounded-full bg-gray-200" />
          <div className="flex flex-col">
            <Skeleton className="h-3 w-20 rounded-lg bg-gray-200" />
            <Skeleton className="mt-1 h-2 w-16 rounded-lg bg-gray-200" />
          </div>
          <Skeleton className="h-4 w-4 rounded-lg bg-gray-200" />
        </div>
      </div>
    </div>
  );
};
