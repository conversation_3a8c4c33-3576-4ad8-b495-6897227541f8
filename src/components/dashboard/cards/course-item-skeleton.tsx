import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/utils/cn";

export const CourseItemSkeleton = ({
  isEmptyLearningCourse,
}: {
  isEmptyLearningCourse: boolean;
}) => {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center gap-6 lg:gap-8",
        isEmptyLearningCourse && "pt-6 lg:gap-16",
      )}
    >
      <div
        className={cn(
          "flex w-full flex-col items-center justify-center gap-3",
          isEmptyLearningCourse && "gap-8",
        )}
      >
        <div
          className={cn(
            "flex w-full flex-col items-center justify-center gap-2",
            isEmptyLearningCourse && "gap-3",
          )}
        >
          <Skeleton className="h-5 w-20 rounded-lg bg-gray-200" />
          <Skeleton className="h-7 w-2/3 max-w-[380px] rounded-lg bg-gray-200" />
        </div>
        <Skeleton className="my-2 h-8 w-28 rounded-lg bg-gray-200" />
        <div className="flex flex-col items-center justify-center gap-6 lg:flex-row lg:gap-8">
          <Skeleton className="h-44 w-44 rounded-2xl bg-gray-200" />
          <div className="flex flex-col items-center justify-center gap-4 lg:items-start">
            <Skeleton className="h-5 w-32 rounded-lg bg-gray-200" />
            <Skeleton className="h-14 w-[18dvw] max-w-[20rem] rounded-lg bg-gray-200" />
            <Skeleton className="h-5 w-40 rounded-lg bg-gray-200" />
          </div>
        </div>
      </div>
      <Skeleton className="h-10 w-full max-w-[70%] rounded-full bg-gray-200" />
    </div>
  );
};
