import { useRouter } from "@tanstack/react-router";
import { ArrowRight } from "lucide-react";
import { useState } from "react";
import { Categories } from "src/components/categories";
import CourseAuthor from "@/components/course/course-author";
import CourseInfo from "@/components/course/course-info";
import CourseRating from "@/components/course/course-rating";
import { CourseTag, calculateCourseTag } from "@/components/course/course-tag";
import { CourseItemSkeleton } from "@/components/dashboard/cards/course-item-skeleton";
import { LearningCourse } from "@/components/dashboard/cards/learning-course";
import { LearningCourseSkeleton } from "@/components/dashboard/cards/learning-skeleton";
import { ShadowButton } from "@/components/shadow-button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Image } from "@/components/ui/image";
import { useCourses } from "@/services/courses";
import { useLearningCourses } from "@/services/my-learning";
import { Category } from "@/types/app";
import { cn } from "@/utils/cn";
import { parseCourseDescription } from "@/utils/course";

interface CourseCardProps {
  categories: Category[];
}

export default function CourseCard({ categories }: CourseCardProps) {
  const router = useRouter();
  const { data: learningCourse, isFetching: isLearningLoading } =
    useLearningCourses({
      completed: false,
      limit: 2,
    });
  const isEmptyLearningCourse = !isLearningLoading && !learningCourse?.length;
  const [currentCategoryId, setCurrentCategoryId] = useState<string | null>(
    categories?.[0]?.id || null,
  );
  const { data: courses, isFetching: isCoursesLoading } = useCourses(
    currentCategoryId ? { category_id: currentCategoryId } : undefined,
  );

  return (
    <div className="h-full flex-1 overflow-hidden">
      <Card className="h-full border-2 border-primary border-b-4 p-0 py-5">
        <CardContent className="flex flex-col gap-4 px-5 lg:gap-6">
          <div className="w-full">
            <Categories onChange={setCurrentCategoryId} />
          </div>
          <div className="relative">
            <Carousel className="w-full" key={currentCategoryId}>
              <CarouselContent>
                {isCoursesLoading
                  ? Array.from({ length: 3 }).map((_, index) => (
                      <CarouselItem key={index.toString()}>
                        <CourseItemSkeleton
                          isEmptyLearningCourse={isEmptyLearningCourse}
                        />
                      </CarouselItem>
                    ))
                  : courses
                      ?.filter((course) => !course.unpublished)
                      .map((course) => (
                        <CarouselItem
                          key={course.id}
                          className={cn(
                            "flex flex-col items-center justify-center gap-4 lg:gap-5",
                            isEmptyLearningCourse && "py-8 lg:gap-20",
                          )}
                        >
                          <div
                            className={cn(
                              "flex w-full flex-col items-center justify-center gap-4 sm:gap-8",
                              isEmptyLearningCourse && "gap-2",
                            )}
                          >
                            <div
                              className={cn(
                                "flex w-full flex-col items-center justify-center gap-1 text-balance",
                                isEmptyLearningCourse && "gap-2",
                              )}
                            >
                              <CourseTag tag={calculateCourseTag(course)} />
                              <h3 className="min-h-[3rem] text-center font-light text-xl lg:text-4xl! lg:leading-12">
                                {course.name}
                              </h3>
                            </div>
                            <div
                              className={cn(
                                "mt-1 flex flex-col items-center justify-center gap-5 lg:flex-row lg:justify-between",
                                isEmptyLearningCourse && "lg:mt-6",
                              )}
                            >
                              <div className="flex h-48 w-48 items-center justify-center rounded-xl border border-(--card-border) px-4">
                                <Image
                                  src={course.image_url}
                                  alt="owl-teach"
                                  className="object-cover"
                                />
                              </div>
                              <div className="flex flex-1 flex-col items-center justify-between gap-3 py-1 lg:items-start">
                                <CourseAuthor course={course} />
                                <div className="flex flex-col items-center justify-center gap-3 lg:items-start">
                                  <CourseRating
                                    courseId={course.id}
                                    stats={course.stats}
                                  />
                                  <p className="min-w-[20dvw] max-w-[80%] text-center text-xs leading-5 md:max-w-[24rem] lg:text-left">
                                    {
                                      parseCourseDescription(course.description)
                                        ?.overview
                                    }
                                  </p>
                                  <CourseInfo stats={course.stats} />
                                </div>
                              </div>
                            </div>
                          </div>
                          <ShadowButton
                            size="xl"
                            className="flex w-full items-center justify-center gap-2.5 rounded-full py-6! text-primary-foreground md:w-[74%]"
                            onClick={() =>
                              router.navigate({ to: `/courses/${course.slug}` })
                            }
                          >
                            <span className="font-normal text-lg md:text-xl">
                              Dive In
                            </span>{" "}
                            <ArrowRight />
                          </ShadowButton>
                        </CarouselItem>
                      ))}
              </CarouselContent>
              <CarouselPrevious
                className={cn(
                  "-left-1 sm:!p-5 top-56 bg-transparent p-3 sm:size-8 md:left-4 lg:top-2/3",
                  isEmptyLearningCourse && "!top-70",
                )}
                iconClassName="sm:size-6 size-4"
              />
              <CarouselNext
                className={cn(
                  "-right-1 sm:!p-5 top-56 bg-transparent !sm:p-5 p-3 sm:size-8 md:right-4 lg:top-2/3",
                  isEmptyLearningCourse && "!top-70",
                )}
                iconClassName="sm:size-6 size-4"
              />
            </Carousel>
          </div>
          {isLearningLoading ? (
            <LearningCourseSkeleton />
          ) : (
            <LearningCourse learningCourse={learningCourse || []} />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
