import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { CourseRating } from "@/components/lesson/exam/course-rating";
import { StarRating } from "@/components/star-rating";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { UUID } from "@/types/app";
import { UserVoting } from "@/types/rating";
import { cn } from "@/utils/cn";

export const LearningCourseRating = ({
  currentVote,
  courseId,
}: {
  currentVote?: UserVoting;
  courseId: UUID;
}) => {
  const [initRating, setInitRating] = useState(0);
  const queryClient = useQueryClient();

  return (
    <div
      className={cn(
        "flex items-center justify-between gap-2 rounded-(--size-2xs) border border-(--card-border-secondary) bg-(--rating-bg) p-4",
        !currentVote && "flex-col justify-center",
      )}
    >
      {currentVote ? (
        <>
          <StarRating size={24} initialValue={currentVote.general} readOnly />
          <p className="text-xs">Rated</p>
        </>
      ) : (
        <>
          <p>
            Rate this course and earn{" "}
            <span className="--card-tag-bg">+20 XP</span>
          </p>
          <StarRating
            initialValue={initRating}
            onClick={setInitRating}
            size={24}
          />
          <Dialog
            open={initRating !== 0}
            onOpenChange={(open) => !open && setInitRating(0)}
          >
            <DialogContent className="max-w-3xl bg-(--card) sm:max-w-4xl">
              <div className="w-full overflow-hidden">
                <CourseRating
                  courseId={courseId}
                  inDialog
                  initRating={initRating}
                  onRateSuccess={() => {
                    setInitRating(0);
                    queryClient.invalidateQueries({
                      queryKey: ["my-voting"],
                    });
                  }}
                />
              </div>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  );
};
