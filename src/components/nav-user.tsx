import { Link } from "@tanstack/react-router";
import {
  Bell,
  ChevronDown,
  HelpCircleIcon,
  LogOut,
  UserIcon,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import * as m from "@/paraglide/messages";
import { logOut } from "@/store/auth";
import { User } from "@/types/auth";
import { cn } from "@/utils/cn";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Button } from "./ui/button";
import { UserAvatar } from "./user-avatar";

export function NavUser(
  props: { className?: string; avatarClassName?: string } & (
    | { pending: true }
    | {
        user: User;
      }
  ),
) {
  if ("pending" in props) {
    return (
      <Button
        disabled
        variant="ghost"
        size="lg"
        className={cn(props.className)}
      >
        <Avatar>
          <AvatarImage
            alt="user avatar"
            className={cn("max-w-8", props.avatarClassName)}
          />
          <AvatarFallback className="size-8 animate-pulse"></AvatarFallback>
        </Avatar>
        <ChevronDown className="hidden lg:block" />
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="lg"
          className={cn(
            "!py-1 !px-2 h-auto w-auto flex-none gap-0 hover:bg-transparent data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",
            props.className,
          )}
        >
          <UserAvatar
            url={props.user.avatar_url}
            username={props.user.name}
            className={cn("lg:size-10", props.avatarClassName)}
          />
          <ChevronDown />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-(--radix-dropdown-menu-trigger-width) min-w-48 rounded-lg"
        side="bottom"
        align="end"
        sideOffset={4}
      >
        <DropdownMenuLabel className="p-0 font-normal">
          <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
            <UserAvatar
              url={props.user.avatar_url}
              username={props.user.name}
            />
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-medium">{props.user.name}</span>
              <span className="truncate text-xs">{props.user.email}</span>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {/* <DropdownMenuGroup>
          <DropdownMenuItem>
            <Sparkles />
            Upgrade to Pro
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator /> */}
        <DropdownMenuGroup>
          <Link to="/profile/me">
            <DropdownMenuItem className="cursor-pointer">
              <UserIcon />
              Profile
            </DropdownMenuItem>
          </Link>
          <DropdownMenuItem className="cursor-pointer">
            <Bell />
            Notifications
          </DropdownMenuItem>
          <Link to="/help-center">
            <DropdownMenuItem>
              <HelpCircleIcon />
              {m["common.navigation.help_center"]()}
            </DropdownMenuItem>
          </Link>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="cursor-pointer"
          onClick={() => {
            logOut();
          }}
        >
          <LogOut />
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
