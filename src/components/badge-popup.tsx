import { Link } from "@tanstack/react-router";
import { ArrowLineRightIcon } from "@/components/icons";
import * as m from "@/paraglide/messages.js";
import { useBadgePopup } from "@/utils/badges";
import { ShadowButton } from "./shadow-button";
import { Dialog, DialogContent, DialogTitle } from "./ui/dialog";

export const BadgePopup = () => {
  const { badge, isVisible, showPopup, hidePopup } = useBadgePopup();
  if (!badge) return null;

  const onHandleChange = (visible: boolean) => {
    if (visible) {
      showPopup();
      return;
    }
    hidePopup();
  };

  return (
    <Dialog open={isVisible} onOpenChange={onHandleChange}>
      <DialogTitle className="sr-only"></DialogTitle>
      <DialogContent>
        <div className="p-4">
          <div className="w-64 hover:scale-110">
            <img src={badge.img} alt={badge.title} />
          </div>
          <p className="mt-6 font-light text-3xl">
            {m["profile.achievement_details.popup.congratulations"]()}
            <br />
            {badge.congratulation}
          </p>
          <p className="mt-4 text-xl leading-7">{badge.title}</p>
          <p className="mt-4 text-base">{badge.description}</p>
          <div className="mt-4 flex w-full items-center">
            <ShadowButton
              size="lg"
              className="flex w-[50%] items-center justify-center gap-2.5 rounded-full py-6! text-primary-foreground"
              onClick={hidePopup}
            >
              {m["profile.achievement_details.popup.keep_learning"]()}
              <ArrowLineRightIcon />
            </ShadowButton>
            <Link
              className="ms-4 cursor-pointer text-sm underline"
              to="/profile/me"
              search={{ tab: "achievement" }}
              onClick={hidePopup}
            >
              {m["profile.achievement_details.popup.view_my_badges"]()}
            </Link>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
