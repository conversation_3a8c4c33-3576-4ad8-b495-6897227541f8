import { memo, ReactNode } from "react";
import { ButtonCopy } from "@/components/button-copy";
import {
  CodeIcon,
  ErrorIcon,
  InfoIcon,
  QuoteIcon,
  WarningIcon,
} from "@/components/icons";
import { HighlightedText } from "@/components/lesson/exam/highlighted-text";
import { cn } from "@/utils/cn";

export interface TipTapNode {
  type: string;
  text?: string;
  content?: TipTapNode[];
  marks?: Array<{ type: string; attrs?: Record<string, unknown> }>;
  attrs?: {
    level?: number;
    src?: string;
    alt?: string;
    textAlign?: "left" | "center" | "right" | "justify";
    width?: string;
    height?: string;
    language?: string;
    checked?: boolean;
    id?: string;
    label?: string;
    emoji?: string;
    colspan?: number;
    rowspan?: number;
    color?: string;
    size?: string;
    [key: string]: unknown;
  };
}

type NodeRenderer = (
  node: TipTapNode,
  children: ReactNode[],
  key?: number,
  context?: RenderContext,
) => ReactNode;

type MarkRenderer = (
  child: ReactNode,
  mark: { type: string; attrs?: Record<string, unknown> },
) => ReactNode;

interface RenderContext {
  sentenceId?: string;
  fullText?: string;
}

type TextAlign = "left" | "center" | "right" | "justify";
type HeadingLevel = "h1" | "h2" | "h3" | "h4" | "h5" | "h6";

// Utility functions for sentence detection
const detectSentences = (
  text: string,
): Array<{ sentence: string; start: number; end: number }> => {
  const sentences: Array<{ sentence: string; start: number; end: number }> = [];
  const sentenceRegex = /[.!?]+\s*/g;
  let lastIndex = 0;
  let match: RegExpExecArray | null;

  match = sentenceRegex.exec(text);
  while (match !== null) {
    const sentence = text
      .substring(lastIndex, match.index + match[0].length)
      .trim();
    if (sentence) {
      sentences.push({
        sentence,
        start: lastIndex,
        end: match.index + match[0].length,
      });
    }
    lastIndex = match.index + match[0].length;
    match = sentenceRegex.exec(text);
  }

  // Add remaining text as a sentence if it exists
  if (lastIndex < text.length) {
    const sentence = text.substring(lastIndex).trim();
    if (sentence) {
      sentences.push({
        sentence,
        start: lastIndex,
        end: text.length,
      });
    }
  }

  return sentences;
};

const generateSentenceId = (sentence: string): string => {
  return `sentence-${sentence.substring(0, 20).replace(/\s+/g, "-").toLowerCase()}-${Date.now()}`;
};

// Helper function to check if content should be rendered
const shouldRenderContent = (text: string): boolean => {
  const trimmed = text.trim();
  return trimmed !== "" && trimmed !== ".";
};

// Extended mark renderers
const markRenderers: Record<string, MarkRenderer> = {
  bold: (child) => <strong>{child}</strong>,
  italic: (child) => <em className="italic">{child}</em>,
  underline: (child) => <u>{child}</u>,
  strike: (child) => <s>{child}</s>,
  link: (child, mark) => (
    <a
      href={mark.attrs?.href as string}
      target="_blank"
      rel="noopener noreferrer"
      className="text-blue-600 hover:underline"
    >
      {child}
    </a>
  ),
  code: (child) => (
    <code className="rounded bg-gray-100 px-1 font-mono">{child}</code>
  ),
  subscript: (child) => <sub>{child}</sub>,
  superscript: (child) => <sup>{child}</sup>,
  highlight: (child, mark) => (
    <mark
      style={{
        backgroundColor: (mark.attrs?.color as string) || "#fff9b8",
      }}
      className="rounded px-1"
    >
      {child}
    </mark>
  ),
  textStyle: (child, mark) => (
    <span style={{ color: mark.attrs?.color as string }}>{child}</span>
  ),
  fontSize: (child, mark) => (
    <span style={{ fontSize: mark.attrs?.size as string }}>{child}</span>
  ),
  color: (child, mark) => (
    <span style={{ color: mark.attrs?.color as string }}>{child}</span>
  ),
  alignment: (child, mark) => (
    <div style={{ textAlign: mark.attrs?.alignment as TextAlign }}>{child}</div>
  ),
};

// Extended node renderers
const nodeRenderers: Record<string, NodeRenderer> = {
  doc: (_, children) => {
    if (!children || children.length === 0) return null;
    if (children.length === 1) return children[0];
    return <div className="tiptap-content selectable-content">{children}</div>;
  },
  paragraph: (node, children, key) => {
    if (!children || children.length === 0) return null;

    const textAlign = node.attrs?.textAlign;

    // Extract full text from paragraph for sentence detection
    const fullText = extractTextFromNode(node);
    const sentences = detectSentences(fullText);

    // If there are multiple sentences, wrap paragraph with sentence context
    if (sentences.length > 1) {
      return (
        <p
          key={key}
          className="selectable-content leading-7"
          style={textAlign ? { textAlign } : undefined}
          data-full-text={fullText}
        >
          {children}
        </p>
      );
    }

    return (
      <p
        key={key}
        className="selectable-content leading-7"
        style={textAlign ? { textAlign } : undefined}
        data-sentence-id={
          sentences[0] ? generateSentenceId(sentences[0].sentence) : undefined
        }
      >
        {children}
      </p>
    );
  },
  text: (node, _, key, context) => {
    const text = node.text ?? "";
    if (!shouldRenderContent(text)) {
      return null;
    }
    return <TextNode node={node} key={key} nodeKey={key} context={context} />;
  },
  heading: (node, children, key) => {
    if (!children || children.length === 0) return null;

    const level = (node.attrs?.level ?? 1) as 1 | 2 | 3 | 4 | 5 | 6;
    const textAlign = node.attrs?.textAlign;
    const HeadingTag = `h${level}` as HeadingLevel;
    const classNames: Record<HeadingLevel, string> = {
      h1: "text-3xl font-bold leading-7 mt-6",
      h2: "text-2xl font-bold mb-3 mt-6",
      h3: "text-xl font-bold mb-2 mt-6",
      h4: "text-lg font-bold mb-2 mt-6",
      h5: "text-base font-bold mb-2 mt-4",
      h6: "text-sm font-bold mb-2 mt-4",
    };

    const fullText = extractTextFromNode(node);
    const sentences = detectSentences(fullText);

    return (
      <HeadingTag
        key={key}
        className={`${classNames[HeadingTag]} selectable-content`}
        style={textAlign ? { textAlign } : undefined}
        data-sentence-id={
          sentences[0] ? generateSentenceId(sentences[0].sentence) : undefined
        }
      >
        {children}
      </HeadingTag>
    );
  },
  bulletList: (node, children, key) => {
    if (!children || children.length === 0) return null;

    const textAlign = node.attrs?.textAlign;
    return (
      <ul
        key={key}
        className="ml-5 list-disc leading-7"
        style={textAlign ? { textAlign } : undefined}
      >
        {children}
      </ul>
    );
  },
  orderedList: (node, children, key) => {
    if (!children || children.length === 0) return null;

    const textAlign = node.attrs?.textAlign;
    return (
      <ol
        key={key}
        className="ml-5 list-decimal leading-7"
        style={textAlign ? { textAlign } : undefined}
      >
        {children}
      </ol>
    );
  },
  listItem: (_, children, key) => {
    if (!children || children.length === 0) return null;

    return (
      <li key={key} className="selectable-content mb-1">
        {children}
      </li>
    );
  },
  blockquote: (node, children, key) => {
    if (!children || children.length === 0) return null;

    const textAlign = node.attrs?.textAlign;
    return (
      <blockquote
        key={key}
        className="selectable-content border-gray-500 border-l-4 bg-[#FAFBFF] p-4 italic"
        style={textAlign ? ({ textAlign } as React.CSSProperties) : undefined}
      >
        <QuoteIcon />
        <div className="mt-1 pl-5">{children}</div>
      </blockquote>
    );
  },
  codeBlock: (node, children, key) => {
    if (!children || children.length === 0) return null;

    const language = node.attrs?.language;
    return (
      <pre
        key={key}
        className={`overflow-x-auto rounded-[20px] bg-[#494949] p-3 font-mono text-primary-foreground text-sm leading-7 sm:rounded-[30px] sm:p-6 ${
          language ? `language-${language}` : ""
        }`}
      >
        <div className="flex gap-2">
          <CodeIcon />
          <div className="flex flex-col gap-2">
            <p className="flex items-center pt-1 font-semibold">Code/prompt</p>
            <code>{children}</code>
          </div>
        </div>
      </pre>
    );
  },
  horizontalRule: (_, __, key) => (
    <hr key={key} className="border-gray-300 border-t" />
  ),
  image: (node, _, key) => {
    const src = node.attrs?.src as string;
    const alt = (node.attrs?.alt as string) || "Image";
    const title = node.attrs?.title as string | undefined;
    const width = node.attrs?.width;
    const height = node.attrs?.height;

    return (
      <img
        key={key}
        src={src}
        alt={alt}
        title={title}
        style={{
          width: width,
          height: height,
        }}
        className="h-auto max-w-full rounded pb-4"
      />
    );
  },
  hardBreak: (_, __, key) => <br key={key} />,
  table: (node, children, key) => {
    if (!children || children.length === 0) return null;

    const textAlign = node.attrs?.textAlign;
    return (
      <table
        key={key}
        className="w-full border-collapse border border-gray-300 leading-7"
        style={textAlign ? { textAlign } : undefined}
      >
        <tbody>{children}</tbody>
      </table>
    );
  },
  tableRow: (_, children, key) => {
    if (!children || children.length === 0) return null;
    return <tr key={key}>{children}</tr>;
  },
  tableCell: (node, children, key) => {
    const colspan = node.attrs?.colspan;
    const rowspan = node.attrs?.rowspan;
    const textAlign = node.attrs?.textAlign;

    return (
      <td
        key={key}
        className="selectable-content border border-gray-300 p-2"
        colSpan={colspan}
        rowSpan={rowspan}
        style={textAlign ? { textAlign } : undefined}
      >
        {children}
      </td>
    );
  },
  tableHeader: (node, children, key) => {
    const colspan = node.attrs?.colspan;
    const rowspan = node.attrs?.rowspan;
    const textAlign = node.attrs?.textAlign;

    return (
      <th
        key={key}
        className="selectable-content border border-gray-300 bg-gray-100 p-2 font-semibold"
        colSpan={colspan}
        rowSpan={rowspan}
        style={textAlign ? { textAlign } : undefined}
      >
        {children}
      </th>
    );
  },
  taskList: (_, children, key) => {
    if (!children || children.length === 0) return null;

    return (
      <ul key={key} className="list-none pl-0 leading-7">
        {children}
      </ul>
    );
  },
  taskItem: (node, children, key) => {
    const checked = node.attrs?.checked ?? false;
    return (
      <li key={key} className="mb-1 flex items-start">
        <input
          type="checkbox"
          checked={checked}
          readOnly
          className="mt-1 mr-2"
        />
        <div className="selectable-content">{children}</div>
      </li>
    );
  },
  mention: (node, _, key) => {
    const id = node.attrs?.id as string;
    const label = node.attrs?.label as string;
    return (
      <span
        key={key}
        className="rounded bg-blue-100 px-1 text-blue-800"
        data-mention-id={id}
      >
        @{label || id}
      </span>
    );
  },
  emoji: (node, _, key) => {
    const emoji = node.attrs?.emoji as string;
    return <span key={key}>{emoji}</span>;
  },
  div: (node, children, key) => {
    if (!children || children.length === 0) return null;

    const textAlign = node.attrs?.textAlign;
    return (
      <div
        key={key}
        className="selectable-content"
        style={textAlign ? { textAlign } : undefined}
      >
        {children}
      </div>
    );
  },
  tableHead: (_, children, key) => {
    if (!children || children.length === 0) return null;
    return <thead key={key}>{children}</thead>;
  },
  tableBody: (_, children, key) => {
    if (!children || children.length === 0) return null;
    return <tbody key={key}>{children}</tbody>;
  },
  tableFoot: (_, children, key) => {
    if (!children || children.length === 0) return null;
    return <tfoot key={key}>{children}</tfoot>;
  },
  highlightBox: (node, children, key) => {
    if (!children || children.length === 0) return null;
    const textAlign = node.attrs?.textAlign;
    const variant = node.attrs?.boxType as HighlightedBoxVariant;

    if (variant === "Code" || variant === "Prompt") {
      const language = node.attrs?.language;

      return (
        <pre
          key={key}
          className={`overflow-x-auto rounded-[20px] bg-[#494949] p-3 font-mono text-primary-foreground text-sm leading-7 sm:rounded-[30px] sm:p-6 ${
            language ? `language-${language}` : ""
          }`}
        >
          <div className="flex gap-2">
            <div>
              <CodeIcon />
            </div>
            <div className="flex flex-col gap-2">
              <p className="flex items-center pt-1 font-semibold">
                Code/prompt
              </p>
              <code>{children}</code>
            </div>
          </div>
        </pre>
      );
    }

    const variantData = variantDataMap[variant];
    const Icon = variantData.icon;
    const fullTextContent = extractTextFromNode(node);

    return (
      <div
        key={key}
        className={cn(
          "selectable-content relative rounded-[20px] border border-gray-300 p-4 sm:rounded-[30px] sm:p-6",
          variantData.className,
        )}
        style={textAlign ? { textAlign } : undefined}
      >
        <div className="absolute top-3 right-3">
          <ButtonCopy content={fullTextContent} />
        </div>
        <div className="flex flex-col gap-3 sm:flex-row">
          <div className="hidden sm:block">
            <Icon />
          </div>
          <div className="sm:pt-1">
            <div className="flex items-center gap-2">
              <div className="block sm:hidden">
                <Icon width={24} height={24} />
              </div>
              <span className={cn("font-semibold", variantData.titleClassName)}>
                {variantData.title}
              </span>
            </div>
            <div className="mt-2 text-start text-sm">{children}</div>
          </div>
        </div>
      </div>
    );
  },
};

// Helper function to extract text content from a node
const extractTextFromNode = (node: TipTapNode): string => {
  if (node.text) {
    return node.text;
  }

  if (node.content) {
    return node.content.map(extractTextFromNode).join("");
  }

  return "";
};

const renderChildren = (
  nodes: TipTapNode[] | undefined,
  context?: RenderContext,
): ReactNode[] =>
  nodes
    ?.map((child, i) => renderTipTapNode(child, i, context))
    .filter((child) => child !== null) ?? [];

const applyMarks = (
  element: ReactNode,
  marks: Array<{ type: string; attrs?: Record<string, unknown> }>,
): ReactNode => {
  if (!marks || marks.length === 0) return element;

  let result = element;
  for (const mark of marks) {
    const renderer = markRenderers[mark.type];
    if (renderer) {
      result = renderer(result, mark);
    }
  }
  return result;
};

const TextNode = memo(
  ({
    node,
    nodeKey,
    context,
  }: {
    node: TipTapNode;
    nodeKey?: number;
    context?: RenderContext;
  }) => {
    const text = node.text ?? "";

    if (!shouldRenderContent(text)) {
      return null;
    }

    const highlightedText = <HighlightedText text={text} />;

    if (node.marks?.length) {
      return (
        <span key={nodeKey}>{applyMarks(highlightedText, node.marks)}</span>
      );
    }

    return <span key={nodeKey}>{highlightedText}</span>;
  },
  (prevProps, nextProps) => {
    return (
      prevProps.node.text === nextProps.node.text &&
      JSON.stringify(prevProps.node.marks) ===
        JSON.stringify(nextProps.node.marks)
    );
  },
);

// Main render function with better error handling
export const renderTipTapNode = (
  node: TipTapNode,
  key?: number,
  context?: RenderContext,
): ReactNode => {
  if (!node) return null;

  try {
    const children = renderChildren(node.content, context);
    const renderer = nodeRenderers[node.type];

    if (!renderer) {
      return children.length > 0 ? <div key={key}>{children}</div> : null;
    }

    return renderer(node, children, key, context);
  } catch (error) {
    console.error(
      `Error rendering node type: ${node.type || "unknown"}`,
      error,
    );
    return (
      <div key={key} className="rounded border border-red-300 p-2 text-red-500">
        Rendering error: {node.type}
      </div>
    );
  }
};

const variantGroups = {
  infoLike: {
    className: "border-[#0178D4] bg-[#F3F6FF]",
    icon: InfoIcon,
    titleClassName: "text-[#456DFF]",
  },
  warning: {
    className: "border-[#FFB800] bg-[#FFF9E9]",
    icon: WarningIcon,
    titleClassName: "text-[#A67C2F]",
  },
  error: {
    className: "border-[#F4364B] bg-[#FDF4F4]",
    icon: ErrorIcon,
    titleClassName: "text-[#A73636]",
  },
};

type HighlightedBoxVariant = keyof typeof variantDataMap | "Code" | "Prompt";

const variantDataMap = {
  Info: { ...variantGroups.infoLike, title: "Info" },
  Fact: { ...variantGroups.infoLike, title: "Fact" },
  Tips: { ...variantGroups.warning, title: "Tips" },
  Notes: { ...variantGroups.warning, title: "Notes" },
  Remark: { ...variantGroups.warning, title: "Remark" },
  Attention: { ...variantGroups.error, title: "Attention" },
};
