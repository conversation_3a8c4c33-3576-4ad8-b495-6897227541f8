import { Slot } from "@radix-ui/react-slot";
import { type VariantProps } from "class-variance-authority";
import React from "react";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/utils/cn";

interface ShadowButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const ShadowButton = React.forwardRef<HTMLButtonElement, ShadowButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(
          buttonVariants({ variant, size }),
          "shadow-[0px_7px_0px_0px_#14B440] active:translate-y-1 active:shadow-[0px_3px_0px_0px_#14B440]",
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
ShadowButton.displayName = "ShadowButton";

export { ShadowButton };
