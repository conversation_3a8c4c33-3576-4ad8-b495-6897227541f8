import { useState } from "react";

interface StarRatingProps {
  initialValue?: number;
  onClick?: (value: number) => void;
  size?: number;
  starGap?: number;
  allowFraction?: boolean;
  totalStars?: number;
  readOnly?: boolean;
}

export const StarRating = ({
  initialValue = 0,
  onClick,
  size = 28,
  starGap = 4,
  allowFraction = false,
  totalStars = 5,
  readOnly = false,
}: StarRatingProps) => {
  const [rating, setRating] = useState(initialValue);
  const [hoverRating, setHoverRating] = useState(0);

  const handleClick = (value: number) => {
    if (readOnly) return;
    const newRating = allowFraction ? value : Math.round(value);
    setRating(newRating);
    onClick?.(newRating);
  };

  const handleMouseMove = (
    e: React.MouseEvent<HTMLDivElement>,
    index: number,
  ) => {
    if (readOnly) return;
    if (!allowFraction) {
      setHoverRating(index + 1);
      return;
    }
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const starWidth = rect.width / totalStars;
    const fraction = x / starWidth;
    setHoverRating(Math.min(index + fraction, totalStars));
  };

  const handleMouseLeave = () => {
    if (readOnly) return;
    setHoverRating(0);
  };

  return (
    <div className="flex" style={{ gap: `${starGap}px` }}>
      {Array.from({ length: totalStars }).map((_, index) => {
        const starValue = index + 1;
        const isFilled =
          hoverRating > 0 ? hoverRating >= starValue : rating >= starValue;
        const partialFill =
          allowFraction && hoverRating > index && hoverRating < starValue
            ? hoverRating % 1
            : 0;

        return (
          <div
            key={index.toString()}
            className={`relative ${readOnly ? "cursor-default" : "cursor-pointer"}`}
            style={{ width: size, height: size }}
            onClick={() => handleClick(starValue)}
            onMouseMove={(e) => handleMouseMove(e, index)}
            onMouseLeave={handleMouseLeave}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width={size}
              height={size}
              fill="none"
              viewBox="0 0 30 30"
              className="text-gray-300"
            >
              <path
                fill="currentColor"
                fillOpacity="0.3"
                d="m14.789 1.849-4.205 8.522-9.4 1.358 6.802 6.637-1.608 9.362 8.411-4.418 8.41 4.418-1.607-9.362 6.802-6.628-9.4-1.367z"
              ></path>
            </svg>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width={size}
              height={size}
              fill="none"
              viewBox="0 0 30 30"
              className="absolute top-0 left-0 text-yellow-400"
              style={{
                clipPath: partialFill
                  ? `inset(0 ${100 - partialFill * 100}% 0 0)`
                  : isFilled
                    ? "none"
                    : "inset(0 100% 0 0)",
              }}
            >
              <path
                fill="currentColor"
                d="m14.789 1.849-4.205 8.522-9.4 1.358 6.802 6.637-1.608 9.362 8.411-4.418 8.41 4.418-1.607-9.362 6.802-6.628-9.4-1.367z"
              ></path>
            </svg>
          </div>
        );
      })}
    </div>
  );
};
