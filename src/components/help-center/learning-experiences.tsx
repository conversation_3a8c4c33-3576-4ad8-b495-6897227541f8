import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import * as m from "@/paraglide/messages.js";
import { Divider } from "../course/course-detail-items";
import { GradientText } from "../ui/gradient-text";

// Moved learningExperiences data inside component to use translations
const oldLearningExperiences = [
  {
    question: "What kind of games or activities are included?",
    answer:
      "aicademy makes learning feel like a game with interactive lessons, XP points, leaderboards, and <PERSON><PERSON>, your AI Mentor, guiding you every step of the way.",
  },
  {
    question: "Is Aicademy free to use?",
    answer:
      "That’s <PERSON><PERSON>! 🦉 <PERSON><PERSON> is your built-in AI Mentor who’s always there to explain tough concepts, give examples, and help you out whenever you’re stuck - whenever you need support.",
  },
  {
    question: "Do I need to know anything about AI before I start?",
    answer:
      "Yes! You can learn and explore courses for free. In addition, learners can use in-game items to unlock even more exciting features and experiences.",
  },
  {
    question: "Can I use Aicademy on mobile?",
    answer:
      "Currently, courses are available in Vietnamese, with English support coming soon.",
  },
  {
    question: "Is my data safe on Aicademy?",
    answer:
      "aicademy works on all modern devices — laptop, tablet, or smartphone. Just open it in your web browser!",
  },
];

export const LearningExperiences = () => {
  const learningExperiences = [
    {
      question: m["help.learning_experiences.questions.q1"](),
      answer: m["help.learning_experiences.questions.a1"](),
    },
    {
      question: m["help.learning_experiences.questions.q2"](),
      answer: m["help.learning_experiences.questions.a2"](),
    },
    {
      question: m["help.learning_experiences.questions.q3"](),
      answer: m["help.learning_experiences.questions.a3"](),
    },
    {
      question: m["help.learning_experiences.questions.q4"](),
      answer: m["help.learning_experiences.questions.a4"](),
    },
    {
      question: m["help.learning_experiences.questions.q5"](),
      answer: m["help.learning_experiences.questions.a5"](),
    },
  ];

  return (
    <div className="w-full bg-white p-10 sm:rounded-md sm:border-(--card-border-secondary) sm:border-1">
      <div className="flex w-full items-end justify-center gap-2 lg:justify-start">
        <GradientText className="font-light text-3xl md:text-4xl">
          {m["help.learning_experiences.title"]()}
        </GradientText>
        <span className="hidden text-sm lg:inline">
          {m["help.learning_experiences.description"]()}
        </span>
      </div>
      <span className="text-sm lg:hidden">
        {m["help.learning_experiences.description"]()}
      </span>
      <Divider />
      <Accordion type="single" collapsible className="w-full text-left">
        {learningExperiences.map((item, index) => (
          <AccordionItem
            key={`item-${index.toString()}`}
            value={`item-${index}`}
          >
            <AccordionTrigger>
              {index + 1}. {item.question}
            </AccordionTrigger>
            <AccordionContent className="text-sm">
              {item.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};
