import { ArrowRightIcon } from "lucide-react";

export const Explore = () => {
  return (
    <div className="my-4">
      <div className="mb-6 text-left text-2xl">Explore</div>
      <div className="grid gap-4 sm:grid-cols-3 md:gap-10">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index.toString()} className="flex flex-col gap-4">
            <div className="relative aspect-[4/3] w-full overflow-hidden rounded-md">
              <div className="absolute inset-0 h-full w-full bg-gray-300"></div>
            </div>
            <div className="flex items-center text-left">
              <span className="mr-2 text-lg">Lorem ipsum dolor sit amet</span>
              <ArrowRightIcon />
            </div>
            <div className="text-left text-sm">
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam,
              quos.
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
