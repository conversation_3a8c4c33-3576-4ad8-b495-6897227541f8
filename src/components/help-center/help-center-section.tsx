import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { HelpCenterSection } from "@/data/help-center-vi";
import { Divider } from "../course/course-detail-items";
import { GradientText } from "../ui/gradient-text";

interface HelpCenterSectionComponentProps {
  section: HelpCenterSection;
}

export const HelpCenterSectionComponent = ({
  section,
}: HelpCenterSectionComponentProps) => {
  return (
    <div className="w-full bg-white p-10 sm:rounded-md sm:border-(--card-border-secondary) sm:border-1">
      <div className="flex w-full items-end justify-center gap-2 lg:justify-start">
        <GradientText className="font-light text-3xl md:text-4xl">
          {section.title}
        </GradientText>
        <span className="hidden text-sm lg:inline">{section.description}</span>
      </div>
      <span className="text-sm lg:hidden">{section.description}</span>
      <Divider />
      <Accordion type="single" collapsible className="w-full text-left">
        {section.items.map((item, index) => (
          <AccordionItem
            key={`item-${index.toString()}`}
            value={`item-${index}`}
          >
            <AccordionTrigger>
              {index + 1}. {item.question}
            </AccordionTrigger>
            <AccordionContent className="text-sm">
              {item.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};
