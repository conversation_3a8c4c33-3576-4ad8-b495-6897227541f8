import { Link } from "@tanstack/react-router";
import { ArrowRightIcon } from "lucide-react";
import HelpCenterBookIcon from "@/assets/images/help-center-book.svg";
import HelpCenterLearningIcon from "@/assets/images/help-center-learning.svg";
import HelpCenterLockIcon from "@/assets/images/help-center-lock.svg";
import HelpCenterOwlIcon from "@/assets/images/help-center-owl.svg";
import HelpCenterPaymentIcon from "@/assets/images/help-center-payment.svg";
import HelpCenterSupportIcon from "@/assets/images/help-center-support.svg";
import { helpCenterDataEn } from "@/data/help-center-en";
import { helpCenterDataVi } from "@/data/help-center-vi";
import { getLocale } from "@/paraglide/runtime";

const getIconForSection = (sectionId: string) => {
  switch (sectionId) {
    case "about-aicademy":
      return HelpCenterOwlIcon;
    case "ai-assistant-ailo":
      return HelpCenterSupportIcon;
    case "courses":
      return HelpCenterBookIcon;
    case "learning-experiences":
      return HelpCenterLearningIcon;
    case "fee-and-payment":
      return HelpCenterPaymentIcon;
    case "certificates-and-achievements":
      return HelpCenterLockIcon;
    default:
      return HelpCenterOwlIcon;
  }
};

export const InformationList = () => {
  const locale = getLocale();
  const helpCenterData = locale === "en" ? helpCenterDataEn : helpCenterDataVi;

  return (
    <div className="grid gap-10 sm:grid-cols-2 md:grid-cols-3 md:gap-y-20">
      {helpCenterData.map((section) => (
        <Link
          key={section.id}
          to="/help-center/$helpCenter"
          params={{ helpCenter: section.id }}
          className="flex cursor-pointer flex-col gap-2 text-left transition-opacity hover:opacity-80"
        >
          <img
            src={getIconForSection(section.id)}
            alt={section.title}
            className="h-10 w-10"
          />
          <div className="flex items-center">
            <span className="text-xl">{section.title}</span>
            <ArrowRightIcon className="ml-2 size-4" />
          </div>
          <span className="text-sm">{section.description}</span>
        </Link>
      ))}
    </div>
  );
};
