import { useEffect, useState } from "react";
import { CustomCarousel } from "@/components/custom-carousel";
import { Route } from "@/routes/__layout/courses";
import { useCategories } from "@/services/categories";
import { Category, CategoryEnum } from "@/types/app";
import { cn } from "@/utils/cn";

interface CategoriesProps {
  includeAll?: boolean;
  route?: string;
  onChange?: (categoryId: string) => void;
  category?: string;
}

const allCategory: Category = {
  id: "all",
  name: "All",
  slug: CategoryEnum.ALL,
  description: "",
  ordinal_index: 0,
};

export const Categories = ({
  includeAll,
  route,
  onChange,
  category,
}: CategoriesProps) => {
  const { data: categories } = useCategories();
  const navigate = Route.useNavigate();
  const [activeCategory, setActiveCategory] = useState<string>("");

  useEffect(() => {
    if (!categories?.length) return;
    setActiveCategory(
      category ? category : includeAll ? allCategory.slug : categories[0].slug,
    );
  }, [category, includeAll, categories]);

  const handleCategoryChange = (categorySlug: string) => {
    setActiveCategory(categorySlug);
    if (!route) {
      onChange?.(categories?.find((c) => c.slug === categorySlug)?.id ?? "");
      return;
    }
    navigate({
      search: (prev) => ({
        ...prev,
        category: categorySlug === allCategory.slug ? undefined : categorySlug,
      }),
      to: route,
    });
  };

  const renderCategoryItem = (category: Category) => {
    const isSelected = activeCategory === category.slug;
    return (
      <p
        key={category.slug}
        onClick={() => handleCategoryChange(category.slug)}
        className={cn(
          "flex h-10 flex-shrink-0 cursor-pointer snap-start items-center rounded-md px-4 py-2 font-medium text-sm transition-all hover:text-black",
          isSelected
            ? "bg-secondary text-secondary-foreground hover:bg-secondary hover:text-secondary-foreground"
            : " border-none bg-transparent text-gray-700 hover:bg-transparent hover:text-primary",
        )}
      >
        {category.name}
        {/*{category. > 0 && (*/}
        {/*  <span*/}
        {/*    className={cn(*/}
        {/*      "ml-2 rounded-sm bg-gray-200 px-1 py-0.5 font-semibold text-gray-700 text-xs md:px-2",*/}
        {/*      isSelected && "bg-gray-700 text-gray-100",*/}
        {/*    )}*/}
        {/*  >*/}
        {/*    {category.count}*/}
        {/*  </span>*/}
        {/*)}*/}
      </p>
    );
  };

  if (!categories?.length) {
    return null;
  }
  return (
    <div className="rounded-lg bg-(--background-active) p-2">
      <CustomCarousel
        data={includeAll ? [allCategory, ...categories] : categories}
        renderItem={renderCategoryItem}
        itemKey={(category) => category.id}
        showGradient
        className="!gap-0"
        itemClassName="flex-shrink-0"
      />
    </div>
  );
};
