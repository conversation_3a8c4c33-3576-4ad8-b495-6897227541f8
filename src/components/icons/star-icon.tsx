export const StarIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      fill="none"
      viewBox="0 0 14 14"
      {...props}
    >
      <path
        fill="#fff"
        fillOpacity="0.01"
        d="M0 0h13.091v13.091H0z"
        style={{ mixBlendMode: "multiply" }}
        transform="translate(0 .455)"
      />
      <path
        fill="currentColor"
        d="M6.545 1.273 4.684 5.045l-4.16.6 3.01 2.938-.711 4.144 3.722-1.955 3.723 1.955-.712-4.144 3.011-2.933-4.16-.605z"
      />
    </svg>
  );
};
