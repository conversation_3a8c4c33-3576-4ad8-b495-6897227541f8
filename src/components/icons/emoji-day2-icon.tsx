export const EmojiDay2Icon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="22"
      fill="none"
      viewBox="0 0 22 22"
      {...props}
    >
      <ellipse
        cx="10"
        cy="10.435"
        fill={props.color || "#fff"}
        rx="10"
        ry="10.435"
        transform="matrix(-1 0 0 1 21.373 .565)"
      ></ellipse>
      <path
        fill="#000"
        d="M21.33 9.335a11.4 11.4 0 0 0-.6-2.609 8.63 8.63 0 0 0-2.939-3.913 9.25 9.25 0 0 0-4.495-1.8.313.313 0 0 0-.34.261.305.305 0 0 0 .261.33 8.6 8.6 0 0 1 4.166 1.783 7.94 7.94 0 0 1 2.608 3.635c.285.784.48 1.598.583 2.426.074.83.074 1.666 0 2.496a9 9 0 0 1-.565 2.391 10.7 10.7 0 0 1-1.122 2.2 8.46 8.46 0 0 1-3.922 3.052 11.04 11.04 0 0 1-4.991.73c-.94-.08-1.864-.288-2.748-.617a8.2 8.2 0 0 1-2.409-1.391 10.03 10.03 0 0 1-3.287-6.191 9.28 9.28 0 0 1 1.548-6.844 10.1 10.1 0 0 1 2.713-2.582 8.1 8.1 0 0 1 3.54-1.183c.617-.06 1.225-.113 1.843-.113q.612 0 1.217.078a.261.261 0 1 0 .079-.513 9 9 0 0 0-1.288-.156Q10.224.789 9.27.892a8.8 8.8 0 0 0-3.808 1.243C4.31 2.84 3.3 3.755 2.487 4.831a10 10 0 0 0-1.818 7.4 10.92 10.92 0 0 0 3.522 6.826 9.1 9.1 0 0 0 2.696 1.583 11.8 11.8 0 0 0 3.026.678 12 12 0 0 0 5.443-.835 9.24 9.24 0 0 0 4.305-3.478c.488-.755.888-1.563 1.191-2.409.303-.84.494-1.718.565-2.609.054-.885.025-1.772-.087-2.652"
      ></path>
      <path
        fill="#000"
        d="M16.87 11.353a5.9 5.9 0 0 1-1.157 2.99 7 7 0 0 1-.922 1.01q-.512.447-1.104.782a3.74 3.74 0 0 1-3.026.46.27.27 0 0 0-.348.14.27.27 0 0 0 .147.348 4.27 4.27 0 0 0 3.661-.096 5.4 5.4 0 0 0 1.34-.87c.398-.357.744-.77 1.026-1.225a6.3 6.3 0 0 0 .974-3.479.287.287 0 0 0-.279-.339.304.304 0 0 0-.313.278M9.087 7.24c-.66 0-1.26-.053-1.843-.061a10 10 0 0 0-1.244 0 13.6 13.6 0 0 0-2.07.417.26.26 0 0 0-.225.296.26.26 0 0 0 .287.226c.643 0 1.217.052 1.791.07h.774l.774-.062a23 23 0 0 0 1.817-.287.304.304 0 0 0 .162-.535.3.3 0 0 0-.223-.065M17.635 7.857a.29.29 0 0 0-.27-.322 31 31 0 0 0-1.86-.33c-.27 0-.53-.061-.8-.079-.27-.017-.54 0-.81 0-.66 0-1.286.096-1.886.157a.26.26 0 1 0-.009.522c.592.078 1.209.2 1.852.278l.792.06h2.67a.304.304 0 0 0 .321-.286"
      ></path>
    </svg>
  );
};
