export const TelegramIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="29"
      height="29"
      viewBox="0 0 29 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.0057 9.41873C14.1249 10.201 10.3659 11.8202 4.7288 14.2762C3.81341 14.6402 3.3339 14.9963 3.29024 15.3445C3.21647 15.933 3.95341 16.1647 4.95694 16.4803C5.09344 16.5232 5.23488 16.5677 5.37988 16.6148C6.3672 16.9357 7.69531 17.3112 8.38574 17.3261C9.01203 17.3397 9.71103 17.0815 10.4828 16.5515C15.7497 12.9962 18.4685 11.1992 18.6392 11.1604C18.7597 11.1331 18.9265 11.0987 19.0396 11.1992C19.1527 11.2997 19.1415 11.49 19.1296 11.5411C19.0566 11.8523 16.1638 14.5417 14.6668 15.9335C14.2001 16.3673 13.8691 16.6751 13.8014 16.7454C13.6498 16.9028 13.4953 17.0518 13.3468 17.1949C12.4296 18.0791 11.7417 18.7422 13.3849 19.825C14.1745 20.3454 14.8064 20.7757 15.4368 21.205C16.1252 21.6738 16.8118 22.1414 17.7003 22.7238C17.9266 22.8722 18.1428 23.0263 18.3533 23.1764C19.1545 23.7475 19.8743 24.2607 20.7635 24.1788C21.2802 24.1313 21.814 23.6454 22.085 22.1964C22.7256 18.7717 23.9848 11.3517 24.2758 8.29404C24.3013 8.02615 24.2692 7.6833 24.2435 7.5328C24.2177 7.3823 24.1639 7.16787 23.9683 7.00913C23.7366 6.82114 23.3789 6.7815 23.219 6.78432C22.4917 6.79713 21.3758 7.18513 16.0057 9.41873Z"
        fill="black"
      />
    </svg>
  );
};
