import React from "react";

export const BookIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_1056_48946)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M29.0681 13.5334C28.6948 13.9334 26.6948 16.2001 26.4014 16.6001C26.1081 17.0001 25.7081 17.4534 25.3748 17.9334C24.6014 18.9601 23.8814 20.0268 23.1614 21.1068C23.1229 21.1551 23.0946 21.2108 23.0784 21.2704C23.0622 21.3301 23.0583 21.3924 23.0671 21.4536C23.0758 21.5148 23.097 21.5735 23.1292 21.6263C23.1615 21.679 23.2042 21.7245 23.2548 21.7601C23.3522 21.8358 23.4755 21.8699 23.598 21.8549C23.7204 21.8399 23.8319 21.7771 23.9081 21.6801C24.7748 20.6934 25.7081 19.7734 26.5748 18.8001C27.1614 18.1468 29.2414 15.6534 29.8014 14.9868C30.3614 14.3201 29.9481 12.5868 29.0681 13.5334Z"
        fill={props.color || "white"}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M31.6009 9.74659C31.5877 9.63228 31.5328 9.52686 31.4467 9.45057C31.3606 9.37428 31.2493 9.3325 31.1342 9.33325C31.0724 9.33141 31.0109 9.34222 30.9535 9.36502C30.896 9.38781 30.8438 9.42212 30.8001 9.46583C30.7564 9.50954 30.7221 9.56172 30.6993 9.61917C30.6765 9.67663 30.6657 9.73814 30.6676 9.79992C30.6676 10.6533 30.7609 14.0933 30.7342 14.5333C30.7076 15.1502 30.5955 15.7605 30.4009 16.3466C30.3 16.6088 30.1558 16.8522 29.9742 17.0666C29.6542 17.4666 29.2676 17.8399 28.9342 18.1866L26.0809 21.3333C24.7476 22.8133 23.6276 24.4799 22.3209 25.9733C22.0142 26.3066 21.6676 26.5999 21.3342 26.9199C21.4093 26.2024 21.4449 25.4813 21.4409 24.7599V23.7333C21.4409 23.3999 21.3476 23.0533 21.2809 22.7333C21.1476 22.0799 20.9476 21.4666 20.7742 20.8266C22.0409 19.2666 23.3742 17.7733 24.6542 16.2266C25.4676 15.2666 26.2276 14.2799 27.0142 13.3066C28.1876 11.8399 29.3342 10.3466 30.5342 8.89325C31.2142 8.13325 30.9476 7.90659 30.8009 7.79992C30.2413 7.33428 29.617 6.9525 28.9476 6.66659C28.0021 6.26035 27.0257 5.93041 26.0276 5.67992L19.0809 3.99992C18.0707 3.75243 17.0395 3.60064 16.0009 3.54659C15.2174 3.50195 14.433 3.61531 13.6942 3.87992C13.2146 4.09967 12.7662 4.38214 12.3609 4.71992C11.7342 5.17325 11.2142 5.70659 10.6676 6.13325C9.85423 6.86659 9.06756 7.63992 8.33423 8.43992C7.22756 9.62659 6.18756 10.8666 5.13423 12.1199C4.2809 13.1199 3.33423 14.0266 2.54756 15.0533C2.19217 15.5152 1.87554 16.0058 1.6009 16.5199C1.58125 16.5851 1.58125 16.6547 1.6009 16.7199C1.38808 16.8949 1.20367 17.1018 1.05423 17.3333C0.715904 17.8539 0.463777 18.4257 0.307562 19.0266C-0.136375 20.323 -0.136375 21.7302 0.307562 23.0266C0.783345 24.2764 1.61843 25.3573 2.70756 26.1333C3.66761 26.7996 4.75841 27.2541 5.90756 27.4666C7.50807 27.7198 9.1273 27.8358 10.7476 27.8133C12.1609 27.8133 13.9609 27.9466 15.7876 28.0799C17.6142 28.2133 19.3876 28.3733 20.8009 28.5466C20.8009 28.5466 22.6542 27.0533 23.0142 26.7066C24.4276 25.3733 25.6809 23.7866 27.1076 22.4399C27.5342 22.0399 27.9476 21.6266 28.3342 21.1999C28.9209 20.5733 29.4676 19.8666 30.0409 19.2799C30.4344 18.8602 30.7997 18.4147 31.1342 17.9466C31.3797 17.5825 31.5769 17.1881 31.7209 16.7733C31.9223 16.0396 32.0167 15.2806 32.0009 14.5199C32.0009 12.9066 31.6542 11.1599 31.6009 9.74659ZM3.66756 15.2266C4.41423 14.4266 5.26756 13.6799 6.02756 12.8666C7.14756 11.6933 8.25423 10.5466 9.4009 9.42659C10.1476 8.69325 10.9209 7.99992 11.7476 7.30659C12.2276 6.89325 12.7209 6.38659 13.2676 5.97325C13.5692 5.72061 13.9005 5.50569 14.2542 5.33325C14.786 5.15356 15.3471 5.07664 15.9076 5.10659C16.8664 5.13603 17.821 5.24754 18.7609 5.43992L25.7609 6.77325C26.7084 6.97004 27.6399 7.23745 28.5476 7.57325C28.97 7.76104 29.3759 7.98405 29.7609 8.23992C28.4276 9.57325 27.1609 10.8133 25.9209 12.1733C25.4009 12.7599 24.8809 13.3599 24.3742 13.9733C23.8676 14.5866 23.3742 15.2133 22.9209 15.8533C19.1876 21.0399 21.5876 20.3333 17.2142 19.3999C13.6942 18.6666 10.2542 17.3333 6.66756 16.7866C5.8009 16.6666 4.7609 16.3333 3.77423 16.2533C3.44346 16.2457 3.11279 16.2725 2.78756 16.3333C3.06149 15.9493 3.35522 15.58 3.66756 15.2266ZM15.9076 26.9066C14.0942 26.7066 12.2676 26.5466 10.8542 26.4533C9.31047 26.4175 7.77212 26.2569 6.25423 25.9733C5.323 25.7835 4.44464 25.3922 3.6809 24.8266C2.83612 24.2309 2.1903 23.3946 1.82756 22.4266C1.50628 21.4615 1.50628 20.4183 1.82756 19.4533C1.93123 19.0792 2.07425 18.7172 2.25423 18.3733C2.35408 18.1394 2.54036 17.9531 2.77423 17.8533C3.21421 17.7642 3.66758 17.7642 4.10756 17.8533C4.9609 17.9466 5.82756 18.1599 6.54756 18.2399C10.1076 18.5999 13.5609 19.7466 17.0809 20.3466C17.9625 20.5097 18.8529 20.621 19.7476 20.6799C19.6943 21.5369 19.6943 22.3963 19.7476 23.2533C19.807 23.7594 19.8916 24.2622 20.0009 24.7599C20.5609 28.3466 21.5342 27.5066 15.9076 26.9066Z"
        fill={props.color || "white"}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.6543 21.7998C16.1782 21.2712 14.6736 20.8261 13.1476 20.4664C12.4869 20.3219 11.8194 20.2106 11.1476 20.1331C10.4791 20.0551 9.80719 20.0106 9.13427 19.9998C7.56292 19.9412 5.98939 20.0036 4.4276 20.1864C4.36632 20.1864 4.30564 20.1985 4.24902 20.222C4.1924 20.2454 4.14095 20.2798 4.09762 20.3231C4.05429 20.3665 4.01991 20.4179 3.99646 20.4745C3.97301 20.5311 3.96094 20.5918 3.96094 20.6531C3.96094 20.7144 3.97301 20.7751 3.99646 20.8317C4.01991 20.8883 4.05429 20.9398 4.09762 20.9831C4.14095 21.0264 4.1924 21.0608 4.24902 21.0843C4.30564 21.1077 4.36632 21.1198 4.4276 21.1198C5.73347 21.1431 7.03734 21.2321 8.33427 21.3864C9.53427 21.5064 10.7476 21.7064 11.9476 21.8531C13.8009 22.0931 15.6276 22.2798 17.4009 22.6264C17.4526 22.6424 17.5071 22.6474 17.5608 22.641C17.6145 22.6346 17.6663 22.617 17.7128 22.5894C17.7593 22.5617 17.7995 22.5246 17.8307 22.4805C17.862 22.4364 17.8836 22.3861 17.8943 22.3331C17.9136 22.2823 17.9227 22.2283 17.921 22.174C17.9194 22.1197 17.9071 22.0662 17.8848 22.0167C17.8625 21.9672 17.8307 21.9225 17.7911 21.8853C17.7516 21.8481 17.7051 21.819 17.6543 21.7998Z"
        fill={props.color || "white"}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.3997 24.7068C16.0043 24.2386 14.5845 23.8469 13.1464 23.5334C12.5331 23.4134 11.9064 23.3201 11.2797 23.2401C10.6531 23.1601 10.0264 23.1201 9.38639 23.1068C7.91517 23.0486 6.44186 23.0708 4.97306 23.1734C4.91239 23.1734 4.85233 23.1856 4.79644 23.2092C4.74055 23.2328 4.68997 23.2674 4.6477 23.3109C4.60542 23.3544 4.57231 23.406 4.55033 23.4625C4.52835 23.5191 4.51794 23.5795 4.51973 23.6401C4.51968 23.7616 4.56701 23.8783 4.65166 23.9655C4.73632 24.0526 4.85162 24.1033 4.97306 24.1068C5.78639 24.1068 6.59973 24.2401 7.43973 24.3334C8.27973 24.4268 9.05306 24.5601 9.87973 24.6534L12.3064 24.9601C13.9864 25.1334 15.6264 25.2534 17.2131 25.5068C17.2656 25.5209 17.3204 25.5245 17.3743 25.5173C17.4282 25.5102 17.4802 25.4925 17.5273 25.4653C17.5743 25.438 17.6156 25.4017 17.6486 25.3585C17.6816 25.3153 17.7058 25.266 17.7197 25.2134C17.7403 25.1042 17.7187 24.9912 17.6593 24.8972C17.5999 24.8032 17.5072 24.7352 17.3997 24.7068Z"
        fill={props.color || "white"}
      />
    </g>
    <defs>
      <clipPath id="clip0_1056_48946">
        <rect width="32" height="32" fill={props.color || "white"} />
      </clipPath>
    </defs>
  </svg>
);
