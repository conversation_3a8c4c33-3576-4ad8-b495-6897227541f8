export const EmojiDay4Icon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="22"
      fill="none"
      viewBox="0 0 22 22"
      {...props}
    >
      <ellipse
        cx="10"
        cy="10.435"
        fill={props.color || "#fff"}
        rx="10"
        ry="10.435"
        transform="matrix(-1 0 0 1 21.373 .565)"
      ></ellipse>
      <path
        fill="#000"
        d="M5.792 16.212a7.1 7.1 0 0 0 5.217 2.2 8.06 8.06 0 0 0 5.27-2.26 6.96 6.96 0 0 0 1.739-2.774 4.1 4.1 0 0 0 .243-********* 0 0 0-.4-.478 6.3 6.3 0 0 0-.983 0c-1.39 0-4.4.174-7.104.321s-5.4.435-5.4.435a.71.71 0 0 0-.287.54c-.018.539.071 1.077.261 1.582a6.8 6.8 0 0 0 1.444 2.53m2.721-4.347a468 468 0 0 1 8.279-.087h.573a5.2 5.2 0 0 1-.643 2.182 5.7 5.7 0 0 1-1.104 1.504 7.23 7.23 0 0 1-4.61 2.053 6.4 6.4 0 0 1-4.712-1.844 5.7 5.7 0 0 1-1.157-1.817 5.2 5.2 0 0 1-.417-1.461 4 4 0 0 1 0-.409 35 35 0 0 1 3.826-.122z"
      ></path>
      <path
        fill="#000"
        d="M21.33 9.256a11.4 11.4 0 0 0-.6-2.609 8.63 8.63 0 0 0-2.939-3.913 9.25 9.25 0 0 0-4.495-1.8.313.313 0 0 0-.34.261.305.305 0 0 0 .261.33 8.6 8.6 0 0 1 4.166 1.783 7.94 7.94 0 0 1 2.608 3.635c.285.784.48 1.598.583 2.426.074.83.074 1.665 0 2.496a9.4 9.4 0 0 1-.557 2.39 11.2 11.2 0 0 1-1.13 2.2 8.4 8.4 0 0 1-3.922 3.044 10.9 10.9 0 0 1-4.991.74c-.94-.081-1.864-.289-2.748-.618a8.2 8.2 0 0 1-2.409-1.391 10.03 10.03 0 0 1-3.287-6.191 9.24 9.24 0 0 1 1.548-6.844 10.1 10.1 0 0 1 2.713-2.583 8.04 8.04 0 0 1 3.54-1.182c.617-.061 1.225-.113 1.843-.113q.612-.007 1.217.078a.261.261 0 1 0 .079-.513 9 9 0 0 0-1.288-.157Q10.224.712 9.27.804a8.9 8.9 0 0 0-3.808 1.252C4.31 2.76 3.3 3.676 2.487 4.752a10 10 0 0 0-1.818 7.4 10.92 10.92 0 0 0 3.522 6.826 9.1 9.1 0 0 0 2.696 1.582c.973.362 1.991.59 3.026.678a12 12 0 0 0 5.443-.834 9.24 9.24 0 0 0 4.305-3.479c.488-.754.888-1.562 1.191-2.408.303-.84.494-1.718.565-2.609.054-.885.025-1.773-.087-2.652"
      ></path>
      <path
        fill="#000"
        d="M4.34 8.717q.217-.463.53-.87c.183-.253.417-.466.687-.626.306-.17.65-.262 1-.27.361-.021.723.023 1.07.131a2.88 2.88 0 0 1 1.738 1.356.304.304 0 0 0 .392.157.296.296 0 0 0 .156-.391 3.54 3.54 0 0 0-1.965-2.026 3.26 3.26 0 0 0-1.461-.244c-.51.04-1 .217-1.417.513a3 3 0 0 0-.679.8 5.2 5.2 0 0 0-.548 1.305.261.261 0 1 0 .496.165M17.627 7.117a2.7 2.7 0 0 0-1.339-.93 2.42 2.42 0 0 0-1.66.078 2.81 2.81 0 0 0-1.74 2.539.28.28 0 0 0 .235.287.26.26 0 0 0 .287-.235 2.17 2.17 0 0 1 1.521-1.67 1.94 1.94 0 0 1 1.07-.052c.374.08.717.264.991.53.152.146.273.32.357.514q.137.328.209.678a.29.29 0 0 0 .33.252.296.296 0 0 0 .252-.34 3.8 3.8 0 0 0-.174-.99 2.2 2.2 0 0 0-.339-.661"
      ></path>
    </svg>
  );
};
