import { SVGProps } from "react";

export const LessonEndIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="35"
      height="34"
      fill="none"
      viewBox="0 0 35 34"
      {...props}
    >
      <path
        fill="#000"
        fillRule="evenodd"
        d="M22.457 27.51a6 6 0 0 0-1.009-.569c-2.172-.852-1.96-.213-.88-1.05 3.451-2.642 2.996-9.146-1.577-10.041a.483.483 0 0 0-.568.369c-.184.852 1.122.298 2.372 1.889 1.96 2.528.483 8.663-5.88 7.186-4.9-.994-4.26-9.615 2.43-9.174a.427.427 0 1 0 .084-.838 6.022 6.022 0 0 0-4.019 11.19c-3.11 1.265-5.68 3.793-5.212 7.102a.426.426 0 1 0 .852 0 5.13 5.13 0 0 1 1.15-2.585 18.2 18.2 0 0 1 3.153-3.153c1.335-.81 1.264-.752 1.35-.866q.703.14 1.42.184a11.56 11.56 0 0 0-.114 5.596.2.2 0 0 0 0 .085.412.412 0 0 0 .54.313.38.38 0 0 0 .383-.284.4.4 0 0 0 0-.128 20.3 20.3 0 0 0 .355-4.26c-.241-1.42-.213-1.137-.199-1.279a6.3 6.3 0 0 0 1.563-.255 27 27 0 0 1 2.84 1.817 12.8 12.8 0 0 1 2.94 4.772.485.485 0 0 0 .581.432.482.482 0 0 0 .356-.63 8.27 8.27 0 0 0-2.911-5.823"
        clipRule="evenodd"
      ></path>
      <path
        fill="#000"
        fillRule="evenodd"
        d="M15.896 19.756a6.1 6.1 0 0 1-3.38.908.426.426 0 0 0-.071.838c2.84.54 3.934 0 6.263-1.52.88.881 1.676 2.003 2.187 1.42a.483.483 0 0 0 0-.68c-1.207-1.265-1.32-2.657-2.57-2.302a24 24 0 0 0-2.429 1.335M14.306 7.684c-.696 1.32-2.415 3.848-.923 4.26 1.264.37 2.584-1.08 3.437-2.059 0 0 .298-.199.51-.383.342.355.71.71 1.094 1.036a7.8 7.8 0 0 0 2.471 1.42c1.52.143.91-2.527.64-4.047l-.228-.313c1.08-.525 2.997-1.42 2.912-2.655-.1-1.42-2.145-1.278-3.721-1.42h-.412L18.637.853c-1.022-1.279-2.201-1.051-2.84.227l-.952 2.102c0 .085-.213.27-.255.397a8.8 8.8 0 0 0-3.224.412c-.696.398-.668 1.264-.085 1.903.919.736 1.937 1.339 3.025 1.79m0-2.84c.27 0 .582.198 1.008 0 .426-.2.554-.782 1.832-3.267q.155.106.284.241c.213.284 1.193 2.486 1.62 2.954.158.16.36.269.581.313.955.02 1.906.13 2.84.326-.771.426-1.579.782-2.413 1.066a.97.97 0 0 0-.384 1.008c.273.875.464 1.773.568 2.684a10.8 10.8 0 0 1-2.485-2.002c-1.15-1.18-2.4 2.542-3.906 2.84a2 2 0 0 1 0-.27c.156-.767 1.15-2.386 1.42-3.167 0 .199.157-.639-.397-.639l-1.307-.64c-.227-.17-1.42-.837-1.619-1.192.17-.043 2.258-.284 2.386-.298zM34.088 9.047c-.142-1.42-2.102-1.179-3.636-1.335a2.5 2.5 0 0 0-.511 0l-1.42-2.386c-.952-1.108-1.946-.895-2.486.199l-.909 1.974a.483.483 0 0 0 .866.426c1.293-2.599 1.15-2.471 1.605-1.86.185.241 1.18 2.371 1.563 2.84s.937.256 1.164.298c.668.1 1.832 0 2.287.156 0 0-2.372 1.051-2.543 1.136a.92.92 0 0 0-.397 1.023c.24.769.412 1.558.511 2.358-2.84-1.492-2.33-3.295-4.26-.838-.228.27-1.307 1.76-1.918 1.86a7.1 7.1 0 0 1 0-2.641.427.427 0 1 0-.795-.313 8.7 8.7 0 0 0-.298 2.84c.099 1.236.994 1.42 2.016.824a9.4 9.4 0 0 0 1.918-1.846l.454-.327a10.1 10.1 0 0 0 2.3 1.818c.725.384 1.421.796 1.932.242s0-2.84-.1-3.991c.995-.483 2.756-1.364 2.657-2.457M2.66 11.575q-.27 1.366-.355 2.755a1.56 1.56 0 0 0 .255 1.25c.554.554 1.208.142 1.932-.242a10.1 10.1 0 0 0 2.3-1.832l.455.327a9.8 9.8 0 0 0 1.917 1.747c1.009.568 1.918.426 2.017-.824a8.6 8.6 0 0 0-.298-********** 0 0 0-.554-.242.426.426 0 0 0-.242.554 7.1 7.1 0 0 1 0 2.642c-.624-.1-1.718-1.62-1.917-1.86-1.945-2.458-1.42-.654-4.26.837q.152-1.202.51-2.357a.92.92 0 0 0-.397-1.023c-.17-.1-2.528-1.164-2.542-1.136.454-.114 1.62 0 2.287-.156.227 0 .823 0 1.164-.298.341-.299 1.69-3.239 1.946-*************.27.228 1.264 2.216a.483.483 0 0 0 .866-.426L8.1 5.525c-.54-1.094-1.534-1.307-2.414-.199l-1.42 2.386a2.5 2.5 0 0 0-.512 0C2.12 7.939.146 7.726.004 9.118c-.1 1.093 1.662 1.96 2.656 2.457"
        clipRule="evenodd"
      ></path>
    </svg>
  );
};
