import React from "react";

export const LinkCopyIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="21"
      height="21"
      viewBox="0 0 21 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M7.91408 13.0351C7.20943 13.0352 6.52057 12.8264 5.93462 12.435C5.34867 12.0436 4.89197 11.4872 4.62229 10.8362C4.3526 10.1852 4.28204 9.46883 4.41954 8.77772C4.55703 8.08661 4.89641 7.45181 5.39473 6.95361L9.07055 3.27851C9.73866 2.61035 10.6448 2.23496 11.5897 2.23492C12.5346 2.23489 13.4408 2.61021 14.109 3.27833C14.7772 3.94645 15.1526 4.85263 15.1526 5.79752C15.1526 6.74241 14.7773 7.64862 14.1092 8.31678L13.7115 8.71465L12.6921 7.69737L13.0937 7.29568C13.4902 6.89718 13.7125 6.35774 13.712 5.79559C13.7115 5.23344 13.4881 4.69442 13.0909 4.29666C12.6864 3.91066 12.1488 3.69529 11.5897 3.69529C11.0306 3.69529 10.493 3.91066 10.0885 4.29666L6.4131 7.97154C6.21594 8.16861 6.05954 8.4026 5.95283 8.66013C5.84612 8.91766 5.7912 9.19369 5.7912 9.47245C5.7912 9.75122 5.84612 10.0272 5.95283 10.2848C6.05954 10.5423 6.21594 10.7763 6.4131 10.9734C6.81757 11.3594 7.35519 11.5747 7.9143 11.5747C8.4734 11.5747 9.01102 11.3594 9.4155 10.9734L10.4338 11.9918C10.1036 12.3236 9.71092 12.5867 9.27841 12.7658C8.84591 12.9449 8.38219 13.0364 7.91408 13.0351Z"
        fill={props.color || "black"}
      />
      <path
        d="M13.6741 11.5951C12.9694 11.5952 12.2806 11.3864 11.6946 10.995C11.1087 10.6036 10.652 10.0472 10.3823 9.39619C10.1126 8.74519 10.042 8.02883 10.1795 7.33772C10.317 6.64661 10.6564 6.01181 11.1547 5.51361L11.5526 5.11574L12.5709 6.13425L12.173 6.5319C11.9759 6.72898 11.8196 6.96295 11.7129 7.22046C11.6062 7.47797 11.5513 7.75397 11.5513 8.03271C11.5513 8.31144 11.6062 8.58744 11.7129 8.84495C11.8196 9.10246 11.9759 9.33643 12.173 9.53351C12.5775 9.91952 13.1151 10.1349 13.6742 10.1349C14.2333 10.1349 14.771 9.91952 15.1754 9.53351C15.3626 9.34631 18.5805 6.12841 18.8512 5.85827C19.2487 5.45995 19.4719 4.92023 19.4719 4.35754C19.4719 3.79485 19.2487 3.25513 18.8512 2.85681C18.4468 2.4708 17.9091 2.25543 17.35 2.25543C16.7909 2.25543 16.2533 2.4708 15.8488 2.85681L14.8305 1.83837C15.4989 1.17122 16.4048 0.796705 17.3491 0.797119C18.2934 0.797534 19.199 1.17284 19.8668 1.84058C20.5345 2.50831 20.9099 3.41384 20.9103 4.35818C20.9108 5.30252 20.5363 6.20841 19.8692 6.87678C19.5981 7.1475 16.3815 10.3636 16.1944 10.5513C15.8642 10.8832 15.4714 11.1464 15.0388 11.3256C14.6062 11.5047 14.1423 11.5963 13.6741 11.5951Z"
        fill={props.color || "black"}
      />
      <path
        d="M16.592 20.9551H2.19195C1.81016 20.9547 1.44413 20.8028 1.17416 20.5329C0.904196 20.2629 0.752353 19.8969 0.751953 19.5151V5.11509C0.752353 4.7333 0.904196 4.36726 1.17416 4.09729C1.44413 3.82733 1.81016 3.67549 2.19195 3.67509H5.07195V5.11509H2.19195V19.5151H16.592V12.3151H18.032V19.5151C18.0316 19.8969 17.8797 20.2629 17.6097 20.5329C17.3398 20.8028 16.9737 20.9547 16.592 20.9551Z"
        fill={props.color || "black"}
      />
    </svg>
  );
};
