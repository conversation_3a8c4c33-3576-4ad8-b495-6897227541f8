import React from "react";

export const CheckIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="none"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="#fff"
        fillOpacity="0.01"
        d="M0 0h16v16H0z"
        style={{ mixBlendMode: "multiply" }}
      ></path>
      <path
        fill="currentColor"
        d="M6.5 12 2 7.5l.707-.707L6.5 10.585l6.793-6.792L14 4.5z"
      ></path>
    </svg>
  );
};
