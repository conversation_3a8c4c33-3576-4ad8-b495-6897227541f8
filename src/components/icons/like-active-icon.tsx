import React from "react";

export const LikeActiveIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="12"
      height="12"
      fill="none"
      viewBox="0 0 12 12"
      {...props}
    >
      <path
        fill="#fff"
        fillOpacity="0.01"
        d="M0 0h12v12H0z"
        style={{ mixBlendMode: "multiply" }}
      ></path>
      <path
        fill="#161616"
        d="M2.625 6H.75v5.25h1.875zM8.625 11.25h-5.25V5.699l1.14-1.711.318-2.219a.754.754 0 0 1 .742-.644h.05A1.126 1.126 0 0 1 6.75 2.25V4.5h3a1.5 1.5 0 0 1 1.5 1.5v2.625a2.63 2.63 0 0 1-2.625 2.625"
      ></path>
    </svg>
  );
};
