import type * as React from "react";

export const XpIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="19"
      height="16"
      fill="none"
      viewBox="0 0 19 16"
    >
      <g clipPath="url(#clip0_1056_49060)">
        <path
          fill="#000"
          d="M.568 6.071c.12.15.245.305.358.462 1.685 2.337 3.562 4.621 5.378 6.83.605.737 1.231 1.498 1.84 ***********.377.361.574.38l.053.002c.177 0 .363-.08.581-.252a8.3 8.3 0 0 0 1.032-.993c2.483-2.764 4.893-5.464 6.922-7.74.227-.253.448-.516.662-.77.195-.23.397-.47.601-.7q.352-.4.033-.787a6 6 0 0 0-.398-.43q-.485-.474-.973-.947-.495-.48-.988-.962c-.135-.132-.27-.269-.4-.401a25 25 0 0 0-.475-.475c-.506-.488-.93-.886-1.333-1.253a.84.84 0 0 0-.38-.18 2.7 2.7 0 0 0-.538-.042C12.433.06 11.749.06 11.177.06h-.51L9.182.119c-1.348.053-2.62.103-3.879.148-.294.011-.618.05-.727.387a.2.2 0 0 1-.053.058L4.5.734l-.406.388c-.334.32-.68.652-1.026.973Q1.667 3.4.47 4.837q-.18.232-.278.496a.33.33 0 0 0 .038.303c.108.148.225.294.338.435m16.186-.185-.01.013c-.062.076-.115.142-.174.206q-.358.384-.72.766c-.463.493-.943 1.003-1.4 1.515a779 779 0 0 0-3.204 3.613L9.88 13.544q.082-.204.171-.409l.88-2.028q1-2.306 1.996-4.612.084-.21.137-.428l.05-.18zm-4.91-.001-3.138 7.502a82 82 0 0 0-.808-2.567c-.516-1.578-1.049-3.209-1.384-4.867zm-1.7-4.889c.549-.017 1.101-.016 1.637-.015.538 0 1.094.002 1.644-.016l3.49 3.896-4.07.07h-.052zm-3.506 4.01C7.376 3.743 8.313 2.674 9.37 1.52l1.915 3.4zm-1.48.975c.42 1.732 1.004 3.447 1.568 5.106.164.482.332.977.496 1.47C6.01 11.207 4.9 9.774 3.823 8.385c-.612-.79-1.245-1.605-1.89-2.399.557-.053 2.477-.059 3.224-.004m2.359-4.927.749-.006q.033 0 .075.002C7.327 2.183 6.203 3.51 5.466 5.026h-4.01a1 1 0 0 1 .08-.098c.962-.95 1.771-1.748 2.597-2.557.212-.208.432-.418.644-.62q.297-.284.592-.572a.3.3 0 0 1 .122-.087.4.4 0 0 1 .158-.028c.622 0 1.255-.005 1.868-.01"
        ></path>
      </g>
      <defs>
        <clipPath id="clip0_1056_49060">
          <path fill="#fff" d="M.1 0h18.684v16H.1z"></path>
        </clipPath>
      </defs>
    </svg>
  );
};
