export const PanelIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.334 9.33333H24.0007C24.3543 9.33333 24.6934 9.47381 24.9435 9.72386C25.1935 9.97391 25.334 10.313 25.334 10.6667V21.3333C25.334 21.687 25.1935 22.0261 24.9435 22.2761C24.6934 22.5262 24.3543 22.6667 24.0007 22.6667H13.334V9.33333ZM12.0007 9.33333H8.00065C7.64703 9.33333 7.30789 9.47381 7.05784 9.72386C6.80779 9.97391 6.66732 10.313 6.66732 10.6667V21.3333C6.66732 21.687 6.80779 22.0261 7.05784 22.2761C7.30789 22.5262 7.64703 22.6667 8.00065 22.6667H12.0007V9.33333ZM5.33398 10.6667C5.33398 9.95942 5.61494 9.28115 6.11503 8.78105C6.61513 8.28095 7.29341 8 8.00065 8H24.0007C24.7079 8 25.3862 8.28095 25.8863 8.78105C26.3864 9.28115 26.6673 9.95942 26.6673 10.6667V21.3333C26.6673 22.0406 26.3864 22.7189 25.8863 23.219C25.3862 23.719 24.7079 24 24.0007 24H8.00065C7.29341 24 6.61513 23.719 6.11503 23.219C5.61494 22.7189 5.33398 22.0406 5.33398 21.3333V10.6667Z"
        fill="black"
      />
    </svg>
  );
};
