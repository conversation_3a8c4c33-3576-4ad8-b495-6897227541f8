import React from "react";

export const DiamondIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="32"
    height="28"
    viewBox="0 0 32 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_1056_48965)">
      <path
        d="M0.801342 10.3986C1.00746 10.6558 1.22059 10.9215 1.41393 11.1895C4.29985 15.1918 7.51555 19.1043 10.6252 22.8879C11.662 24.1492 12.734 25.4535 13.7779 26.7417C14.1116 27.1536 14.4233 27.3601 14.7601 27.3915C14.7905 27.3943 14.821 27.3957 14.8515 27.3957C15.154 27.3957 15.4732 27.257 15.8465 26.9647C16.4931 26.4406 17.0844 25.8713 17.6141 25.263C21.8664 20.5287 25.9934 15.9056 29.4697 12.0074C29.857 11.5732 30.2361 11.1238 30.6032 10.6893C30.9373 10.2933 31.283 9.88395 31.6327 9.48823C32.034 9.03389 32.053 8.58065 31.6889 8.14097C31.4767 7.88638 31.2491 7.64097 31.0069 7.40566C30.4527 6.86341 29.897 6.32233 29.3397 5.78239C28.775 5.23396 28.2112 4.68494 27.6484 4.13539C27.4172 3.909 27.1868 3.67467 26.9639 3.44807C26.7 3.17963 26.4269 2.9018 26.1491 2.63414C25.2822 1.79829 24.5571 1.11679 23.8672 0.489083C23.6897 0.337144 23.4641 0.229964 23.2167 0.179911C22.9136 0.125727 22.604 0.101639 22.2942 0.108121C21.1231 0.103535 19.952 0.104424 18.9709 0.104871H18.1044H18.0979L15.5539 0.204515C13.2467 0.29489 11.0673 0.380482 8.91184 0.458554C8.40683 0.476897 7.85234 0.544563 7.66532 1.12048C7.64312 1.15791 7.61281 1.19159 7.57583 1.2198C7.56117 1.23277 7.54673 1.24585 7.53328 1.25871C7.30131 1.47965 7.06986 1.70093 6.83897 1.92255C6.26606 2.47129 5.67356 3.03869 5.08089 3.58911C3.48461 5.07733 1.99929 6.64544 0.633274 8.28461C0.428652 8.54874 0.268659 8.83503 0.157827 9.13544C0.126656 9.22115 0.116313 9.31113 0.1273 9.40021C0.138287 9.48929 0.170394 9.57565 0.221838 9.6543C0.407519 9.90742 0.607728 10.1572 0.801342 10.3986ZM28.5226 10.0822C28.517 10.0894 28.5112 10.0965 28.5056 10.1035C28.3999 10.2344 28.3086 10.3476 28.2073 10.4565C27.7994 10.8956 27.3892 11.3333 26.9767 11.7695C26.1815 12.614 25.3593 13.4873 24.5764 14.363C22.7375 16.42 20.883 18.5207 19.0895 20.5522C18.3411 21.4 17.5924 22.2476 16.8432 23.0949C16.8123 23.13 16.7816 23.1646 16.7507 23.1985C16.8448 22.9668 16.9427 22.7296 17.0431 22.4977C17.545 21.3399 18.0471 20.1821 18.549 19.0246C19.6908 16.3916 20.8307 13.7581 21.9687 11.124C22.065 10.8844 22.1436 10.64 22.2043 10.3924C22.2311 10.2927 22.2592 10.1896 22.2904 10.0822H28.5226ZM20.1152 10.0793L14.7394 22.9294C14.3083 21.4486 13.8251 19.968 13.3554 18.5325C12.4721 15.8298 11.5593 13.0366 10.9854 10.196L20.1152 10.0793ZM17.203 1.70657C18.1417 1.67738 19.0886 1.67915 20.0069 1.6805C20.9276 1.68206 21.8795 1.68364 22.8219 1.65433L28.7992 8.32676L21.8274 8.44588L21.7398 8.44744L17.203 1.70657ZM11.1972 8.57417C12.461 6.41088 14.0671 4.5799 15.8787 2.60485L19.1577 8.42608L11.1972 8.57417ZM8.66177 10.2441C9.38271 13.2103 10.3816 16.148 11.3478 18.9898C11.6285 19.8152 11.9169 20.6636 12.1971 21.5085C10.1231 19.1925 8.22048 16.7387 6.37658 14.3606C5.32784 13.0078 4.24445 11.6107 3.13978 10.2519C4.09265 10.1604 7.38163 10.151 8.66177 10.244V10.2441ZM12.7023 1.80532C13.13 1.80174 13.5577 1.7984 13.9852 1.79527C14.0256 1.79471 14.0641 1.79673 14.1147 1.79975C12.377 3.74035 10.4523 6.0138 9.19098 8.60828H2.32176C2.363 8.54996 2.40938 8.49428 2.46061 8.44173C4.107 6.81465 5.49337 5.44705 6.90809 4.06101C7.27111 3.70476 7.6469 3.34572 8.00952 2.99843C8.34915 2.67354 8.68753 2.34765 9.02455 2.02075C9.07851 1.9575 9.15043 1.90638 9.23392 1.87189C9.31742 1.83741 9.40991 1.82064 9.50309 1.82312C10.5697 1.82323 11.6538 1.81382 12.7023 1.80521V1.80532Z"
        fill={props.color || "white"}
      />
    </g>
    <defs>
      <clipPath id="clip0_1056_48965">
        <rect width="32" height="27.4033" fill={props.color || "white"} />
      </clipPath>
    </defs>
  </svg>
);
