export const StartIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="17"
      height="16"
      fill="none"
      viewBox="0 0 17 16"
      {...props}
    >
      <path
        fill="#fff"
        fillOpacity="0.01"
        d="M0 0h16v16H0z"
        style={{ mixBlendMode: "multiply" }}
        transform="translate(.5)"
      ></path>
      <path
        fill="#fff"
        d="M4 14a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .74-.438l10 5.5a.5.5 0 0 1 0 .876l-10 5.5A.5.5 0 0 1 4 14"
      ></path>
    </svg>
  );
};
