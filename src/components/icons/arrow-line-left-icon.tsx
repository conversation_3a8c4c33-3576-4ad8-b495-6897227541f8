import React from "react";

export const ArrowLineLeftIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="21"
    height="20"
    fill="none"
    viewBox="0 0 21 20"
    {...props}
  >
    <path
      fill={props.color || "#fff"}
      fillOpacity="0.01"
      d="M0 0h20v20H0z"
      style={{ mixBlendMode: "multiply" }}
      transform="translate(.332)"
    ></path>
    <path
      fill={props.color || "#161616"}
      d="m9.082 16.25.881-.881-4.737-4.744h12.606v-1.25H5.226l4.737-4.744-.881-.881L2.832 10z"
    ></path>
  </svg>
);
