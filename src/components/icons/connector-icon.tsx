import { SVGProps } from "react";
import { cn } from "@/utils/cn";

interface ConnectorIconProps extends SVGProps<SVGSVGElement> {
  type?: "start" | "end";
  isActive?: boolean;
}

export const ConnectorIcon = ({ type, isActive }: ConnectorIconProps) => {
  const activeStyle = isActive ? "border-(--text-selected)" : "border-gray-200";

  return (
    <>
      <div
        className={cn(
          "h-20 w-1 rounded-full border-r-4 lg:hidden",
          activeStyle,
        )}
      />

      {type === "start" && (
        <div
          className={cn(
            "hidden h-28 w-full rounded-tr-xl border-t-4 border-r-4 lg:block",
            activeStyle,
          )}
        />
      )}
      {type === "end" && (
        <div
          className={cn(
            "hidden h-28 w-full rounded-bl-xl border-b-[5px] border-l-[5px] lg:block",
            activeStyle,
          )}
        />
      )}
      {!type && (
        <div
          className={cn(
            "hidden h-20 w-full rounded-tr-xl border-t-[5px] border-r-[5px] lg:block",
            activeStyle,
          )}
        />
      )}
    </>
  );
};
