export const ErrorIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="36"
      height="36"
      fill="none"
      viewBox="0 0 36 36"
      {...props}
    >
      <path
        fill="#F4364B"
        d="M0 18C0 8.059 8.059 0 18 0s18 8.059 18 18-8.059 18-18 18S0 27.941 0 18"
      ></path>
      <path
        fill="#fff"
        fillOpacity="0.01"
        d="M0 0h16v16H0z"
        style={{ mixBlendMode: "multiply" }}
        transform="translate(10 10)"
      ></path>
      <path
        fill="#fff"
        d="M18.001 13.086h-.002l-5.675 10.912.001.002h11.35v-.002zM17.437 16h1.125v4.5h-1.125zM18 23a.75.75 0 1 1 0-********* 0 0 1 0 1.5"
      ></path>
      <path
        fill="#fff"
        d="M24.5 25h-13a.5.5 0 0 1-.444-.73l6.5-12.5a.5.5 0 0 1 .888 0l6.5 12.5a.5.5 0 0 1-.444.73m-12.175-1h11.35v-.002l-5.674-10.912h-.002l-5.675 10.912z"
      ></path>
    </svg>
  );
};
