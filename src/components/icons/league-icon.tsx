import React from "react";

export const LeagueIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="32"
    height="28"
    viewBox="0 0 32 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.30698 9.63894C4.30698 9.75917 4.35474 9.87448 4.43976 9.9595C4.52477 10.0445 4.64008 10.0923 4.76031 10.0923C4.88054 10.0923 4.99585 10.0445 5.08087 9.9595C5.16588 9.87448 5.21364 9.75917 5.21364 9.63894C5.5865 7.50635 5.5639 5.32336 5.14698 3.19894C11.8136 0.532277 24.387 1.86561 26.7736 3.65228C26.8117 3.7876 26.8385 3.92585 26.8536 4.06561C27.2403 9.81228 26.787 10.4923 27.4936 10.4923C27.5997 10.4923 27.7015 10.4501 27.7765 10.3751C27.8515 10.3001 27.8936 10.1984 27.8936 10.0923C28.0003 3.74561 28.187 3.33228 27.827 2.93228C25.6936 0.678944 19.4003 0.198944 16.2803 0.26561C10.1603 0.22561 4.56031 1.99894 4.36031 2.74561C4.11332 3.66727 3.99222 4.61813 4.00031 5.57228C3.98114 6.93426 4.08377 8.29523 4.30698 9.63894Z"
      fill={props.color || "white"}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M25.7073 20.2788C25.7829 20.1895 25.821 20.0745 25.8137 19.9577C25.8064 19.841 25.7542 19.7316 25.6681 19.6524C25.5821 19.5732 25.4687 19.5304 25.3518 19.5328C25.2348 19.5352 25.1234 19.5828 25.0406 19.6655C23.214 21.5455 18.0673 25.2122 16.2406 25.4255C15.036 24.9045 13.8952 24.2469 12.8406 23.4655C7.24065 19.5988 7.38731 18.8922 6.90731 19.4655C6.83402 19.5403 6.79297 19.6408 6.79297 19.7455C6.79297 19.8502 6.83402 19.9507 6.90731 20.0255C8.50731 21.5588 14.254 27.2922 16.4273 26.9988C17.9481 26.5364 19.3715 25.7999 20.6273 24.8255C22.4801 23.4981 24.1834 21.9737 25.7073 20.2788Z"
      fill={props.color || "white"}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.36098 18.8921C3.13431 20.1188 4.82764 18.8921 5.52098 18.6788C8.18764 17.9321 11.441 16.5321 14.201 16.7855C15.761 16.9321 15.5343 17.4788 15.7876 18.1188C16.1321 18.6881 16.6338 19.1458 17.2322 19.4367C17.8307 19.7276 18.5005 19.8395 19.161 19.7588C20.601 19.7588 23.5076 18.8255 26.2276 18.6121C26.961 18.6121 27.681 18.3588 28.401 18.2521C29.7343 18.0521 29.561 18.3588 30.3076 18.2521C31.5343 18.2521 31.241 16.6388 31.241 16.0388C31.241 13.6921 31.9743 10.7855 28.4543 10.7055C26.3886 10.8441 24.3348 11.1248 22.3076 11.5455C20.1076 12.1055 19.801 12.2521 19.4676 12.0121C19.1343 11.7721 18.7743 10.6788 17.681 10.1055C14.9745 9.00433 11.9594 8.92848 9.20098 9.89213C7.27363 10.4089 5.41574 11.1565 3.66764 12.1188C1.90764 13.1721 1.00098 13.5455 1.22764 14.0921C1.33872 15.7436 1.72165 17.3654 2.36098 18.8921ZM17.121 11.2388C18.081 11.7321 18.0676 13.6388 20.1743 13.4655C21.0276 13.4655 21.8143 13.0788 22.5876 12.9455C30.9076 11.7455 29.7076 11.6121 29.6943 16.0521C29.6943 16.1321 29.6276 16.3988 29.601 16.6388C28.4202 16.6439 27.2439 16.7825 26.0943 17.0521C21.481 17.4388 19.1876 18.9988 17.4676 17.9721C16.801 17.5721 16.9476 17.1721 16.6676 16.6388C14.7876 13.2121 3.66764 18.4521 3.25431 18.3588C2.6341 16.9811 2.25957 15.5055 2.14764 13.9988C2.28098 13.9188 11.161 8.19879 17.121 11.2388Z"
      fill={props.color || "white"}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.69335 23.559C7.98668 24.2123 5.90668 26.3323 4.76001 26.399C4.75186 26.3281 4.75186 26.2565 4.76001 26.1857C6.21335 23.3323 5.97335 23.999 6.09335 23.6523C6.15876 23.5031 6.18009 23.3383 6.15484 23.1774C6.12959 23.0165 6.05879 22.8661 5.95083 22.7441C5.84286 22.6222 5.70223 22.5336 5.54556 22.489C5.38889 22.4444 5.22272 22.4455 5.06668 22.4923C4.1298 22.9462 3.10729 23.1961 2.06668 23.2257C3.30573 22.434 4.30955 21.3243 4.97335 20.0123C5.02639 19.9204 5.04074 19.8111 5.01323 19.7086C4.98573 19.6061 4.91862 19.5187 4.82668 19.4657C4.73474 19.4126 4.62549 19.3983 4.52297 19.4258C4.42045 19.4533 4.33306 19.5204 4.28001 19.6123C3.84956 20.3043 3.30392 20.9176 2.66668 21.4257C1.89777 21.9517 1.09615 22.4282 0.26668 22.8523C-0.599987 23.5457 0.693346 25.2123 3.90668 24.519C3.37335 25.7057 2.66668 27.3323 4.00001 27.6657C5.60001 28.0523 6.97335 26.5457 8.18668 25.439C8.74668 24.9323 9.89335 23.919 9.41335 23.4523C9.29591 23.3984 9.16533 23.3798 9.03751 23.3987C8.90968 23.4177 8.79011 23.4734 8.69335 23.559Z"
      fill={props.color || "white"}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M31.7337 22.9591C31.6137 22.8658 31.1471 22.6124 31.3871 22.7724C29.852 22.1923 28.5497 21.1243 27.6804 19.7324C27.6541 19.6869 27.6192 19.647 27.5775 19.615C27.5358 19.583 27.4882 19.5595 27.4374 19.5459C27.3867 19.5323 27.3337 19.5288 27.2816 19.5356C27.2295 19.5425 27.1793 19.5595 27.1337 19.5858C27.0882 19.612 27.0483 19.647 27.0163 19.6887C26.9843 19.7304 26.9608 19.778 26.9472 19.8287C26.9336 19.8795 26.9301 19.9325 26.9369 19.9846C26.9438 20.0367 26.9608 20.0869 26.9871 20.1324C27.6354 21.4347 28.6206 22.5397 29.8404 23.3324C28.8201 23.3019 27.817 23.0613 26.8937 22.6258C26.6903 22.5648 26.4712 22.5831 26.2807 22.6771C26.0902 22.7711 25.9424 22.9338 25.8671 23.1324C25.6804 23.7191 26.0537 24.1458 26.3204 24.5591L27.2271 26.3058C27.4404 26.7858 26.7737 26.5058 26.3737 26.3058C24.7604 25.4524 24.2404 24.2791 23.7071 24.8924C23.6255 24.979 23.5801 25.0935 23.5801 25.2124C23.5801 25.3314 23.6255 25.4459 23.7071 25.5324C24.4482 26.2895 25.2762 26.9563 26.1737 27.5191C27.0404 27.9458 28.2137 28.0124 28.5604 27.1058C28.6253 26.2545 28.43 25.4036 28.0004 24.6658C31.2671 25.3858 32.6271 23.6924 31.7337 22.9591Z"
      fill={props.color || "white"}
    />
  </svg>
);
