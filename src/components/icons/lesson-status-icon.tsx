import { SVGProps } from "react";
import { LessonStatus } from "@/types/lessons";

export const LessonStatusIcon = ({
  status,
  ...props
}: {
  status?: LessonStatus;
} & SVGProps<SVGSVGElement>) => {
  switch (status) {
    case LessonStatus.COMPLETED: {
      return (
        <div className="flex size-[18px] items-center justify-center rounded-full bg-(--lesson-completed-bg)">
          <svg
            width="9"
            height="8"
            viewBox="0 0 9 8"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
          >
            <path
              d="M0 3.75611L3.27273 7.02884L9 1.30156L7.69844 0L3.27273 4.42571L1.30156 2.45455L0 3.75611Z"
              fill="white"
            />
          </svg>
        </div>
      );
    }
    case LessonStatus.CURRENT: {
      return (
        <div className="flex size-[18px] items-center justify-center rounded-full bg-(--lesson-completed-bg)" />
      );
    }
    case LessonStatus.IN_PROGRESS: {
      return (
        <div className="flex size-[18px] items-center justify-center rounded-full border-(--card) border-3 bg-(--lesson-btn-active)" />
      );
    }
    case LessonStatus.LOCKED: {
      return (
        <div className="flex size-[18px] items-center justify-center rounded-full border-(--card) border-2 bg-gray-200">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="15"
            height="15"
            fill="none"
            viewBox="0 0 15 15"
          >
            <path
              fill="#fff"
              fillOpacity="0.01"
              d="M0 0h14.286v14.286H0z"
              style={{ mixBlendMode: "multiply" }}
            ></path>
            <path
              fill="#161616"
              d="M10.714 6.25h-.892V3.571a2.679 2.679 0 0 0-5.358 0V6.25h-.892a.893.893 0 0 0-.893.893V12.5a.893.893 0 0 0 .893.893h7.142a.893.893 0 0 0 .893-.893V7.143a.893.893 0 0 0-.893-.893M5.357 3.571a1.786 1.786 0 0 1 3.572 0V6.25H5.357zm5.357 8.929H3.572V7.143h7.142z"
            ></path>
          </svg>
        </div>
      );
    }
  }
};
