import React from "react";

export const ClockIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="13"
    height="14"
    fill="none"
    viewBox="0 0 13 14"
    {...props}
  >
    <path
      fill="#000"
      fillRule="evenodd"
      d="M4.749.719A5.27 5.27 0 0 0 .574 4.54a7.43 7.43 0 0 0 .522 5.62 6 6 0 0 0 4.456 2.778 6.9 6.9 0 0 0 5.042-1.068.276.276 0 1 0-.321-.45 6.27 6.27 0 0 1-4.641.804A5.28 5.28 0 0 1 1.81 9.719a6.62 6.62 0 0 1-.361-4.945A4.39 4.39 0 0 1 4.86 1.562a6.96 6.96 0 0 1 5.564 1.236c1.205 1.036 1.96 2.746 1.67 5.42a5.3 5.3 0 0 1-.265 1.245c-.138.378-.33.735-.57 1.06a.25.25 0 0 0 .049.345.25.25 0 0 0 .345-.057c.275-.353.497-.745.658-1.164.167-.417.28-.854.337-1.3.426-2.963-.41-4.882-1.718-6.07A7.79 7.79 0 0 0 4.75.719"
      clipRule="evenodd"
    ></path>
    <path
      fill="#000"
      fillRule="evenodd"
      d="M6.837 6.854v-.522c-.065-.803-.225-1.863-.25-2.112a.24.24 0 0 0-.24-.24.24.24 0 0 0-.25.24c0 .249-.136 1.325-.152 2.136v.915a.45.45 0 0 0 .418.442h2.69a.281.281 0 0 0 0-.554z"
      clipRule="evenodd"
    ></path>
  </svg>
);
