export const ReviseIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="17"
      height="16"
      fill="none"
      viewBox="0 0 17 16"
    >
      <path
        fill="#fff"
        fillOpacity="0.01"
        d="M0 0h16v16H0z"
        style={{ mixBlendMode: "multiply" }}
        transform="translate(.5)"
      ></path>
      <path
        fill={props.color || "#161616"}
        d="M3.5 3h10.086l-1.793-1.793L12.5.5l3 3-3 3-.707-.707L13.586 4H3.5v3.5h-1V4a1 1 0 0 1 1-1M5.207 10.207 3.414 12H13.5V8.5h1V12a1 1 0 0 1-1 1H3.414l1.793 1.793-.707.707-3-3 3-3z"
      ></path>
    </svg>
  );
};
