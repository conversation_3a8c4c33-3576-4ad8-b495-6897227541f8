export const EmojiDay1Icon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="19"
      fill="none"
      viewBox="0 0 20 19"
      {...props}
    >
      <ellipse
        cx="8.696"
        cy="9.074"
        fill={props.color || "#fff"}
        rx="8.696"
        ry="9.074"
        transform="matrix(-1 0 0 1 19.02 .361)"
      ></ellipse>
      <path
        fill="#000"
        d="M18.983 7.917a10 10 0 0 0-.522-2.268 7.56 7.56 0 0 0-2.555-3.403 8.1 8.1 0 0 0-3.91-1.565.27.27 0 0 0-.295.227.264.264 0 0 0 .227.287 7.56 7.56 0 0 1 3.63 1.55 6.94 6.94 0 0 1 2.268 3.161c.245.682.412 1.39.5 2.11.064.722.064 1.448 0 2.17a8 8 0 0 1-.492 2.08 9.3 9.3 0 0 1-.976 1.912 7.3 7.3 0 0 1-3.41 2.647 9.5 9.5 0 0 1-4.34.643 9 9 0 0 1-2.39-.537 7.2 7.2 0 0 1-2.094-1.21 8.76 8.76 0 0 1-2.858-5.384 8.07 8.07 0 0 1 1.346-5.95A8.8 8.8 0 0 1 5.47 2.14a7 7 0 0 1 3.077-1.028 16 16 0 0 1 1.603-.098 7 7 0 0 1 *************.227 0 1 0 .068-.446A8 8 0 0 0 10.158.5c-.551 0-1.103 0-1.655.068a7.7 7.7 0 0 0-3.32 1.089A9.5 9.5 0 0 0 2.597 4a8.7 8.7 0 0 0-1.58 6.434 9.5 9.5 0 0 0 3.062 5.936 7.9 7.9 0 0 0 2.344 1.376c.848.312 1.733.51 2.632.59 1.613.129 3.233-.12 4.733-.726a8 8 0 0 0 3.743-3.024 10 10 0 0 0 1.036-2.087 8.8 8.8 0 0 0 .492-2.269 12.4 12.4 0 0 0-.076-2.314"
      ></path>
      <path
        fill="#000"
        d="M5.576 7.471a1.565 1.565 0 0 0 2.163-.605 1.346 1.346 0 0 0-.56-1.739 1.32 1.32 0 0 0-1.255.098c-.726.424-1.21 1.649-.348 2.246M12.821 7.479a1.278 1.278 0 0 0 1.944-.93 1.285 1.285 0 0 0-.636-1.376c-1.293-.552-2.563.854-1.776 1.913.123.166.283.3.468.393M7.988 14.36a4.5 4.5 0 0 1-1.27-.794 4.05 4.05 0 0 1-1.331-3.448.227.227 0 0 0-.286-.245.23.23 0 0 0-.168.192 4.6 4.6 0 0 0 1.293 4.023c.415.429.904.78 1.444 1.035.82.37 1.721.52 2.617.439a6.05 6.05 0 0 0 3.78-1.92 5.33 5.33 0 0 0 1.445-3.895.25.25 0 0 0-.265-.25.257.257 0 0 0-.25.265 4.72 4.72 0 0 1-1.459 3.373 5.3 5.3 0 0 1-3.35 1.588 4.54 4.54 0 0 1-2.2-.363"
      ></path>
    </svg>
  );
};
