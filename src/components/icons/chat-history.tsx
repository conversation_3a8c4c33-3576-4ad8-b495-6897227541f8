export const ChatHistory = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="none"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="#fff"
        fillOpacity="0.01"
        d="M0 0h16v16H0z"
        style={{ mixBlendMode: "multiply" }}
      ></path>
      <path fill="#161616" d="M5.716 9.5 4.423 8.207V6h1v1.793l1 1z"></path>
      <path
        fill="#161616"
        fillRule="evenodd"
        d="M4.338 10.942q.289.058.582.058H3a1 1 0 0 1-1-1V8.675a3 3 0 0 0 2.338 2.267M1 7.22a4 4 0 0 0 0 1.56z"
        clipRule="evenodd"
      ></path>
      <path
        fill="#161616"
        fillRule="evenodd"
        d="M2 5.27v2.055a3 3 0 0 0 0 1.35V10a1 1 0 0 0 1 1h4.5v.057A4 4 0 0 1 1 8.78V7.22a4 4 0 0 1 1-1.95"
        clipRule="evenodd"
      ></path>
      <path
        fill="#161616"
        d="M2 7.325V5.269q.045-.049.094-.098A4 4 0 1 1 7.5 11.057V11H4.92A3 3 0 1 0 2 7.325"
      ></path>
      <path
        fill="#161616"
        fillRule="evenodd"
        d="m8 ********* 1.71-3H13a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2H3a2 2 0 0 0-2 2v4.22a4 4 0 0 1 1-1.95V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1h-3zM2 7.325a3 3 0 0 0 0 1.35z"
        clipRule="evenodd"
      ></path>
    </svg>
  );
};
