export const InfoIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect width="32" height="32" rx="16" fill="#456DFF" />
      <rect
        width="14.2222"
        height="14.2222"
        transform="translate(8.88892 8.88867)"
        fill="white"
        fill-opacity="0.01"
        style={{ mixBlendMode: "multiply" }}
      />
      <path
        d="M15.9999 10.6665C16.1977 10.6665 16.391 10.7247 16.5555 10.8337C16.7199 10.9428 16.8481 11.0977 16.9238 11.279C16.9995 11.4603 17.0193 11.6599 16.9807 11.8523C16.9421 12.0448 16.8469 12.2216 16.707 12.3604C16.5672 12.4991 16.389 12.5936 16.195 12.6319C16.001 12.6702 15.8 12.6506 15.6172 12.5755C15.4345 12.5004 15.2783 12.3732 15.1684 12.21C15.0586 12.0468 14.9999 11.855 14.9999 11.6588C14.9999 11.3956 15.1053 11.1432 15.2928 10.9571C15.4803 10.771 15.7347 10.6665 15.9999 10.6665ZM18.6666 21.3332H13.3333V19.8448H15.2499V16.0412H13.9999V14.5528H16.7499V19.8448H18.6666V21.3332Z"
        fill="white"
      />
    </svg>
  );
};
