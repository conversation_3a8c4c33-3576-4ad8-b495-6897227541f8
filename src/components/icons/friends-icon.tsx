import React from "react";

export const FriendsIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_1056_48952)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.7059 21.134C13.6937 21.0788 13.6707 21.0266 13.6382 20.9803C13.6058 20.9341 13.5646 20.8946 13.5169 20.8643C13.4692 20.834 13.4161 20.8133 13.3604 20.8035C13.3048 20.7938 13.2477 20.795 13.1926 20.8073C13.1374 20.8195 13.0852 20.8425 13.039 20.875C12.9927 20.9074 12.9533 20.9486 12.9229 20.9963C12.8926 21.044 12.872 21.0971 12.8622 21.1528C12.8524 21.2084 12.8537 21.2655 12.8659 21.3206L13.8259 24.8406C13.9039 25.1238 13.953 25.4142 13.9726 25.7073C14 25.8616 14 26.0196 13.9726 26.174C13.7459 25.9606 12.9459 26.174 12.7592 26.174H12.2393L8.11925 25.8673H6.78592H6.66592V25.6006L7.35925 22.4273C7.35925 22.4273 7.49258 21.7206 7.53258 21.454C7.55929 21.2832 7.53265 21.1083 7.45629 20.9532C7.37993 20.7981 7.25758 20.6703 7.10592 20.5873C7.00746 20.5427 6.90064 20.5197 6.79258 20.5197C6.68452 20.5197 6.5777 20.5427 6.47925 20.5873C6.35925 20.5873 5.99925 20.854 5.95925 20.8806C5.71002 20.9797 5.4507 21.0512 5.18592 21.094C4.9341 21.1332 4.67773 21.1332 4.42592 21.094C4.28249 21.0877 4.1398 21.0699 3.99925 21.0406C3.84997 20.9739 3.70715 20.8935 3.57258 20.8006L3.22592 20.4273L3.06592 20.0673C3.03272 19.9269 3.04597 19.7795 3.10367 19.6473C3.16137 19.5151 3.26041 19.4051 3.38592 19.334C4.07159 19.0625 4.77556 18.8397 5.49258 18.6673L6.58592 18.174C6.85336 18.0134 7.10356 17.8257 7.33258 17.614C7.45258 17.5073 7.54592 17.3873 7.65258 17.2806C8.63301 17.6949 9.71496 17.8063 10.7592 17.6006C11.3726 17.4238 11.9346 17.1028 12.3986 16.6644C12.8625 16.226 13.2147 15.683 13.4259 15.0806C13.6152 14.5637 13.6994 14.0141 13.6734 13.4641C13.6474 12.9142 13.5118 12.375 13.2746 11.8782C13.0374 11.3814 12.7033 10.937 12.2919 10.571C11.8806 10.2051 11.4003 9.92505 10.8792 9.74729C9.89345 9.41005 8.82078 9.42804 7.84684 9.79814C6.8729 10.1682 6.05898 10.8672 5.54592 11.774C5.24984 12.2905 5.0766 12.8682 5.03961 13.4624C5.00261 14.0566 5.10286 14.6514 5.33258 15.2006C5.66668 15.9254 6.20997 16.5337 6.89258 16.9473L6.78592 17.054C6.58097 17.2188 6.3624 17.366 6.13258 17.494L5.15925 17.854C4.34592 18.1206 3.07925 18.1073 2.49258 18.6806C2.29021 18.8712 2.13624 19.1073 2.04348 19.3694C1.95071 19.6314 1.92184 19.9118 1.95925 20.1873C1.96963 20.3419 2.00103 20.4945 2.05258 20.6406C2.0966 20.7753 2.1547 20.9049 2.22592 21.0273C2.30386 21.1676 2.39304 21.3014 2.49258 21.4273C2.59793 21.5526 2.71392 21.6686 2.83925 21.774C3.04863 21.942 3.27682 22.0852 3.51925 22.2006C3.75913 22.3182 4.01532 22.3991 4.27925 22.4406C4.59753 22.4875 4.92097 22.4875 5.23925 22.4406C5.47746 22.4257 5.71391 22.39 5.94592 22.334L5.33258 25.334C5.22259 25.7061 5.16867 26.0926 5.17258 26.4806C5.20328 26.6047 5.26104 26.7204 5.34172 26.8195C5.4224 26.9187 5.524 26.9987 5.63925 27.054C5.90889 27.1637 6.19101 27.2398 6.47925 27.2806C6.65677 27.2943 6.83507 27.2943 7.01258 27.2806C6.79419 28.1986 6.76696 29.1517 6.93258 30.0806C7.05812 30.5403 7.33962 30.942 7.72883 31.2169C8.11804 31.4917 8.59074 31.6227 9.06592 31.5873C9.55433 31.6124 10.0331 31.4448 10.3993 31.1206C10.597 31.4078 10.8651 31.6394 11.1779 31.7935C11.4907 31.9475 11.8377 32.0189 12.1859 32.0006C12.5468 31.9944 12.9007 31.9001 13.2167 31.7258C13.5328 31.5515 13.8014 31.3025 13.9993 31.0006C14.2991 30.4928 14.4771 29.9223 14.5193 29.334C14.5395 28.6502 14.4858 27.9662 14.3593 27.294C14.5671 27.2256 14.7427 27.0832 14.8526 26.894C15.0097 26.5802 15.0831 26.2311 15.0659 25.8806C15.0418 25.4268 14.9566 24.9783 14.8126 24.5473L13.7059 21.134ZM6.50592 14.6673C6.3189 14.2878 6.22164 13.8704 6.22164 13.4473C6.22164 13.0242 6.3189 12.6068 6.50592 12.2273C6.66759 11.8538 6.90229 11.5165 7.19622 11.235C7.49015 10.9535 7.83738 10.7337 8.21749 10.5883C8.59761 10.443 9.00295 10.3751 9.40968 10.3887C9.81641 10.4022 10.2163 10.497 10.5859 10.6673C11.2642 10.9612 11.8168 11.4854 12.1459 12.1473C12.3418 12.5365 12.4494 12.9642 12.4609 13.3998C12.4725 13.8355 12.3877 14.2682 12.2126 14.6673C12.0706 15.0565 11.8362 15.4054 11.5297 15.6841C11.2231 15.9628 10.8535 16.1629 10.4526 16.2673C9.6958 16.3951 8.9182 16.3026 8.21258 16.0006C7.49752 15.7842 6.88894 15.3087 6.50592 14.6673ZM9.81258 29.334C9.79064 29.563 9.72261 29.7852 9.61258 29.9873C9.56287 30.1013 9.48341 30.1998 9.38254 30.2725C9.28167 30.3452 9.1631 30.3895 9.03925 30.4006C8.80486 30.4472 8.56158 30.4117 8.35031 30.3C8.13905 30.1883 7.9727 30.0072 7.87925 29.7873C7.67557 28.9946 7.63925 28.1681 7.77258 27.3606H8.06592H9.98592C9.98013 27.387 9.98013 27.4143 9.98592 27.4406C9.84911 28.062 9.79088 28.6981 9.81258 29.334ZM13.4926 29.2673C13.4352 29.6843 13.2798 30.0818 13.0392 30.4273C12.9365 30.5524 12.8057 30.6514 12.6574 30.7163C12.5091 30.7812 12.3475 30.8101 12.1859 30.8006C12.044 30.8001 11.9052 30.7598 11.785 30.6844C11.6648 30.609 11.5681 30.5015 11.5059 30.374C11.3566 30.047 11.275 29.6933 11.2659 29.334C11.2722 29.2363 11.2722 29.1383 11.2659 29.0406L11.3459 28.1073V27.574C11.3547 27.5255 11.3547 27.4758 11.3459 27.4273H12.6793H13.4659C13.547 28.0151 13.556 28.6106 13.4926 29.2006V29.2673Z"
        fill={props.color || "white"}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M30.0254 17.3342C29.9595 16.98 29.8016 16.6495 29.5676 16.3757C29.3335 16.1018 29.0316 15.8944 28.692 15.7742C28.2581 15.653 27.8026 15.6302 27.3587 15.7075C26.795 15.8206 26.242 15.9812 25.7054 16.1875C26.2992 15.3624 26.5567 14.3422 26.4254 13.3342C25.9587 8.56083 18.852 8.49417 18.6254 13.4542C18.5521 14.4181 18.8305 15.376 19.409 16.1506C19.9875 16.9252 20.8269 17.464 21.772 17.6675C22.0384 17.7293 22.3125 17.7518 22.5854 17.7342C22.9642 17.7035 23.3393 17.6365 23.7054 17.5342C24.0565 17.4186 24.3889 17.2524 24.692 17.0408C24.7312 17.0937 24.7846 17.1342 24.846 17.1578C24.9074 17.1815 24.9742 17.1871 25.0387 17.1742C25.572 17.1075 26.372 16.9208 27.1054 16.8675C27.4577 16.8247 27.8152 16.8706 28.1454 17.0008C28.2963 17.0769 28.4212 17.1961 28.5043 17.3433C28.5873 17.4905 28.6248 17.659 28.612 17.8275C28.619 18.2654 28.4781 18.6929 28.212 19.0408C27.9002 19.4204 27.4895 19.7065 27.0254 19.8675C26.2253 20.092 25.4098 20.2569 24.5854 20.3608C24.4866 20.386 24.4018 20.4492 24.3494 20.5366C24.2969 20.624 24.2811 20.7285 24.3054 20.8275L24.6654 22.8142L25.132 24.7742C25.4072 25.4443 25.6432 26.1299 25.8387 26.8275C25.9612 27.2379 26.0108 27.6666 25.9854 28.0942C25.9911 28.3394 25.9341 28.582 25.8197 28.799C25.7054 29.016 25.5375 29.2002 25.332 29.3342C23.3854 30.4675 23.2387 27.9208 23.2254 27.8808C23.0356 27.1755 22.795 26.4848 22.5054 25.8142C22.4682 25.7063 22.3898 25.6176 22.2873 25.5676C22.1847 25.5176 22.0665 25.5104 21.9587 25.5475C21.8508 25.5846 21.7622 25.6631 21.7121 25.7656C21.6621 25.8681 21.6549 25.9863 21.692 26.0942C21.9136 26.7438 22.0918 27.4075 22.2254 28.0808C22.2987 28.482 22.2987 28.893 22.2254 29.2942C22.1724 29.4706 22.0655 29.626 21.9196 29.7384C21.7736 29.8508 21.5961 29.9146 21.412 29.9208C21.2155 29.948 21.0153 29.931 20.8262 29.8711C20.637 29.8111 20.4637 29.7097 20.3187 29.5742C20.1147 29.3578 19.9399 29.1158 19.7987 28.8542C19.5667 28.421 19.3575 27.9759 19.172 27.5208C18.7315 26.4458 18.3836 25.3351 18.132 24.2008C17.8707 23.0484 17.6748 21.8821 17.5454 20.7075L17.412 18.2142C17.412 18.1116 17.3713 18.0133 17.2988 17.9408C17.2263 17.8682 17.1279 17.8275 17.0254 17.8275C16.9228 17.8275 16.8245 17.8682 16.7519 17.9408C16.6794 18.0133 16.6387 18.1116 16.6387 18.2142L16.5587 20.8008C16.5544 22.285 16.6838 23.7665 16.9454 25.2275C17.1595 26.1752 17.4539 27.103 17.8254 28.0008C18.0307 28.5406 18.2804 29.0624 18.572 29.5608C18.7926 29.9209 19.0568 30.2523 19.3587 30.5475C19.6673 30.8156 20.0334 31.0091 20.4288 31.1132C20.8241 31.2172 21.2381 31.2289 21.6387 31.1475C22.0393 31.0843 22.4116 30.9015 22.7066 30.6232C23.0016 30.3448 23.2057 29.9838 23.292 29.5875C23.2728 29.6731 23.2803 29.7625 23.3138 29.8437C23.3472 29.9248 23.4048 29.9936 23.4787 30.0408C23.8219 30.2826 24.2206 30.4338 24.6378 30.4804C25.055 30.5271 25.4773 30.4676 25.8654 30.3075C26.2569 30.1154 26.5954 29.8302 26.8511 29.4768C27.1068 29.1235 27.2719 28.7128 27.332 28.2808C27.4238 27.6909 27.4011 27.0889 27.2654 26.5075C27.0404 25.7377 26.7504 24.9883 26.3987 24.2675L25.692 22.5342L25.172 21.0008C25.2337 21.0585 25.3143 21.0917 25.3987 21.0942C26.1852 21.1443 26.9746 21.0586 27.732 20.8408C28.2842 20.6803 28.7911 20.3926 29.212 20.0008C29.5434 19.6487 29.7891 19.225 29.9301 18.7625C30.0712 18.3 30.1038 17.8113 30.0254 17.3342ZM23.2787 16.1475C23.071 16.2182 22.8565 16.2674 22.6387 16.2942C22.4485 16.3208 22.2555 16.3208 22.0654 16.2942C21.417 16.1776 20.8308 15.835 20.411 15.3273C19.9912 14.8196 19.7648 14.1796 19.772 13.5208C19.6787 9.9475 24.9587 9.9075 25.1054 13.5208C25.147 14.1015 24.9884 14.6789 24.656 15.1568C24.3236 15.6348 23.8375 15.9844 23.2787 16.1475Z"
        fill={props.color || "white"}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.0255 1.33418C17.0703 1.22797 17.0717 1.1084 17.0293 1.00118C16.9869 0.893957 16.9042 0.807659 16.7988 0.760847C16.5011 0.619123 16.1936 0.498818 15.8788 0.400847C15.5855 0.307513 15.2655 0.227513 14.9588 0.160847L13.6255 0.000846636C13.08 -0.0140683 12.5345 0.0306413 11.9988 0.13418C11.7385 0.205644 11.506 0.354423 11.3321 0.560847C11.2632 0.668269 11.2266 0.793216 11.2266 0.920847C11.2266 1.04848 11.2632 1.17342 11.3321 1.28085L11.5188 1.53418L12.1721 2.58751C12.4255 2.98751 12.7455 3.40085 13.0388 3.84085H12.7988C12.625 3.88171 12.4586 3.94918 12.3055 4.04085C12.0466 4.1812 11.8217 4.37662 11.6465 4.61334C11.4714 4.85006 11.3503 5.12232 11.2918 5.41092C11.2333 5.69952 11.2389 5.99743 11.308 6.28366C11.3772 6.56989 11.5083 6.83746 11.6921 7.06751C11.8526 7.33448 12.068 7.56432 12.324 7.74182C12.5799 7.91932 12.8707 8.04044 13.177 8.09716C13.4833 8.15388 13.7981 8.14492 14.1007 8.07085C14.4033 7.99678 14.6867 7.8593 14.9321 7.66751C15.2223 7.43265 15.4283 7.10972 15.5188 6.74751C15.6211 6.30896 15.6211 5.85273 15.5188 5.41418C15.3547 4.82628 15.0981 4.26821 14.7588 3.76085C14.3188 3.05418 13.7455 2.42751 13.3055 1.78751L12.9588 1.33418C13.2063 1.23326 13.4602 1.14864 13.7188 1.08085H14.7721C15.0655 1.08085 15.3455 1.17418 15.6255 1.24085C15.9068 1.30503 16.1831 1.3897 16.4521 1.49418C16.5504 1.54417 16.6638 1.55547 16.7699 1.52584C16.8761 1.49621 16.9673 1.42782 17.0255 1.33418ZM13.5188 4.60085C13.7577 4.96775 13.9418 5.3675 14.0655 5.78751C14.0989 5.97268 14.0989 6.16235 14.0655 6.34751C14.0708 6.40128 14.064 6.45556 14.0456 6.50634C14.0271 6.55712 13.9974 6.60309 13.9588 6.64085C13.8542 6.72986 13.732 6.79574 13.6002 6.83416C13.4684 6.87258 13.3299 6.88268 13.1939 6.8638C13.0579 6.84491 12.9275 6.79746 12.8111 6.72456C12.6947 6.65167 12.5951 6.55498 12.5188 6.44085C12.4112 6.32403 12.331 6.18465 12.2841 6.03288C12.2372 5.88112 12.2248 5.7208 12.2478 5.56363C12.2708 5.40647 12.3286 5.25642 12.417 5.12445C12.5054 4.99249 12.6222 4.88193 12.7588 4.80085C12.8476 4.72773 12.9528 4.67739 13.0655 4.65418L13.5188 4.60085Z"
        fill={props.color || "white"}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.4124 8.10777C21.7497 7.97753 22.0526 7.77154 22.2976 7.50568C22.5427 7.23982 22.7234 6.92121 22.8257 6.57444C23.0896 5.3395 23.1615 4.07129 23.0391 2.81444V1.70777L23.3724 1.77444C23.6755 1.85696 23.9667 1.97794 24.2391 2.13444C24.4428 2.25277 24.6309 2.3961 24.7991 2.56111C24.9857 2.73444 25.1591 2.92111 25.3324 3.10777C25.4161 3.1846 25.5255 3.22724 25.6391 3.22724C25.7527 3.22724 25.8621 3.1846 25.9457 3.10777C25.9869 3.06799 26.0196 3.02033 26.0419 2.96765C26.0643 2.91497 26.0758 2.85833 26.0758 2.80111C26.0758 2.74388 26.0643 2.68724 26.0419 2.63456C26.0196 2.58188 25.9869 2.53422 25.9457 2.49444C25.7431 2.22424 25.525 1.96603 25.2924 1.72111C25.1607 1.58107 25.018 1.45177 24.8657 1.33444C24.4611 1.04336 24.0104 0.822498 23.5324 0.681105C23.1918 0.57627 22.8346 0.535581 22.4791 0.561105C22.3458 0.564426 22.217 0.610177 22.1115 0.691706C22.006 0.773235 21.9293 0.886291 21.8924 1.01444V1.53444L21.7191 2.86777V4.70777C21.4116 4.64524 21.0994 4.60955 20.7857 4.60111C20.4677 4.60021 20.155 4.68298 19.8791 4.84111C19.4946 5.06923 19.2025 5.42521 19.0538 5.84684C18.9052 6.26847 18.9094 6.72894 19.0657 7.14777C19.2539 7.58268 19.6048 7.92665 20.0434 8.10607C20.482 8.28549 20.9734 8.2861 21.4124 8.10777ZM20.3591 5.54777C20.4839 5.45827 20.6323 5.40729 20.7857 5.40111C21.0562 5.37445 21.3286 5.37445 21.5991 5.40111C21.5938 5.50326 21.5938 5.60562 21.5991 5.70777C21.5057 6.18777 21.3857 6.64111 20.9724 6.80111C20.9105 6.85836 20.8366 6.90091 20.756 6.92558C20.6753 6.95025 20.5902 6.9564 20.5069 6.94359C20.4236 6.93077 20.3442 6.89931 20.2748 6.85155C20.2053 6.80379 20.1475 6.74097 20.1057 6.66777C19.9946 6.48464 19.959 6.26553 20.0062 6.05661C20.0535 5.84769 20.18 5.66526 20.3591 5.54777Z"
        fill={props.color || "white"}
      />
    </g>
    <defs>
      <clipPath id="clip0_1056_48952">
        <rect width="32" height="32" fill={props.color || "white"} />
      </clipPath>
    </defs>
  </svg>
);
