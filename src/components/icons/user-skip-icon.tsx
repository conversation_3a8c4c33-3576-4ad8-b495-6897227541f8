import { SVGProps } from "react";

export const UserSkipIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      fill="none"
      viewBox="0 0 20 20"
      {...props}
    >
      <path
        fill="#fff"
        fillOpacity="0.01"
        d="M0 0h24v24H0z"
        style={{ mixBlendMode: "multiply" }}
      ></path>
      <path
        fill="#161616"
        d="M16.5 6.75h3.9a8.25 8.25 0 0 0-15.15 4.5h-1.5A9.75 9.75 0 0 1 21 5.025V2.25h1.5v6h-6z"
      ></path>
      <path
        fill="#161616"
        d="M12 9a2.25 2.25 0 1 1-2.25 2.25A2.21 2.21 0 0 1 12 9m0-1.5a3.75 3.75 0 1 0 0 7.5 3.75 3.75 0 0 0 0-7.5M17.25 22.5h-1.5v-2.25A2.25 2.25 0 0 0 13.5 18h-3a2.25 2.25 0 0 0-2.25 2.25v2.25h-1.5v-2.25a3.75 3.75 0 0 1 3.75-3.75h3a3.75 3.75 0 0 1 3.75 3.75z"
      ></path>
    </svg>
  );
};
