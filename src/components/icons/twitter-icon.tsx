export const TwitterIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="30"
      height="30"
      fill="none"
      viewBox="0 0 30 30"
      {...props}
    >
      <path
        fill="#fff"
        fillOpacity="0.01"
        d="M0 0h28.444v28.444H0z"
        style={{ mixBlendMode: "multiply" }}
        transform="translate(.832 .653)"
      ></path>
      <path
        fill="#161616"
        d="m17.04 13.242 7.771-9.034H22.97l-6.748 7.844-5.389-7.844H4.617l8.15 11.861-8.15 9.473H6.46l7.125-8.283 5.692 8.283h6.216zm-2.522 2.932-.826-1.181-6.57-9.398h2.829l5.302 7.584.826 1.181 6.892 9.858h-2.829z"
      ></path>
    </svg>
  );
};
