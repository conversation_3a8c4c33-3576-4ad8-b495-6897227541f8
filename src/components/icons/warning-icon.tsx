export const WarningIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="36"
      height="36"
      fill="none"
      viewBox="0 0 36 36"
      {...props}
    >
      <rect width="36" height="36" fill="#FFB800" rx="18"></rect>
      <path
        fill="#fff"
        d="M0 0h16v16H0z"
        style={{ mixBlendMode: "multiply" }}
        transform="translate(10 10)"
      ></path>
      <path
        fill="#fff"
        d="m24.853 12.647-1.5-1.5a.5.5 0 0 0-.707 0L19.793 14h-1.246a5.51 5.51 0 0 0-5.19 3.683l-2.329 6.652a.5.5 0 0 0 .637.637l6.652-2.328A5.51 5.51 0 0 0 22 17.453v-1.246l2.853-2.854a.5.5 0 0 0 0-.707m-3.707 3A.5.5 0 0 0 21 16v1.453a4.505 4.505 0 0 1-3.014 4.247l-4.583 1.605L18 18.707 17.293 18l-4.597 4.597 1.604-4.584A4.505 4.505 0 0 1 18.547 15H20a.5.5 0 0 0 .353-.146L23 12.207l.793.793z"
      ></path>
    </svg>
  );
};
