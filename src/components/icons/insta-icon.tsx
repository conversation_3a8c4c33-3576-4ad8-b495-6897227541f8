export const InstaIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="29"
      height="29"
      viewBox="0 0 29 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect
        width="28.4444"
        height="28.4444"
        transform="translate(0.428711 0.562012)"
        fill="white"
        fillOpacity="0.01"
        style={{ mixBlendMode: "multiply" }}
      />
      <path
        d="M20.3449 10.3705C21.0518 10.3705 21.6249 9.7974 21.6249 9.09048C21.6249 8.38355 21.0518 7.81048 20.3449 7.81048C19.638 7.81048 19.0649 8.38355 19.0649 9.09048C19.0649 9.7974 19.638 10.3705 20.3449 10.3705Z"
        fill="#161616"
      />
      <path
        d="M14.651 9.30683C13.5677 9.30683 12.5087 9.62808 11.6079 10.23C10.7071 10.8318 10.0051 11.6873 9.59048 12.6882C9.1759 13.6891 9.06743 14.7904 9.27878 15.853C9.49013 16.9155 10.0118 17.8915 10.7779 18.6575C11.5439 19.4236 12.5199 19.9453 13.5824 20.1566C14.645 20.368 15.7463 20.2595 16.7472 19.8449C17.7481 19.4303 18.6035 18.7283 19.2054 17.8275C19.8073 16.9267 20.1286 15.8677 20.1286 14.7843C20.1286 13.3316 19.5515 11.9384 18.5242 10.9112C17.497 9.88392 16.1038 9.30683 14.651 9.30683ZM14.651 18.3399C13.9478 18.3399 13.2604 18.1314 12.6757 17.7407C12.091 17.35 11.6352 16.7947 11.3661 16.145C11.097 15.4953 11.0266 14.7804 11.1638 14.0907C11.301 13.401 11.6396 12.7674 12.1369 12.2702C12.6341 11.7729 13.2677 11.4343 13.9574 11.2971C14.6471 11.1599 15.362 11.2303 16.0117 11.4994C16.6614 11.7685 17.2167 12.2243 17.6074 12.809C17.9981 13.3937 18.2066 14.0811 18.2066 14.7843C18.2066 15.7273 17.832 16.6317 17.1652 17.2985C16.4984 17.9653 15.594 18.3399 14.651 18.3399Z"
        fill="#161616"
      />
      <path
        d="M14.651 6.03963C17.4991 6.03963 17.8366 6.05048 18.9613 6.10185C19.6377 6.10991 20.3077 6.23414 20.942 6.46914C21.402 6.64664 21.8197 6.91845 22.1684 7.2671C22.517 7.61574 22.7888 8.0335 22.9663 8.4935C23.2013 9.12782 23.3256 9.79781 23.3336 10.4742C23.3849 11.5989 23.3958 11.9363 23.3958 14.7844C23.3958 17.6325 23.385 17.9699 23.3336 19.0947C23.3256 19.7711 23.2013 20.441 22.9663 21.0754C22.7888 21.5354 22.517 21.9531 22.1684 22.3018C21.8197 22.6504 21.402 22.9222 20.942 23.0997C20.3077 23.3347 19.6377 23.459 18.9613 23.467C17.8366 23.5183 17.4994 23.5292 14.651 23.5292C11.8027 23.5292 11.4654 23.5184 10.3408 23.467C9.66442 23.459 8.99443 23.3347 8.36011 23.0997C7.90011 22.9222 7.48235 22.6504 7.13371 22.3018C6.78506 21.9531 6.51325 21.5354 6.33575 21.0754C6.10075 20.441 5.97652 19.7711 5.96846 19.0947C5.91718 17.9699 5.90624 17.6325 5.90624 14.7844C5.90624 11.9363 5.91709 11.5989 5.96846 10.4742C5.97652 9.79781 6.10075 9.12782 6.33575 8.4935C6.51324 8.03348 6.78505 7.61571 7.13369 7.26705C7.48234 6.91839 7.9001 6.64656 8.36011 6.46905C8.99443 6.23405 9.66442 6.10982 10.3408 6.10176C11.4655 6.05048 11.803 6.03963 14.651 6.03963ZM14.651 4.11768C11.7542 4.11768 11.3909 4.12994 10.2532 4.18185C9.36842 4.19962 8.49308 4.36727 7.66438 4.67768C6.95569 4.95173 6.31209 5.37083 5.77481 5.90811C5.23753 6.44539 4.81843 7.08899 4.54438 7.79768C4.23392 8.62652 4.06627 9.50202 4.04855 10.3869C3.99664 11.5242 3.98438 11.8875 3.98438 14.7843C3.98438 17.6812 3.99664 18.0445 4.04855 19.1822C4.06628 20.0671 4.23393 20.9426 4.54438 21.7715C4.81843 22.4801 5.23753 23.1237 5.77481 23.661C6.31209 24.1983 6.95569 24.6174 7.66438 24.8915C8.49322 25.2019 9.36871 25.3696 10.2536 25.3873C11.3909 25.4387 11.7542 25.451 14.651 25.451C17.5479 25.451 17.9112 25.4387 19.0489 25.3868C19.9338 25.3691 20.8093 25.2015 21.6382 24.891C22.3468 24.617 22.9904 24.1979 23.5277 23.6606C24.065 23.1233 24.4841 22.4797 24.7582 21.771C25.0686 20.9422 25.2363 20.0667 25.254 19.1818C25.3054 18.0445 25.3177 17.6812 25.3177 14.7843C25.3177 11.8875 25.3054 11.5242 25.2535 10.3865C25.2358 9.50172 25.0681 8.62638 24.7577 7.79768C24.4836 7.08908 24.0645 6.44556 23.5272 5.90836C22.9899 5.37116 22.3463 4.95213 21.6377 4.67812C20.8089 4.36767 19.9334 4.20002 19.0485 4.1823C17.9112 4.12994 17.5479 4.11768 14.651 4.11768Z"
        fill="#161616"
      />
    </svg>
  );
};
