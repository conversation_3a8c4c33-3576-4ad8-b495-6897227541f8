export const EmojiDay6Icon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="22"
      fill="none"
      viewBox="0 0 22 22"
      {...props}
    >
      <ellipse
        cx="10"
        cy="10.435"
        fill={props.color || "#fff"}
        rx="10"
        ry="10.435"
        transform="matrix(-1 0 0 1 21.373 .565)"
      ></ellipse>
      <path
        fill="#000"
        d="M8.56 2.176a5.3 5.3 0 0 1 1.913-.435c.279 0 .566 0 .87-.043.304-.044.565 0 .87 0a4.24 4.24 0 0 1 1.904.339.253.253 0 0 0 .398-.251.26.26 0 0 0-.103-.166A4.35 4.35 0 0 0 12.3.75a6 6 0 0 0-1.008-.043 7 7 0 0 0-.957.113 5.4 5.4 0 0 0-2.078.87.296.296 0 0 0-.096.408.296.296 0 0 0 .4.078M21.24 9.515a.295.295 0 0 0-.331-.26.304.304 0 0 0-.252.33c.052.435.078.87.087 1.295q.013.65-.052 1.296c-.08.81-.27 1.607-.566 2.365-.292.763-.672 1.49-1.13 2.166a8.25 8.25 0 0 1-3.887 2.973c-1.558.613-3.236.86-4.904.722a10.4 10.4 0 0 1-2.687-.6A8 8 0 0 1 5.1 18.463a9.3 9.3 0 0 1-2.73-3.948 11 11 0 0 1-.626-4.852.254.254 0 0 0-.505-.06 11.4 11.4 0 0 0 .487 5.138 10.1 10.1 0 0 0 2.809 4.392 8.7 8.7 0 0 0 2.609 1.574c.955.377 1.96.617 2.982.713a11.7 11.7 0 0 0 5.348-.87 9.04 9.04 0 0 0 4.218-3.4 11.6 11.6 0 0 0 1.147-2.356c.296-.827.477-1.69.54-2.566v-1.365c0-.495-.079-.904-.14-1.348"
      ></path>
      <path
        fill="#000"
        d="M17.188 12.724a4.7 4.7 0 0 0-.87 0c-1.2 0-3.783.156-6.087.287s-4.652.391-4.652.391a.64.64 0 0 0-.26.487c-.015.468.059.934.216 1.374a6 6 0 0 0 1.227 2.209 6.16 6.16 0 0 0 4.53 1.913 6.96 6.96 0 0 0 4.557-1.948 6.13 6.13 0 0 0 1.643-2.86c.145-.475.16-.98.043-1.462a.59.59 0 0 0-.347-.391m-1.07 2.704c-.24.473-.557.903-.939 1.27a6.1 6.1 0 0 1-3.904 1.782 5.37 5.37 0 0 1-4-1.547 4.8 4.8 0 0 1-.992-1.548 4.7 4.7 0 0 1-.356-1.235v-.322a32 32 0 0 1 3.243-.121c2.479-.053 5.644-.061 7.096-.061h.435a4.5 4.5 0 0 1-.583 1.782M3.787 8.011q.725 1.178 1.261 2.452a.77.77 0 0 0 .705.444.87.87 0 0 0 .573-.53 23 23 0 0 1 1.192-2.714c.47-.617 1.956-1.096 2.487-1.47a.75.75 0 0 0 .391-.643.87.87 0 0 0-.426-.556 27 27 0 0 1-2.809-1.261c-.73-.574-1.26-2.218-1.704-2.783a.71.71 0 0 0-.548-.322c-.217 0-.452.261-.6.713a22 22 0 0 1-.722 2.61c-.347.703-1.965 1.4-2.565 1.816a.77.77 0 0 0-.4.687.78.78 0 0 0 .522.496c.6.27 2.113.557 2.643 1.061m-.443-2.844a2.74 2.74 0 0 0 .87-.956 5 5 0 0 0 .226-.696c.182-.73.278-1.591.452-2.095l.017-.148c0 .043.104.078.14.139.269.435.556 1.217.92 1.878.187.377.44.716.749 1 .295.242.62.447.965.609.53.243 1.13.47 1.548.678l.104.061H9.24c-.427.209-1.018.444-1.522.722a3.1 3.1 0 0 0-.957.722 13 13 0 0 0-1.113 2.417c-.31-.77-.71-1.499-1.191-2.174a3.1 3.1 0 0 0-1.052-.583c-.54-.19-1.165-.339-1.626-.47.382-.304 1.043-.695 1.565-1.104M11.779 5.333a.7.7 0 0 0 .382.634c.513.366 1.957.827 2.409 1.427.439.842.82 1.714 1.139 2.608a.87.87 0 0 0 .574.513.77.77 0 0 0 .704-.434q.515-1.236 1.218-2.374c.513-.522 1.982-.774 2.556-1.018.34-.148.487-.348.513-.487a.76.76 0 0 0-.391-.687c-.574-.408-2.148-1.087-2.479-1.739A22 22 0 0 1 17.7 1.28c-.148-.443-.374-.686-.617-.73a.68.68 0 0 0-.548.313c-.435.548-.948 2.14-1.66 2.696a27 27 0 0 1-2.714 1.226.78.78 0 0 0-.383.548m1.139 0c.408-.2.982-.418 1.495-.661.33-.15.643-.336.93-.557.303-.273.556-.597.749-.956.339-.635.626-1.392.87-1.818 0-.06.095-.095.13-.139a.5.5 0 0 0 0 .14c.156.486.26 1.33.434 2.043q.082.343.218.67c.194.37.474.69.817.93.496.4 1.122.774 1.54 1.052-.436.13-1.062.26-1.6.452a3 3 0 0 0-1.027.574c-.462.649-.847 1.35-1.148 2.087a12.4 12.4 0 0 0-.982-2.34 3 3 0 0 0-.93-.703c-.496-.27-1.07-.496-1.479-.696h-.07z"
      ></path>
    </svg>
  );
};
