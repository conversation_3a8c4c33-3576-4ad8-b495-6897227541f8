import { PropsWithChildren } from "react";
import defaultAvatar from "@/assets/images/avatar-default.png";
import { PropsWithClassName } from "@/types/app";
import { cn } from "@/utils/cn";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";

function getFixedIndexByChar(array: readonly string[], char: string): number {
  const charCodeSum = char
    .split("")
    .reduce((sum, c) => sum + c.charCodeAt(0), 0);
  return charCodeSum % array.length;
}

const userAvatarColors = [
  "bg-[#FFEFD2] text-[#66460D]",
  "bg-[#DCF2EA] text-[#317159]",
  "bg-[#F9DADA] text-[#7D2828]",
  "bg-[#D3F5F7] text-[#0F5156]",
] as const;

export const UserAvatar = ({
  username,
  url,
  className,
  children,
  textClassName,
}: {
  username: string;
  textClassName?: string;
  url: string | undefined;
} & PropsWithClassName &
  PropsWithChildren) => {
  if (!url && !username) {
    return (
      <Avatar className={cn("mr-2.5 size-8", className)}>
        <AvatarImage src={defaultAvatar} alt="default avatar" />
      </Avatar>
    );
  }

  return (
    <Avatar className={cn("mr-2.5 size-8", className)}>
      <AvatarImage src={url} alt={username} />
      <AvatarFallback
        className={cn(
          "rounded-lg text-primary-foreground text-xs uppercase",
          userAvatarColors[getFixedIndexByChar(userAvatarColors, username[0])],
          textClassName,
        )}
      >
        {username.slice(0, 2)}
        {children}
      </AvatarFallback>
    </Avatar>
  );
};
