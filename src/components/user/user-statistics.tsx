import { motion } from "framer-motion";
import owlIcon from "@/assets/images/owls/owl-statistics.png";
import * as m from "@/paraglide/messages.js";
import { User } from "@/types/auth";
import { MyLeaderboard } from "@/types/leaderboard";
import { formatNumberUnit } from "@/utils/formatNumberUnit";
import { BookIcon, DiamondIcon, FriendsIcon, LeagueIcon } from "../icons";
import { Card, CardContent } from "../ui/card";

export default function UserStatistics({
  user,
  rank,
}: {
  user?: User;
  rank?: MyLeaderboard;
}) {
  if (!user || !rank) return null;
  const numberBadges = Object.keys(user.badges).length;

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: "auto" }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      <Card className="relative h-full rounded-lg border-(--card-border-secondary) p-0">
        <CardContent className="relative p-4 py-5">
          <div className="py-7 text-center font-normal text-3xl">
            {m["profile.statistics.title"]()}
          </div>
          <div className="absolute top-5 right-5">
            <img className="h-16" alt="owl-icon" src={owlIcon} />
          </div>
          <div className="grid auto-rows-fr grid-cols-1 gap-3 text-secondary md:gap-4 lg:grid-cols-2 lg:gap-y-10">
            <div className="rounded-lg bg-(--statistics-xp-bg)/30 ps-3 pt-3 shadow-[0px_2px_8px_0px_#6C6C6C40]">
              <div className="h-8">
                <DiamondIcon color="black" />
              </div>
              <div className="mt-5 font-semibold text-base leading-6">
                {m["profile.statistics.total_xp"]()}
              </div>
              <div className="font-light text-4xl">
                {formatNumberUnit(user.points)}
              </div>
            </div>
            <div className="rounded-lg bg-(--statistics-courses-bg)/30 ps-3 pt-3 shadow-[0px_2px_8px_0px_#6C6C6C40]">
              <div className="h-8">
                <BookIcon color="black" />
              </div>
              <div className="mt-5 font-semibold text-base leading-6">
                {m["profile.statistics.completed_courses"]()}
              </div>
              <div className="font-light text-4xl">
                {formatNumberUnit(user.stats?.completed_courses ?? 0)}
              </div>
            </div>
            <div className="rounded-lg bg-(--statistics-friends-bg)/30 ps-3 pt-3 shadow-[0px_2px_8px_0px_#6C6C6C40]">
              <div className="h-8">
                <FriendsIcon color="black" />
              </div>
              <div className="mt-5 font-semibold text-base leading-6">
                {m["profile.statistics.friends_invited"]()}
              </div>
              <div className="font-light text-4xl">
                {formatNumberUnit(user.ref_count)}
              </div>
            </div>
            <div className="rounded-lg bg-(--statistics-league-bg)/30 ps-3 pt-3 pb-10 shadow-[0px_2px_8px_0px_#6C6C6C40]">
              <div className="h-8">
                <LeagueIcon color="black" />
              </div>
              <div className="mt-5 font-semibold text-base capitalize leading-6">
                {m["profile.statistics.badges"]()}
              </div>
              <div className="font-light text-4xl">
                {formatNumberUnit(numberBadges)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
