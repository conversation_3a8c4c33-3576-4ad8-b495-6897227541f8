import { useMutation, useQueryClient } from "@tanstack/react-query";
import { type } from "arktype";
import { toast } from "sonner";
import { updateProfile } from "@/services/auth";
import { updateChatPreferences } from "@/services/conversations";
import { uploadFile } from "@/services/file";
import { User } from "@/types/auth";
import { AppError } from "@/utils/api";
import { formatBytes } from "@/utils/formaters";
import { useAppForm } from "../form";

export const userFormSchema = type({
  name: "string>0",
  username: type(/^[a-zA-Z][a-zA-Z0-9_]{2,20}[a-zA-Z0-9]$/).describe(
    "be 2-20 characters long and contain only letters, numbers, or underscores",
  ),
  bio: "string",
  avatar_url: "string?",
  avatar: type("File | undefined")
    .narrow((data, ctx) => {
      if (!data) {
        return true;
      }

      if (
        !["image/jpeg", "image/png", "image/webp", "image/gif"].includes(
          data.type,
        )
      ) {
        return ctx.reject({
          expected: "must be an image",
          actual: data.type,
        });
      }

      if (data.size > 1 * 1024 * 1024) {
        ctx.reject({
          expected: "smaller than 1MB",
          actual: formatBytes(data.size),
        });
      }

      return true;
    })
    .optional(),
});

export function useProfileForm({ profile }: { profile: User | undefined }) {
  const defaultValues: typeof userFormSchema.infer = {
    name: profile?.name ?? "",
    username: profile?.username ?? "",
    bio: profile?.bio ?? "",
  };

  const queryClient = useQueryClient();
  const queryKey = ["profile", undefined, profile?.id, undefined];

  const form = useAppForm({
    defaultValues,
    validators: {
      onSubmit: userFormSchema,
    },
    onSubmit: async ({ value }) => {
      if (value.avatar instanceof File) {
        return uploadFile({ file: value.avatar }).then((v) => {
          return mutateAsync({
            ...value,
            avatar_url: v.square || v.file_name,
          }).then(() => {
            form.getFieldInfo("avatar").instance?.setValue(undefined);
          });
        });
      }
      return mutateAsync(value);
    },
  });

  const { mutateAsync } = useMutation({
    mutationFn: updateProfile,
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey });
      queryClient.invalidateQueries({
        queryKey: ["profile", undefined, profile?.id],
      });
    },
    onSuccess: (data) => {
      form.reset(JSON.parse(JSON.stringify(form.state.values)));
      toast.success("Successfully saved");
      updateChatPreferences({ display_name: data.name });
    },
    onError: (
      error: AppError,
      _,
      context: { prev: User | undefined } | undefined,
    ) => {
      if (error?.code === "Exist") {
        toast.error("Username already taken");
        return;
      }

      queryClient.setQueryData(queryKey, context?.prev);
      toast.error("Failed to update profile");
    },
    async onMutate(variables) {
      await queryClient.cancelQueries({
        queryKey,
      });

      const prev: User | undefined = queryClient.getQueryData(queryKey);
      if (!prev) {
        return { prev };
      }

      queryClient.setQueryData(queryKey, {
        ...prev,
        name: variables.name,
        username: variables.username,
        avatar_url: variables.avatar_url,
        bio: variables.bio,
      });

      return { prev };
    },
  });

  return form;
}
