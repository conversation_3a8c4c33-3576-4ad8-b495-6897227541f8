import { useMemo } from "react";
import alpaCrew from "@/assets/images/badges/alpha-crew.png";
import firstMileStone from "@/assets/images/badges/first-milestone.png";
import theInviter5 from "@/assets/images/badges/the-inviter-5.png";
import explorerActiveImg from "@/assets/images/league/explorer-active.png";
import learnerActiveImg from "@/assets/images/league/learner-active.png";
import strategistActiveImg from "@/assets/images/league/strategist-active.png";
import thinkerActiveImg from "@/assets/images/league/thinker-active.png";
import lockIcon from "@/assets/images/lock.svg";
import * as m from "@/paraglide/messages.js";
import { BadgeEnum, BadgesInfo } from "@/types/app";
import { User } from "@/types/auth";
import { useBadgeList } from "@/utils/badges";
import { formatDate } from "@/utils/formatDate";

// Badge list will be created inside the component to use translations

const disabledRecordsImg: string[] = [
  explorerActiveImg,
  learnerActiveImg,
  strategistActiveImg,
  thinkerActiveImg,
] as const;

export const UserAchievement = ({ user }: { user: User }) => {
  const BadgeList = useBadgeList();

  const activeBadges = useMemo(() => {
    return Object.entries(user.badges).map(([key, value]) => ({
      badge_id: key,
      created_at: value,
      image: BadgeList.find((badge) => badge.id === key)?.img || "",
    }));
  }, [user, BadgeList]);

  const inactiveBadges = useMemo(() => {
    if (!user?.badges) return BadgeList;
    return BadgeList.filter((e) => !(e.id in user.badges));
  }, [user, BadgeList]);

  if (!user) return null;

  return (
    <div className="h-full py-6">
      <div className="mb-4 text-xl leading-7">
        {m["profile.achievement_details.personal_records"]()}
      </div>
      <div>
        <div className="mb-7 w-fit rounded-md bg-[#FFF7E0] p-1 text-xs italic">
          {m["profile.achievement_details.no_records_yet"]()}
        </div>
        <div className="mb-10 grid grid-cols-2 gap-4 md:grid-cols-4 md:gap-14">
          {disabledRecordsImg.map((img) => (
            <div
              key={img}
              className="relative flex items-center justify-center rounded-lg border-(--muted) border-3 border-b-5 py-7 shadow-[0px_4.67px_0px_0px_#E6E8F0]"
            >
              <div className="absolute top-1 right-1 flex h-8 w-8 items-center justify-center rounded-full bg-(--muted)">
                <img alt="key-icon" src={lockIcon} />
              </div>
              <img
                src={img}
                alt="league-placeholder"
                className="pointer-events-none max-w-[70px] opacity-40 grayscale filter"
              />
            </div>
          ))}
        </div>
      </div>
      <div className="mb-7 text-xl leading-7">
        {m["profile.achievement_details.awards"]()}
      </div>
      {activeBadges.length === 0 && (
        <div>
          <div className="mb-7 w-fit rounded-md bg-[#FFF7E0] p-1 text-xs italic">
            {m["profile.achievement_details.no_badges_yet"]()}
          </div>
        </div>
      )}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 lg:grid-cols-3">
        {activeBadges.map((badge) => (
          <div
            key={badge.badge_id}
            className="flex items-center justify-center rounded-lg border-(--muted) border-3 border-b-5 py-7 shadow-[0px_4.67px_0px_0px_#E6E8F0]"
          >
            <div className="text-center">
              <img
                className="transition-transform hover:scale-120"
                src={badge.image}
                alt={`${badge.badge_id}-image`}
                width={150}
              />
              <div className="mt-5 text-sm">{formatDate(badge.created_at)}</div>
            </div>
          </div>
        ))}
        {inactiveBadges.map((badge) => (
          <div
            key={badge.id}
            className="relative flex items-center justify-center rounded-lg border-(--muted) border-3 border-b-5 py-7 shadow-[0px_4.67px_0px_0px_#E6E8F0]"
          >
            <div className="absolute top-1 right-1 flex h-8 w-8 items-center justify-center rounded-full bg-(--muted)">
              <img alt="key-icon" src={lockIcon} />
            </div>
            <img
              className="opacity-40 grayscale filter"
              src={badge.img}
              alt={`${badge.id}-image`}
              width={150}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
