import { useNavigate } from "@tanstack/react-router";
import { type } from "arktype";
import { XIcon } from "lucide-react";
import { useState } from "react";
import { User } from "@/types/auth";
import { formatDate } from "@/utils/formatDate";
import { ClockIcon, EditProfileIcon } from "../icons";
import { EditAvatarIcon } from "../icons/edit-avatar-icon";
import { Button } from "../ui/button";
import { Card, CardContent } from "../ui/card";
import { UserAvatar } from "../user-avatar";
import { useProfileForm, userFormSchema } from "./useProfileForm";

interface UserInfoProps {
  user: User | undefined;
  form?: ReturnType<typeof useProfileForm>;
}

export const UserInfo: React.FC<UserInfoProps> = ({ user, form }) => {
  const navigate = useNavigate();
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const handleAvatarFileChange = (file: File) => {
    if (previewUrl) URL.revokeObjectURL(previewUrl);
    setPreviewUrl(URL.createObjectURL(file));
  };

  if (!user) return null;

  return (
    <div>
      <Card className="relative h-full rounded-lg border-(--card-border-secondary) p-0">
        <CardContent className="p-5">
          <div className="my-3 flex w-full flex-col items-center justify-center">
            <div className="relative size-24">
              {form ? (
                <form.AppField
                  name="avatar"
                  children={(field) => {
                    return (
                      <>
                        <UserAvatar
                          url={previewUrl || user.avatar_url}
                          textClassName={"text-xl"}
                          username={user.name}
                          className="h-full w-full"
                        />
                        <div className="absolute top-0 flex size-full items-center justify-center rounded-full bg-secondary/50 hover:bg-secondary/70">
                          <field.FileField
                            className="absolute inset-0 z-10 h-full w-full cursor-pointer rounded-full opacity-0"
                            onChange={(e) => {
                              if (e.target.files?.[0]) {
                                handleAvatarFileChange(e.target.files[0]);
                              }
                            }}
                          />
                          <EditAvatarIcon className="size-7 rounded bg-background/30" />
                          {previewUrl && (
                            <div
                              className="absolute right-0 bottom-0 z-20 cursor-pointer rounded-full bg-accent hover:text-destructive"
                              onClick={() => {
                                setPreviewUrl(null);
                                URL.revokeObjectURL(previewUrl);
                              }}
                            >
                              <XIcon className="size-6" />
                            </div>
                          )}
                        </div>
                      </>
                    );
                  }}
                  validators={{
                    onChange: (ctx) => {
                      const out = userFormSchema.pick("avatar")({
                        avatar: ctx.value,
                      });

                      if (out instanceof type.errors) {
                        return out;
                      }

                      return undefined;
                    },
                  }}
                ></form.AppField>
              ) : (
                <UserAvatar
                  url={user.avatar_url}
                  textClassName={"text-xl"}
                  username={user.name}
                  className="h-full w-full"
                />
              )}
            </div>

            {form && (
              <form.AppField
                name="avatar"
                children={(field) => {
                  return (
                    <field.FieldMessage className="first-letter:capitalize" />
                  );
                }}
              />
            )}
          </div>
          <div className="w-full text-center">
            <div className="text-3xl">{user.name}</div>
            <div className="mt-2 flex items-center justify-center">
              <ClockIcon className="me-1" />
              <span className="text-(--learning-input-btn-bg) text-sm leading-5 tracking-[0.01em]">
                Joined {formatDate(user.created_at)}
              </span>
            </div>

            <Button
              variant="secondary"
              size={"lg"}
              disabled={!!form}
              onClick={() => navigate({ to: "/profile/setting" })}
              className="mt-3 w-full rounded-full bg-(--learning-input-btn-bg) hover:bg-(--learning-input-btn-bg)/90"
            >
              <span className="text-lg leading-6 tracking-normal">
                Edit Profile
              </span>
              <EditProfileIcon className="ms-2" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
