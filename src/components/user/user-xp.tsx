import type * as React from "react";
import { NavUser } from "@/components/nav-user";
import { useProfile } from "@/services/auth";
import { formatNumberUnit } from "@/utils/formatNumberUnit";

export const UserXp = () => {
  const { data: user } = useProfile();
  if (!user) {
    return null;
  }
  return (
    <div className="flex items-center justify-center gap-1">
      <div className="flex items-center justify-center gap-0.5 rounded-full bg-(--layout-bg) px-2 py-1 md:gap-1 md:px-5">
        <span className="font-normal text-xs sm:text-sm md:leading-6 lg:text-lg">
          {formatNumberUnit(user?.points || 0)}{" "}
        </span>
        XP
      </div>
      <NavUser user={user} avatarClassName="lg:!size-8" />
    </div>
  );
};
