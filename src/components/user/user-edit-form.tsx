import { useNavigate } from "@tanstack/react-router";
import { motion } from "framer-motion";
import { CheckIcon } from "@/components/icons/check-icon";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { GradientText } from "@/components/ui/gradient-text";
import { Input } from "@/components/ui/input";
import { User } from "@/types/auth";
import { useProfileForm } from "./useProfileForm";

export function UserEditForm({
  form,
  currentProfile,
}: {
  form: ReturnType<typeof useProfileForm>;
  currentProfile?: User;
}) {
  const navigate = useNavigate();

  if (!currentProfile) return null;

  const cancelSetting = () => {
    navigate({ to: "/profile/me" });
  };

  return (
    <motion.div layout className="flex-1 overflow-hidden">
      <div className="h-full">
        <Card className="h-full rounded-lg border-(--card-border-secondary) p-0">
          <CardContent className="py-3 md:px-10">
            <div className="md:pt-7">
              <div>
                <GradientText className="font-light text-2xl leading-10 md:text-4xl">
                  Profile settings
                </GradientText>
              </div>
              <div className="xl:max-w-[70%]">
                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    form.handleSubmit();
                  }}
                >
                  <form.AppField
                    name="name"
                    children={(field) => (
                      <>
                        <h3 className="mt-8 mb-2 text-xl leading-7">
                          Display Name
                        </h3>
                        <field.TextField
                          className="h-14 rounded-lg p-3 leading-6 md:text-base"
                          placeholder="Enter your display name"
                        />
                        <field.FieldMessage className="first-letter:capitalize" />
                      </>
                    )}
                  />

                  <form.AppField
                    name="username"
                    children={(field) => (
                      <>
                        <h3 className="mt-6 mb-2 text-base leading-7 md:text-xl">
                          Username
                        </h3>
                        <div className="relative">
                          <div className="absolute top-4 left-2.5">@</div>
                          <field.TextField
                            className="h-14 rounded-lg p-3 ps-7 leading-6 md:text-base"
                            placeholder="Enter your username"
                          />
                          <div className="mt-1 text-xs italic leading-5">
                            Your profile will be available on aicademy.io/
                            {field.state.value}
                          </div>
                          <field.FieldMessage className="first-letter:capitalize" />
                        </div>
                      </>
                    )}
                  />

                  <form.AppField
                    name="bio"
                    children={(field) => (
                      <>
                        <h3 className="mt-6 mb-2 text-base leading-7 md:text-xl">
                          Biography
                        </h3>
                        <field.TextareaField
                          className="min-h-24 w-full rounded-lg border-1 p-3 text-sm leading-6 md:text-base"
                          placeholder="ex: Educator with 5+ years of experience in tech and online learning."
                          rows={3}
                        />
                        <div className="mt-1 text-xs italic leading-5">
                          Links and coupon codes are not permitted in this
                          section.
                        </div>
                      </>
                    )}
                  />

                  <h3 className="mt-6 mb-5 text-base leading-7 md:text-xl">
                    Email address
                  </h3>
                  <div className="relative">
                    {!!currentProfile.email_verified_at && (
                      <div className="absolute top-3.5 right-3 flex items-center rounded-md bg-(--lesson-btn-revise) px-2.5 py-1.5 text-(--border-correct) [font-size:var(--size-2xs)]">
                        Verified <CheckIcon color="var(--border-correct)" />
                      </div>
                    )}
                    <Input
                      className="h-14 rounded-lg p-3 pe-20 text-base leading-6"
                      defaultValue={currentProfile.email}
                      disabled
                    />
                  </div>

                  <form.AppForm>
                    <div className="mt-8 flex">
                      <Button
                        variant="outline"
                        size={"lg"}
                        onClick={cancelSetting}
                        className="me-6 rounded-full bg-card px-10 font-normal md:px-15"
                      >
                        Cancel
                      </Button>
                      <form.SubmitButton
                        className="rounded-full bg-primary px-10 font-normal hover:bg-primary/90 md:px-20"
                        size="lg"
                        variant="secondary"
                        onlyDirty
                      >
                        Save setting
                      </form.SubmitButton>
                    </div>
                  </form.AppForm>
                </form>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </motion.div>
  );
}
