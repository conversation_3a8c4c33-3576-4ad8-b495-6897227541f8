import { Link } from "@tanstack/react-router";
import React from "react";
import { StarRating } from "@/components/star-rating";
import { UserVoting } from "@/types/rating";
import { formatDate } from "@/utils/formatDate";
import ExtendableText from "../ui/extendable-text";
import { CourseCategoryTag } from "../ui/hashtag";

export const UserReview = ({
  votings = [],
}: {
  votings: UserVoting[] | undefined;
}) => {
  return (
    <div className="h-full py-7 md:px-9">
      <div className="mb-7 text-xl leading-7">Past reviews you've written</div>
      {votings.map((voting) => (
        <div
          key={voting.course_id}
          className="flex border-(muted) border-b-1 py-4 last:border-none"
        >
          <div className="hidden h-fit w-fit min-w-24 items-center justify-center rounded-xl border-(--card-border-secondary) border-[0.57px] bg-(--course-chat-bg) md:block">
            <img src={voting.course?.image_url} alt="course-img" width={96} />
          </div>
          <div className="md:ps-7">
            <div className="flex gap-2">
              <div className="me-2 mt-2 flex h-fit w-16 items-center justify-center rounded-lg border-(--card-border-secondary) border-[0.57px] bg-(--course-chat-bg) md:hidden">
                <img src={voting.course?.image_url} alt="course-img" />
              </div>
              <div className="ms-2 md:ms-0">
                <div className="flex">
                  {voting.course?.categories?.map((e) =>
                    e.tags?.map((tag) => (
                      <CourseCategoryTag key={tag} tag={tag} />
                    )),
                  )}
                </div>
                <Link
                  to="/courses/$courseSlug"
                  params={{ courseSlug: voting.course?.slug || "" }}
                >
                  <h4 className="mt-2 text-base">{voting.course?.name}</h4>
                </Link>
                <div className="items-center md:flex">
                  <StarRating
                    size={16}
                    initialValue={voting.general}
                    readOnly
                  />
                  <p className="mt-2 text-xs leading-5 md:ms-5 md:mt-0">
                    {formatDate(voting.created_at)}
                  </p>
                </div>
              </div>
            </div>
            <div className="my-3 h-auto text-sm leading-5">
              <ExtendableText value={voting?.comment} />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
