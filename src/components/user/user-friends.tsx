import { Link } from "@tanstack/react-router";
import { XIcon } from "lucide-react";
import handdrawnArrow from "@/assets/images/handdrawn-arrow.svg";
import handdrawnPlane from "@/assets/images/handdrawn-plane.svg";
import handdrawnUnlock from "@/assets/images/handdrawn-unlock.svg";
import inviteFriendImg from "@/assets/images/invite-friends.png";
import keyIcon from "@/assets/images/key.svg";
import smallKey from "@/assets/images/small-key.svg";
import { FriendsIcon } from "@/components/icons";
import * as m from "@/paraglide/messages.js";
import { useMyFriends } from "@/services/users";
import { User } from "@/types/auth";
import { BASE_URL } from "@/utils";
import { formatDate } from "@/utils/formatDate";
import {
  createShareUrl,
  SHARE_PLATFORMS,
  SharePlatformName,
} from "@/utils/shareUrl";
import { ButtonCopy } from "../button-copy";
import { CheckIcon } from "../icons";
import { Card, CardContent } from "../ui/card";

export default function UserFriends({ profile }: { profile?: User }) {
  const { data: friends = [] } = useMyFriends();

  if (!profile) return null;

  const { username = "", ref_code = "", keys = 0, ref_count = 0 } = profile;
  const refLink = `${BASE_URL}?invite=${username ? username : ref_code}`;

  return (
    <div className="h-full">
      <Card className="h-full rounded-lg border-none bg-(--card-tag-bg) p-0 shadow-none">
        <CardContent className="flex w-full pt-8 pb-5 md:px-12">
          <img
            className="hidden max-h-50 lg:block"
            alt="invite-friend-img"
            src={inviteFriendImg}
          />
          <div className="w-full lg:w-[70%] lg:ps-16">
            <div className="text-3xl leading-9">
              {m["profile.friends.invite_title"]()} <br />
              {m["profile.friends.unlock_lessons"]()}
            </div>
            <div className="mt-8 flex w-full items-center justify-between rounded-lg border-(--color-grey-2) border-1 bg-card px-6 py-4 leading-6 md:text-lg">
              <div className="truncate">{refLink}</div>
              <ButtonCopy content={refLink} />
            </div>
            <div className="ms-1 mt-5 flex items-center">
              <div className="w-[20%] text-base leading-6">
                {m["profile.friends.share_to"]()}
              </div>
              <div className="flex w-[80%] justify-center">
                <div className="flex items-center gap-3">
                  {SHARE_PLATFORMS.filter(
                    ({ name }) => name !== SharePlatformName.Link,
                  ).map(({ Icon, name }) => (
                    <div
                      key={name}
                      className="h-11 w-11 rounded-full bg-card transition-all duration-150 hover:shadow-sm md:p-2"
                    >
                      <Link
                        className="m-auto"
                        to={createShareUrl(name, refLink)}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Icon />
                      </Link>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="my-6 md:flex">
        <div className="w-full text-base leading-6 md:w-1/2">
          <p className="max-w-75">
            {m["profile.friends.bring_friend_description"]()}{" "}
            <b>{m["profile.friends.key_to_unlock"]()}</b>
          </p>
        </div>
        <div className="mt-4 flex w-full md:mt-0 md:w-1/2">
          <div className="flex w-1/2 items-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-(--statistics-friends-bg)/30">
              <img src={keyIcon} width={32} height={32} alt="key-img" />
            </div>
            <div className="ms-6">
              <p className="text-3xl">{keys}</p>
              <p className="text-(--learning-input-btn-bg) text-sm leading-5">
                {m["profile.friends.keys_available"]()}
              </p>
            </div>
          </div>
          <div className="flex w-1/2 items-center justify-end">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-(--statistics-friends-bg)/30">
              <FriendsIcon color="black" />
            </div>
            <div className="ms-6">
              <p className="text-3xl">{ref_count}</p>
              <p className="text-(--learning-input-btn-bg) text-sm leading-5">
                {m["profile.friends.friends_invited"]()}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-4">
        <div className="flex items-end">
          <h5 className="text-xl leading-7">
            {m["profile.friends.invite_list"]()}
          </h5>
          <h6 className="ms-3 text-xs italic leading-5.5">
            {m["profile.friends.top_30_shown"]()}
          </h6>
        </div>
        <div>
          {friends && friends.length > 0 ? (
            <div className="my-4 overflow-hidden rounded-lg border-(--card-border-secondary) border-1">
              <table className="w-full text-left">
                <thead>
                  <tr className="border-gray-200 border-b bg-(--course-chat-bg) text-xs">
                    <th className="ps-6 pt-4 pb-2 font-normal">
                      {m["profile.friends.friends_email"]()}
                    </th>
                    <th className="hidden pt-4 pb-2 font-normal md:table-cell">
                      {m["profile.friends.signup_status"]()}
                    </th>
                    <th className="hidden pt-4 pb-2 font-normal md:table-cell">
                      {m["profile.friends.lesson_completed"]()}
                    </th>
                    <th className="pt-4 pb-2 font-normal">
                      {m["profile.friends.key_earned_date"]()}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {friends.map((friend) => (
                    <tr
                      className="border-gray-200 border-b bg-none text-sm last:border-b-0"
                      key={friend.id}
                    >
                      <td className="py-3 ps-6">{friend.email}</td>
                      <td className="hidden py-3 md:table-cell">
                        <CheckIcon color="#28CC57" />
                      </td>
                      <td className="hidden py-3 md:table-cell">
                        {!friend.learned_at ? (
                          <XIcon color="#F4364B" width={16} />
                        ) : (
                          <CheckIcon color="#28CC57" />
                        )}
                      </td>
                      <td className="py-3">
                        {!friend.learned_at ? (
                          "-"
                        ) : (
                          <div className="flex">
                            <img
                              src={smallKey}
                              alt="small-key-img"
                              width={14}
                            />
                            <p className="ps-2">
                              {formatDate(friend.learned_at)}
                            </p>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="mt-14 mb-2 text-center">
              <p className="mb-13 text-xl leading-7">
                {m["profile.friends.no_friends_yet"]()}
              </p>
              <div className="grid grid-cols-1 gap-10 md:grid-cols-3">
                <div className="relative justify-items-center text-center">
                  <div className="flex w-full justify-center">
                    <div className="flex h-12 w-12 rounded-lg bg-(--muted)">
                      <img
                        className="m-auto"
                        src={handdrawnPlane}
                        alt="key-img"
                      />
                    </div>
                  </div>
                  <p className="mt-2 text-xl leading-7">
                    {m["profile.friends.invite_friends"]()}
                  </p>
                  <p className="mt-2 text-md leading-7">
                    {m["profile.friends.share_unique_link"]()}
                  </p>
                  <img
                    className="md:-right-8 mt-8 rotate-90 md:absolute md:top-1/2 md:mt-0 md:rotate-none"
                    src={handdrawnArrow}
                    alt="arrow-img"
                  />
                </div>
                <div className="relative justify-items-center text-center">
                  <div className="flex w-full justify-center">
                    <div className="flex h-12 w-12 rounded-lg bg-(--muted)">
                      <img className="m-auto" src={keyIcon} alt="plane-img" />
                    </div>
                  </div>
                  <p className="mt-2 text-xl leading-7">
                    {m["profile.friends.earn_keys"]()}
                  </p>
                  <p className="mt-2 text-md leading-7">
                    {m["profile.friends.get_key_when_friend_completes"]()}
                  </p>
                  <img
                    className="md:-right-8 mt-8 rotate-90 md:absolute md:top-1/2 md:mt-0 md:rotate-none"
                    src={handdrawnArrow}
                    alt="arrow-img"
                  />
                </div>
                <div className="relative text-center">
                  <div className="flex w-full justify-center">
                    <div className="flex h-12 w-12 rounded-lg bg-(--muted)">
                      <img
                        className="m-auto"
                        src={handdrawnUnlock}
                        alt="key-img"
                      />
                    </div>
                  </div>
                  <p className="mt-2 text-xl leading-7">
                    {m["profile.friends.unlock_lesson"]()}
                  </p>
                  <p className="mt-2 text-md leading-7">
                    {m["profile.friends.use_keys_access"]()}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
