import { useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { useAtom } from "jotai";
import { useAtomValue } from "jotai/index";
import { useCallback, useMemo } from "react";
import { KeyShareDialog } from "@/components/key-share-dialog";
import { KeyUnlockDialog } from "@/components/key-unlock-dialog";
import { useProfile } from "@/services/auth";
import { courseProgressesQueryOptions } from "@/services/courses";
import { sectionsQueryOptions, startLesson } from "@/services/lessons";
import { atomAuth } from "@/store/auth";
import { atomLessonUnlock } from "@/store/lesson";

const LessonUnlock = () => {
  const { data: user } = useProfile();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [lessonUnlock, setLessonUnlock] = useAtom(atomLessonUnlock);
  const auth = useAtomValue(atomAuth);

  const hasEnoughKeys = useMemo(
    () => (user?.keys ?? 0) >= (lessonUnlock?.requirements?.keys ?? 0),
    [user, lessonUnlock],
  );

  const navigateToLesson = useCallback(async () => {
    if (!lessonUnlock) return;
    await startLesson(lessonUnlock.id);
    queryClient.prefetchQuery(
      courseProgressesQueryOptions(lessonUnlock.course_id, auth?.jwt),
    );
    queryClient.prefetchQuery(sectionsQueryOptions(lessonUnlock.id));
    navigate({ to: `/learn/$courseSlug/${lessonUnlock.slug}` });
    setLessonUnlock(null);
  }, [lessonUnlock, navigate, queryClient, setLessonUnlock, auth]);

  if (!lessonUnlock || !user) {
    return null;
  }

  return (
    <div className="absolute">
      <KeyUnlockDialog
        user={user}
        open={hasEnoughKeys}
        onOpenChange={() => setLessonUnlock(null)}
        lesson={lessonUnlock}
        onUnlock={navigateToLesson}
      />
      <KeyShareDialog
        user={user}
        open={!hasEnoughKeys}
        onOpenChange={() => setLessonUnlock(null)}
        lesson={lessonUnlock}
      />
    </div>
  );
};

export default LessonUnlock;
