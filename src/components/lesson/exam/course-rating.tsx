import { useMutation } from "@tanstack/react-query";
import { Link, useNavigate } from "@tanstack/react-router";
import { motion } from "framer-motion";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import LessonCompletedImg from "@/assets/images/lesson-completed.png";
import owlRating1 from "@/assets/images/owls/owl-rating-1.png";
import owlRating2 from "@/assets/images/owls/owl-rating-2.png";
import owlRating3 from "@/assets/images/owls/owl-rating-3.png";
import owlRating4 from "@/assets/images/owls/owl-rating-4.png";
import owlRating5 from "@/assets/images/owls/owl-rating-5.png";
import {
  ArrowLineLeftIcon,
  CheckIcon,
  ShareIcon,
  UserSkipIcon,
} from "@/components/icons";
import { StarRating } from "@/components/star-rating";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { GradientText } from "@/components/ui/gradient-text";
import { Textarea } from "@/components/ui/textarea";
import * as m from "@/paraglide/messages.js";
import { ELearningStatus } from "@/routes/__layout/my-learning";
import { Route } from "@/routes/learn/$courseSlug/$lessonSlug/__layout-learning";
import { useProfile } from "@/services/auth";
import { voteCourse } from "@/services/courses";
import { RatingValues } from "@/types/rating";
import { cn } from "@/utils/cn";

type RatingCategory =
  | "general"
  | "course_quality"
  | "learning_engagement"
  | "ai_mentor";

interface RatingItemProps {
  category: RatingCategory;
  label: string;
  value: number;
  onChange: (value: number) => void;
}

const getRatingText = (rating: number): string => {
  if (rating === 0) return "";
  if (rating <= 1) return m["course_rating.frustrating"]();
  if (rating <= 2) return m["course_rating.could_be_better"]();
  if (rating <= 3) return m["course_rating.okay"]();
  if (rating <= 4) return m["course_rating.pretty_good"]();
  return m["course_rating.excellent"]();
};

// Function to get the appropriate owl image based on rating
const getOwlImage = (rating: number) => {
  if (rating === 0) return null;
  if (rating === 1) return owlRating1;
  if (rating === 2) return owlRating2;
  if (rating === 3) return owlRating3;
  if (rating === 4) return owlRating4;
  return owlRating5;
};

const getRatingDescription = (
  category: RatingCategory,
  rating: number,
): string => {
  switch (category) {
    case "course_quality":
      if (rating === 0) return "";
      if (rating <= 1)
        return m["course_rating.quality.needs_major_improvements"]();
      if (rating <= 2) return m["course_rating.quality.several_areas_better"]();
      if (rating <= 3) return m["course_rating.quality.good_content_general"]();
      if (rating <= 4)
        return m["course_rating.quality.solid_well_structured"]();
      return m["course_rating.quality.clear_useful_high_quality"]();

    case "learning_engagement":
      if (rating === 0) return "";
      if (rating <= 1) return m["course_rating.engagement.not_engaging"]();
      if (rating <= 2)
        return m["course_rating.engagement.somewhat_monotonous"]();
      if (rating <= 3)
        return m["course_rating.engagement.reasonably_engaging"]();
      if (rating <= 4) return m["course_rating.engagement.fun_interactive"]();
      return m["course_rating.engagement.super_engaging"]();

    case "ai_mentor":
      if (rating === 0) return "";
      if (rating <= 1) return m["course_rating.ai_mentor.not_helpful"]();
      if (rating <= 2)
        return m["course_rating.ai_mentor.occasionally_helpful"]();
      if (rating <= 3) return m["course_rating.ai_mentor.mostly_reliable"]();
      if (rating <= 4) return m["course_rating.ai_mentor.mostly_helpful"]();
      return m["course_rating.ai_mentor.super_helpful"]();
  }
  return "";
};

const CHARACTERS_MIN = 20;
const CHARACTERS_MAX = 1000;

interface CourseRatingProps {
  courseId: string;
  points?: number;
  inDialog?: boolean;
  initRating?: number;
  onRateSuccess?: () => void;
}

export const CourseRating = ({
  courseId,
  points,
  inDialog,
  initRating,
  onRateSuccess,
}: CourseRatingProps) => {
  const { data: profile } = useProfile();
  const navigate = useNavigate();
  const [ratings, setRatings] = useState<RatingValues>({
    general: initRating || 0,
    course_quality: 0,
    learning_engagement: 0,
    ai_mentor: 0,
  });
  const [comment, setComment] = useState("");
  const { mutateAsync, isPending, isSuccess } = useMutation({
    mutationFn: async () => {
      return voteCourse(courseId, { ...ratings, comment });
    },
    onSuccess: () => {
      toast.success(m["course_rating.rating_submitted_success"]());
      if (!inDialog) {
        return;
      }
      navigate({
        to: `/my-learning`,
        search: { status: ELearningStatus.COMPLETED },
      });
      onRateSuccess?.();
    },
    onError: () => {
      toast.error(m["course_rating.rating_submission_failed"]());
    },
  });

  const handleRatingChange = useCallback(
    (category: RatingCategory, value: number) => {
      setRatings((prev) => ({
        ...prev,
        [category]: value,
      }));
    },
    [],
  );

  const isFormComplete = Object.values(ratings).every((rating) => rating > 0);

  if (isSuccess) {
    return <RatingCompleted points={20} />;
  }

  return (
    <div
      className={cn(
        "h-full w-full overflow-y-auto overflow-x-hidden px-3 pb-3",
        !inDialog &&
          "bg-[linear-gradient(180deg,#FFFFFF_48.04%,#FFF4E266_100%)]",
      )}
    >
      <motion.div
        variants={{
          hidden: { opacity: 0, y: 10 },
          visible: { opacity: 1, y: 0 },
        }}
        initial="hidden"
        animate="visible"
        transition={{ duration: 0.2, ease: "easeOut" }}
        className="mx-auto mt-4 flex max-w-4xl flex-col items-center gap-4 text-center md:mt-6 md:gap-8"
      >
        {!inDialog && (
          <div className="flex flex-col items-center gap-4 md:gap-5">
            <img
              src={LessonCompletedImg}
              alt="lesson completed"
              className="w-20 object-cover md:w-24"
            />

            <div className="flex flex-col items-center justify-center gap-3">
              <GradientText className="font-light text-2xl md:text-4xl">
                {m["course_rating.well_done"]({
                  name: profile?.name || m["course_rating.student"](),
                })}
              </GradientText>
              <p className="rounded-lg bg-(--lesson-btn-revise) px-9 py-2 font-light text-3xl md:text-5xl">
                +{points} XP
              </p>
              <p className="font-light md:text-lg">
                {m["course_rating.total_xp_earned"]()}
              </p>
            </div>
          </div>
        )}
        <Card
          className={cn(
            "w-full py-0 shadow-none lg:min-w-[50dvw]",
            !inDialog
              ? "border border-(--text-selected) bg-(--rating-bg)"
              : "border-none",
          )}
        >
          <CardContent
            className={cn(
              "flex flex-col gap-3 rounded px-10 py-6 text-center md:px-14 md:py-8 ",
              inDialog && "gap-5",
            )}
          >
            <p className="text-xl md:text-2xl">
              {m["course_rating.rate_course_enjoy"]()}{" "}
              <span className="text-(--card-tag-bg)">+20 XP</span>
            </p>

            <div className="mb-3">
              <p className="text-sm">{m["course_rating.overall_rating"]()}</p>
              <div className="flex items-center justify-center">
                <div className="relative mt-4 flex w-max flex-col items-center justify-center gap-4 text-center sm:inline-block sm:flex-row">
                  <div className="!w-14 -left-16 -top-2 flex items-center justify-center sm:absolute">
                    {ratings.general > 0 ? (
                      <motion.img
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        key={`owl-${ratings.general}`}
                        src={getOwlImage(ratings.general) || ""}
                        alt={`${getRatingText(ratings.general)} owl`}
                        className="!w-14 h-auto object-cover"
                      />
                    ) : (
                      <div className="w-20" />
                    )}
                  </div>
                  <div className="flex items-center">
                    <StarRating
                      initialValue={ratings.general}
                      onClick={(value: number) =>
                        handleRatingChange("general", value)
                      }
                      size={40}
                      starGap={15}
                      allowFraction={false}
                      totalStars={5}
                    />
                  </div>
                  <div className="-right-28 sm:-translate-y-1/2 top-1/2 flex w-[100px] items-center justify-center text-sm sm:absolute">
                    {getRatingText(ratings.general) || ""}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col justify-center gap-8 md:flex-row md:gap-12">
              <RatingItem
                category="course_quality"
                label={m["course_rating.course_quality"]()}
                value={ratings.course_quality}
                onChange={(value) =>
                  handleRatingChange("course_quality", value)
                }
              />

              <RatingItem
                category="learning_engagement"
                label={m["course_rating.learning_engagement"]()}
                value={ratings.learning_engagement}
                onChange={(value) =>
                  handleRatingChange("learning_engagement", value)
                }
              />

              <RatingItem
                category="ai_mentor"
                label={m["course_rating.ai_mentor_support"]()}
                value={ratings.ai_mentor}
                onChange={(value) => handleRatingChange("ai_mentor", value)}
              />
            </div>

            <div className="my-3 h-[1px] w-full bg-muted" />

            <div className="relative flex flex-col items-start justify-start gap-1">
              <p>{m["course_rating.share_thoughts"]()}</p>
              <Textarea
                className="min-h-[120px] w-full flex-1 shadow-[0px_10px_14px_0px_#10184026]"
                placeholder={m["course_rating.feedback_placeholder"]()}
                value={comment}
                onChange={(e) => setComment(e.target.value)}
              />
              <p className="absolute right-3 bottom-6 text-foreground/70 text-xs">
                {comment.length}/{CHARACTERS_MAX}
              </p>
              <p className="text-left text-foreground/70 text-xs italic">
                {!!comment.length && comment.length < CHARACTERS_MIN ? (
                  <span>
                    {m["course_rating.minimum_characters"]({
                      count: CHARACTERS_MIN,
                    })}
                  </span>
                ) : (
                  <span className="text-transparent">.</span>
                )}
              </p>
            </div>
            <div className="mt-3 flex items-center justify-center gap-6">
              <p
                className="flex cursor-pointer items-center gap-1 underline underline-offset-1"
                onClick={() =>
                  navigate({
                    to: `/my-learning`,
                    search: { status: ELearningStatus.COMPLETED },
                  })
                }
              >
                <UserSkipIcon /> {m["course_rating.skip_this"]()}
              </p>
              <Button
                onClick={() => mutateAsync()}
                disabled={
                  !isFormComplete ||
                  isPending ||
                  (comment.length > 0 && comment.length < CHARACTERS_MIN) ||
                  comment.length > CHARACTERS_MAX
                }
                size="lg"
                className="!px-12 rounded-full bg-(--learning-input-btn-bg)"
                variant="secondary"
              >
                {m["course_rating.submit_ratings"]()}
                <CheckIcon color="#fff" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

const RatingItem = ({ category, label, value, onChange }: RatingItemProps) => {
  return (
    <div className="flex flex-col items-center gap-1">
      {label && <p className="text-sm">{label}</p>}

      <div className="flex flex-col items-center gap-1">
        <StarRating
          initialValue={value}
          onClick={onChange}
          size={28}
          starGap={6} // Customize gap here
          allowFraction={false}
          totalStars={5}
        />
      </div>

      <p className="min-h-5 text-xs italic">
        {getRatingDescription(category, value)}
      </p>
    </div>
  );
};

const RatingCompleted = ({ points }: { points: number }) => {
  const { courseSlug } = Route.useParams();
  return (
    <div className={cn("h-full w-full overflow-y-auto px-3 pb-3")}>
      <motion.div
        variants={{
          hidden: { opacity: 0, y: 10 },
          visible: { opacity: 1, y: 0 },
        }}
        initial="hidden"
        animate="visible"
        transition={{ duration: 0.2, ease: "easeOut" }}
        className="mx-auto mt-4 flex max-w-4xl flex-col items-center gap-4 text-center md:mt-6 md:gap-8"
      >
        <div className="flex flex-col items-center gap-4 md:gap-5">
          <img
            src={LessonCompletedImg}
            alt="lesson completed"
            className="w-20 object-cover md:w-24"
          />

          <div className="flex flex-col items-center justify-center gap-3">
            <GradientText className="font-light text-2xl md:text-4xl">
              {m["course_rating.thanks_feedback"]()}
            </GradientText>
            <p className="rounded-lg bg-(--lesson-btn-revise) px-9 py-2 font-light text-3xl md:text-5xl">
              +{points} XP
            </p>
            <p className="font-light md:text-lg">
              {m["course_rating.review_helps_improve"]()}
            </p>
          </div>
          <div>
            <Link to="/courses">
              <Button
                size="xl"
                className="w-full rounded-full font-normal text-base"
              >
                <ArrowLineLeftIcon color="#fff" />
                {m["course_rating.back_to_courses"]()}
              </Button>
            </Link>
            <Button
              variant="outline"
              size="xl"
              className="w-full rounded-full bg-(--card) font-normal text-base"
              onClick={() => {
                navigator.clipboard.writeText(
                  `${window.location.origin}/courses/${courseSlug}`,
                );
                toast.success(m["course_rating.link_copied"]());
              }}
            >
              {m["course_rating.share_course_friends"]()}
              <ShareIcon />
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
