import { atom, useAtom } from "jotai";
import { useCallback, useEffect, useRef } from "react";
import { UUID } from "@/types/app";
import { useIsMobile } from "@/utils/use-mobile";

interface SelectionValidationConfig {
  minLength?: number;
  maxLength?: number;
  excludedSelectors: string[];
}

interface SelectedText {
  text: string;
  rect: DOMRect | null;
  sectionId: UUID;
}

interface ClickedText {
  text: string;
  rect: DOMRect | null;
  sectionId: UUID;
}

const isValidSentence = (sentence: string): boolean => {
  return !(
    Number.isInteger(Number(sentence)) &&
    Boolean(sentence) &&
    sentence.length < 5
  );
};

const defaultConfig: SelectionValidationConfig = {
  minLength: 5,
  maxLength: 1000,
  excludedSelectors: [".no-select", "button", "input"],
};

const clickedSentenceAtom = atom<ClickedText | null>(null);
const selectedTextAtom = atom<SelectedText | null>(null);
const hoveredSentenceAtom = atom<string | null>(null);

export function useSentenceSelection(config?: SelectionValidationConfig) {
  const [hoveredSentence, setHoveredSentence] = useAtom(hoveredSentenceAtom);
  const [clickedSentence, setClickedSentence] = useAtom(clickedSentenceAtom);
  const [selectedText, setSelectedText] = useAtom(selectedTextAtom);
  const isMobile = useIsMobile();

  const selectionTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(
    null,
  );

  const {
    minLength = 5,
    maxLength = 500,
    excludedSelectors = defaultConfig.excludedSelectors,
  } = {
    ...defaultConfig,
    ...config,
  };

  const isValidSelection = useCallback(
    (selection: Selection): boolean => {
      const text = selection.toString().trim();

      if (text.length < minLength || text.length > maxLength) {
        return false;
      }

      if (selection.rangeCount === 0) {
        return false;
      }

      const range = selection.getRangeAt(0);
      const { commonAncestorContainer } = range;

      // Check for forbidden selectors only if commonAncestor is an Element
      if (commonAncestorContainer instanceof Element) {
        const isForbidden = excludedSelectors?.some((selector) =>
          commonAncestorContainer.closest(selector),
        );
        return !isForbidden;
      }

      if (commonAncestorContainer.parentElement) {
        // If it's a text node, check its parent
        const isForbidden = excludedSelectors?.some((selector) =>
          commonAncestorContainer.parentElement?.closest(selector),
        );
        return !isForbidden;
      }

      return true;
    },
    [minLength, maxLength, excludedSelectors],
  );

  // Centralized hover management
  const handleSentenceHover = useCallback(
    (sentence: string) => {
      if (!isValidSentence(sentence) || selectedText?.text) {
        return;
      }

      setHoveredSentence(sentence);
    },
    [selectedText, setHoveredSentence],
  );

  const handleSentenceLeave = useCallback(() => {
    setHoveredSentence(null);
  }, [setHoveredSentence]);

  const handleSentenceClick = useCallback(
    (sentence: string, element?: HTMLElement) => {
      if (!isValidSentence(sentence) || sentence.length < 3) return;

      const rect = element?.getBoundingClientRect() || null;
      const sectionDiv = element?.closest("[data-section-id]");
      if (!sectionDiv) {
        return;
      }
      setClickedSentence({
        text: sentence,
        rect: rect,
        sectionId: sectionDiv.getAttribute("data-section-id") as UUID,
      });
      setSelectedText(null);
      setHoveredSentence(null);
    },
    [setClickedSentence, setSelectedText, setHoveredSentence],
  );

  const resetClicked = useCallback(() => {
    setClickedSentence(null);
  }, [setClickedSentence]);

  const clearSelection = useCallback(() => {
    setSelectedText(null);
    window.getSelection()?.removeAllRanges();
  }, [setSelectedText]);

  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection();

    // Clear hover when text is being selected
    setHoveredSentence(null);

    if (!selection?.toString().trim()) {
      setSelectedText(null);
      return;
    }

    if (!isValidSelection(selection)) {
      setSelectedText(null);
      return;
    }

    const range = selection.getRangeAt(0);
    const ancestor = range.commonAncestorContainer;
    const container =
      ancestor.nodeType === Node.ELEMENT_NODE
        ? (ancestor as Element)
        : ancestor.parentElement;

    // Check if selection is inside selectable-content
    const isInsideSelectableContent =
      container?.closest(".selectable-content") !== null;

    if (!isInsideSelectableContent) {
      setSelectedText(null);
      return;
    }

    const rects = range.getClientRects();
    if (rects.length === 0) {
      return;
    }

    const sectionDiv = container?.closest("[data-section-id]");

    setSelectedText({
      text: selection.toString(),
      rect: rects[isMobile ? rects.length - 1 : 0],
      sectionId: sectionDiv?.getAttribute("data-section-id") as UUID,
    });

    // Clear other states when text is selected
    setClickedSentence(null);
    setHoveredSentence(null);
  }, [
    setSelectedText,
    setClickedSentence,
    setHoveredSentence,
    isValidSelection,
    isMobile,
  ]);

  useEffect(() => {
    const checkForSelection = () => {
      // Clear hover immediately when selection starts
      setHoveredSentence(null);

      if (selectionTimeoutRef.current) {
        clearTimeout(selectionTimeoutRef.current);
      }

      selectionTimeoutRef.current = setTimeout(handleTextSelection, 400);
    };

    document.addEventListener("mouseup", checkForSelection);
    document.addEventListener("touchend", checkForSelection);
    document.addEventListener("selectionchange", checkForSelection);

    return () => {
      document.removeEventListener("mouseup", checkForSelection);
      document.removeEventListener("touchend", checkForSelection);
      document.removeEventListener("selectionchange", checkForSelection);
      if (selectionTimeoutRef.current) {
        clearTimeout(selectionTimeoutRef.current);
      }
    };
  }, [handleTextSelection, setHoveredSentence]);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent | TouchEvent) => {
      const target = e.target as Node;
      const shouldClear = !document
        .querySelector(".exam-tool-container, .mobile-selection-clear")
        ?.contains(target);

      if (shouldClear && !isMobile && selectedText) {
        setTimeout(() => {
          if (!window.getSelection()?.toString().trim()) {
            clearSelection();
          }
        }, 100);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("touchstart", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("touchstart", handleClickOutside);
    };
  }, [selectedText, clearSelection, isMobile]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (selectionTimeoutRef.current) {
        clearTimeout(selectionTimeoutRef.current);
      }
    };
  }, []);

  return {
    hoveredSentence,
    clickedSentence,
    selectedText,
    handleSentenceHover,
    handleSentenceLeave,
    handleSentenceClick,
    resetClicked,
    clearSelection,
    currentSelection: selectedText?.text || clickedSentence?.text,
  } as const;
}
