import { motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { memo } from "react";
import { ArrowLineLeftIcon, ArrowLineRightIcon } from "@/components/icons";
import { renderTipTapNode } from "@/components/tip-tap-render";
import { But<PERSON> } from "@/components/ui/button";
import * as m from "@/paraglide/messages.js";
import { atomVisibleSections } from "@/store/lesson";
import { PropsWithClassName } from "@/types/app";
import { SectionText } from "@/types/lessons";
import { cn } from "@/utils/cn";
import NavigationControl from "./navigation-control";

interface ExamSlideProps extends PropsWithClassName {
  section: SectionText;
  loading: boolean;
  navigateSection: (newIndex: number) => void;
  isGameNext: boolean;
  sectionIndex: number;
  currentSectionIndex: number;
  navigateBackSection?: () => void;
}

export const ExamSlide = memo(
  ({
    section,
    loading,
    navigateSection,
    isGameNext,
    className,
    sectionIndex,
    navigateBackSection,
  }: ExamSlideProps) => {
    const visibleSections = useAtomValue(atomVisibleSections);
    const isLastVisibleSection = sectionIndex === visibleSections.length - 1;

    return (
      <motion.div
        className={cn("flex w-full flex-col gap-3 md:gap-4", className)}
        variants={{
          hidden: { opacity: 0, y: 10 },
          visible: { opacity: 1, y: 0 },
        }}
        initial="hidden"
        animate="visible"
        transition={{ duration: 0.2, ease: "easeOut" }}
      >
        {section.contentParsed && (
          <div className="exam-content relative">
            {renderTipTapNode(section.contentParsed)}
          </div>
        )}
        {(navigateBackSection || isLastVisibleSection) && (
          <NavigationControl
            navigateBackSection={navigateBackSection}
            sectionIndex={sectionIndex}
            loading={loading}
            navigateSection={navigateSection}
            isGameNext={isGameNext}
          />
        )}
      </motion.div>
    );
  },
);
