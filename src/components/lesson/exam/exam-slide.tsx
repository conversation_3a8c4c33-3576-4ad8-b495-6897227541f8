import { motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { memo } from "react";
import { ArrowLineLeftIcon, ArrowLineRightIcon } from "@/components/icons";
import { renderTipTapNode } from "@/components/tip-tap-render";
import { But<PERSON> } from "@/components/ui/button";
import * as m from "@/paraglide/messages.js";
import { atomVisibleSections } from "@/store/lesson";
import { PropsWithClassName } from "@/types/app";
import { SectionText } from "@/types/lessons";
import { cn } from "@/utils/cn";

interface ExamSlideProps extends PropsWithClassName {
  section: SectionText;
  loading: boolean;
  navigateSection: (newIndex: number) => void;
  isGameNext: boolean;
  sectionIndex: number;
  currentSectionIndex: number;
  navigateBackSection?: () => void;
}

export const ExamSlide = memo(
  ({
    section,
    loading,
    navigateSection,
    isGameNext,
    className,
    sectionIndex,
    navigateBackSection,
  }: ExamSlideProps) => {
    const visibleSections = useAtomValue(atomVisibleSections);
    const isLastVisibleSection = sectionIndex === visibleSections.length - 1;

    return (
      <motion.div
        className={cn("flex w-full flex-col gap-3 md:gap-4", className)}
        variants={{
          hidden: { opacity: 0, y: 10 },
          visible: { opacity: 1, y: 0 },
        }}
        initial="hidden"
        animate="visible"
        transition={{ duration: 0.2, ease: "easeOut" }}
      >
        {section.contentParsed && (
          <div className="exam-content relative">
            {renderTipTapNode(section.contentParsed)}
          </div>
        )}
        {(navigateBackSection || isLastVisibleSection) && (
          <div className="flex flex-col gap-2">
            <div className="h-[1px] w-full bg-gray-200" />
            <div className="flex items-center gap-2">
              {navigateBackSection && !!sectionIndex && (
                <Button
                  variant="outline"
                  disabled={loading}
                  size="lg"
                  className="md:!px-16 rounded-full px-10"
                  onClick={() => navigateBackSection()}
                >
                  <ArrowLineLeftIcon />
                  {m["common.actions.back"]()}
                </Button>
              )}
              <div className="flex gap-2">
                <Button
                  disabled={loading}
                  size="lg"
                  className="md:!px-16 rounded-full px-10"
                  onClick={() => navigateSection(sectionIndex + 1)}
                >
                  {isGameNext
                    ? m["learning.exam_slide.lets_play"]()
                    : m["common.actions.continue"]()}
                  <ArrowLineRightIcon />
                </Button>
              </div>
            </div>
          </div>
        )}
      </motion.div>
    );
  },
);
