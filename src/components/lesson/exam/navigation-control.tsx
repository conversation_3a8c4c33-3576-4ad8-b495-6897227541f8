import { ArrowLineLeftIcon, ArrowLineRightIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import * as m from "@/paraglide/messages.js";

type Props = {
  sectionIndex: number;
  loading: boolean;
  navigateSection: (newIndex: number) => void;
  isGameNext: boolean;
  navigateBackSection?: () => void;
};

const NavigationControl = ({
  navigateBackSection,
  sectionIndex,
  loading,
  navigateSection,
  isGameNext,
}: Props) => {
  return (
    <div className="flex flex-col gap-2">
      <div className="h-[1px] w-full bg-gray-200" />
      <div className="flex items-center gap-2">
        {navigateBackSection && !!sectionIndex && (
          <Button
            variant="outline"
            disabled={loading}
            size="lg"
            className="md:!px-16 rounded-full px-10"
            onClick={() => navigateBackSection()}
          >
            <ArrowLineLeftIcon />
            {m["common.actions.back"]()}
          </Button>
        )}
        <div className="flex gap-2">
          <Button
            disabled={loading}
            size="lg"
            className="md:!px-16 rounded-full px-10"
            onClick={() => navigateSection(sectionIndex + 1)}
          >
            {isGameNext
              ? m["learning.exam_slide.lets_play"]()
              : m["common.actions.continue"]()}
            <ArrowLineRightIcon />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NavigationControl;
