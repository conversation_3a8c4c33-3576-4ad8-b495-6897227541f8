import { UUID } from "crypto";
import { useSet<PERSON>tom } from "jotai/index";
import { useCallback, useRef } from "react";
import {
  atomCurrentQuizId,
  atomCurrentSection,
  atomMappingQuizIdToUserAnswer,
} from "@/store/chat";
import { Section, SectionType } from "@/types/lessons";

interface UseExamScrollProps {
  isRevise: boolean;
  visibleSections: Section[];
  disableAutoScroll?: boolean;
}

// When the container is mounted:
// 1. Tracking section visibility (including set current quizId and current quiz answer)
// 2. If the lesson is not revise, scroll to the last section
export const useExamScroll = ({
  isRevise,
  visibleSections,
  disableAutoScroll,
}: UseExamScrollProps) => {
  const observerRef = useRef<IntersectionObserver | null>(null);
  const setCurrentSection = useSetAtom(atomCurrentSection);
  const setCurrentQuizId = useSetAtom(atomCurrentQuizId);
  const setMappingQuizIdToUserAnswer = useSetAtom(
    atomMappingQuizIdToUserAnswer,
  );

  const startTrackingSectionVisible = useCallback(() => {
    if (disableAutoScroll) return;
    const sectionElements = document.querySelectorAll<HTMLElement>(
      "div[data-section-id]",
    );

    if (!sectionElements.length) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((sectionElement) => {
          if (!sectionElement.isIntersecting) return;

          const sectionId =
            sectionElement.target.getAttribute("data-section-id");
          if (!sectionId) return;

          const section = visibleSections.find((v) => v.id === sectionId);

          if (!section) return;
          setCurrentSection((prev) => {
            // Reset mappingQuizIdToUserAnswer when the lesson is changed
            if (prev?.lesson_id !== section.lesson_id) {
              setMappingQuizIdToUserAnswer({});
            }

            return section;
          });

          if ([SectionType.TEXT, SectionType.GAME].includes(section.type))
            return;

          const quizId = sectionElement.target
            .querySelectorAll<HTMLElement>("div[data-quiz-id]")[0]
            ?.getAttribute("data-quiz-id");

          if (!quizId) return;
          setCurrentQuizId(quizId as UUID);
        });
      },
      {
        threshold: 0.5,
        rootMargin: "-20% 0px",
      },
    );

    sectionElements.forEach((element) => {
      observerRef.current?.observe(element);
    });
  }, [
    visibleSections,
    setCurrentSection,
    setCurrentQuizId,
    setMappingQuizIdToUserAnswer,
    disableAutoScroll,
  ]);

  const waitForElementInView = useCallback(
    (element: HTMLElement, callback: () => void) => {
      const observer = new IntersectionObserver(
        (lastSectionEntries, obs) => {
          if (lastSectionEntries[0].isIntersecting) {
            callback();
            obs.disconnect();
          }
        },
        { threshold: 0.5 },
      );
      observer.observe(element);
    },
    [],
  );

  const scrollToLastSection = useCallback(
    (afterScroll?: () => void) => {
      if (isRevise || visibleSections.length <= 1) {
        afterScroll?.();
        return;
      }

      const lastVisibleSection = visibleSections[visibleSections.length - 1];
      setCurrentSection(lastVisibleSection);

      setTimeout(() => {
        const lastSectionElement = document.getElementById(
          lastVisibleSection.id,
        );
        if (!lastSectionElement) return;

        lastSectionElement.scrollIntoView({
          behavior: "smooth",
        });

        if (afterScroll) {
          waitForElementInView(lastSectionElement, afterScroll);
        }
      }, 500);
    },
    [isRevise, setCurrentSection, visibleSections, waitForElementInView],
  );

  const onContainerMounted = useCallback(
    (node: HTMLElement | null) => {
      if (!node) return;

      scrollToLastSection(startTrackingSectionVisible);

      // Clean up
      return () => {
        if (observerRef.current) {
          observerRef.current.disconnect();
        }
      };
    },
    [scrollToLastSection, startTrackingSectionVisible],
  );

  return {
    onContainerMounted,
  };
};
