import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { useAtom, useAtomValue } from "jotai";
import { useEffect, useMemo } from "react";
import {
  courseProgressesQueryOptions,
  useUserCourseProgresses,
} from "@/services/courses";
import {
  completeLesson,
  UpdateUserProgressParams,
  updateUserProgress,
} from "@/services/lessons";
import { atomAuth } from "@/store/auth";
import { atomCurrentSection } from "@/store/chat";
import { atomVisibleSections } from "@/store/lesson";
import { Course, UserCourse } from "@/types/courses";
import { Lesson, Section } from "@/types/lessons";

const MESSAGE_FORBIDDEN_LESSON = "previous course not completed yet";

interface UseExamSectionProps {
  sections: Section[];
  lesson: Lesson;
  lessons: Lesson[];
  course: Course;
}

export const useExamSection = ({
  sections,
  lesson,
  lessons,
  course,
}: UseExamSectionProps) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { data: userCourse, isPending: isPendingProgress } =
    useUserCourseProgresses(course.id);
  const auth = useAtomValue(atomAuth);
  const [currentSection, setCurrentSection] = useAtom(atomCurrentSection);
  const [visibleSections, setVisibleSections] = useAtom(atomVisibleSections);

  const isRevise = useMemo(() => {
    return Boolean(
      lesson.id && userCourse?.progresses?.[lesson.id] === "COMPLETED",
    );
  }, [userCourse?.progresses, lesson.id]);

  const lastSectionID = useMemo(() => {
    if (!userCourse?.progresses) {
      return;
    }

    const progress = userCourse.progresses[lesson.id];
    if (!progress) {
      return;
    }

    return progress;
  }, [userCourse, lesson.id]);

  const currentSectionIndex = sections.findIndex(
    (section) => section.id === currentSection?.id,
  );

  //Note: Handle for case sectionId is uid. In case its value is "COMPLETED", it's already checked by isRevise
  const isSectionFinishedBefore = useMemo(() => {
    const latestSectionIndex = sections.findIndex(
      (section) => section.id === lastSectionID,
    );
    return (
      latestSectionIndex !== -1 && currentSectionIndex < latestSectionIndex
    );
  }, [lastSectionID, currentSectionIndex, sections]);

  const mutationProgress = useMutation({
    mutationFn: async ({ lesson_id, section_id }: UpdateUserProgressParams) => {
      await updateUserProgress({ lesson_id, section_id });
    },
    onSettled: async () => {
      await queryClient.invalidateQueries({
        queryKey: courseProgressesQueryOptions(course.id, auth?.jwt).queryKey,
      });
    },
    onMutate: ({ lesson_id, section_id }) => {
      queryClient.setQueryData(
        courseProgressesQueryOptions(course.id, auth?.jwt).queryKey,
        (oldData: UserCourse | undefined) => {
          if (!oldData || !lesson_id) return oldData;
          return {
            ...oldData,
            progresses: {
              ...oldData.progresses,
              [lesson_id]: section_id || "",
            },
          };
        },
      );
    },
    onError: (error: Error) => {
      if (error.message === MESSAGE_FORBIDDEN_LESSON) {
        navigate({ to: `/courses/${course.slug}` });
      }
    },
  });

  const isLoading = isPendingProgress || mutationProgress.isPending;
  const countCompleted = lessons.findIndex((v) => v.id === lesson.id) + 1; // TODO: might use memo

  const navigateBackSection = (idx: number) => {
    if (idx < 0) {
      return;
    }

    const targetSection = sections[idx];
    if (targetSection) {
      setCurrentSection(targetSection);
    }
  };

  const navigateSection = async (idx: number) => {
    if (idx < 0) {
      return;
    }

    const nextSection = sections[idx];
    if (nextSection) {
      setCurrentSection(nextSection);
      setVisibleSections((v) => [...v, nextSection]);
      if (isRevise || isSectionFinishedBefore) {
        return;
      }

      return mutationProgress.mutateAsync({
        section_id: nextSection.id,
        lesson_id: lesson.id,
      });
    }

    if (isRevise) {
      if (countCompleted >= lessons.length) {
        // no more lesson
        return navigate({
          to: `/courses/$courseSlug`,
          params: {
            courseSlug: course.slug,
          },
        });
      }

      return navigate({
        to: `/learn/$courseSlug/$lessonSlug`,
        params: {
          courseSlug: course.slug,
          lessonSlug: lessons[countCompleted]?.slug,
        },
      });
    }
    await completeLesson(lesson.id);
    await queryClient.invalidateQueries({
      queryKey: ["profile"],
    });
    setCurrentSection(nextSection); // it's undefined but we keep it here to mark special case: (Lesson completed)
  };

  useEffect(() => {
    if (isLoading || !sections.length) {
      return;
    }

    if (lastSectionID === "STARTED") {
      setCurrentSection(sections[0]);
      setVisibleSections([sections[0]]);
      mutationProgress.mutate({
        section_id: sections[0].id,
        lesson_id: lesson.id,
      });
      return;
    }

    if (lastSectionID === "COMPLETED") {
      setCurrentSection((v) => {
        if (v === undefined) {
          return v;
        }

        return sections[0];
      });
      setVisibleSections(sections);
      return;
    }

    const target = sections.find((section) => section.id === lastSectionID);
    const targetIndex = sections.findIndex(
      (section) => section.id === lastSectionID,
    );

    if (!target) {
      setCurrentSection(sections[0]);
      setVisibleSections([sections[0]]);
      return;
    }

    setCurrentSection(target);
    setVisibleSections(() => sections.slice(0, targetIndex + 1));
  }, [
    isLoading,
    lastSectionID,
    sections,
    setCurrentSection,
    lesson.id,
    mutationProgress.mutate,
    setVisibleSections,
  ]);

  return {
    isRevise,
    currentSection,
    currentSectionIndex,
    navigateSection,
    mutationProgress,
    countCompleted,
    visibleSections,
    navigateBackSection,
  };
};
