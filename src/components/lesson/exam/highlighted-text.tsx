import { useEffect, useRef, useState } from "react";
import { useSentenceSelection } from "@/components/lesson/exam/use-sentence-selected";

interface HighlightedTextProps {
  text: string;
}

const generateSentenceId = (() => {
  const cache = new Map<string, string>();
  return (sentence: string): string => {
    if (cache.has(sentence)) return cache.get(sentence)!;

    let hash = 0;
    for (let i = 0; i < sentence.length; i++) {
      const char = sentence.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash |= 0;
    }

    const id = `sentence-${Math.abs(hash)}`;
    cache.set(sentence, id);
    return id;
  };
})();

const detectSentenceFromText = (
  text: string,
  element: HTMLElement,
): { sentence: string; id: string } => {
  const sentenceElement = element.closest("[data-sentence-id]");
  if (sentenceElement) {
    const fullText = sentenceElement.getAttribute("data-full-text");
    const sentenceId = sentenceElement.getAttribute("data-sentence-id");
    if (fullText && sentenceId) {
      return { sentence: fullText, id: sentenceId };
    }
  }

  const container = element.closest(".selectable-content");
  const fullText = container?.textContent || text;

  const textIndex = fullText.indexOf(text);

  if (textIndex === -1 || fullText.length <= 50) {
    return { sentence: fullText.trim(), id: generateSentenceId(fullText) };
  }

  const beforeText = fullText.slice(0, textIndex);
  const afterText = fullText.slice(textIndex + text.length);

  let sentenceStart = 0;
  const sentenceStarters = /[.!?]\s+/g;
  let match: RegExpExecArray | null;
  match = sentenceStarters.exec(beforeText);
  while (match) {
    sentenceStart = match.index + match[0].length;
    match = sentenceStarters.exec(beforeText);
  }

  const sentenceEndMatch = afterText.match(/[.!?]/);
  const sentenceEnd = sentenceEndMatch
    ? textIndex + text.length + (sentenceEndMatch?.index || 0) + 1
    : fullText.length;

  const extracted = fullText.slice(sentenceStart, sentenceEnd).trim();
  const sentence = extracted.length > 500 ? fullText.trim() : extracted;

  return { sentence, id: generateSentenceId(sentence) };
};

const isValidSentenceForHighlight = (sentence: string): boolean => {
  const trimmed = sentence.trim();

  if (trimmed.length < 10) return false;

  if (/^\d+$/.test(trimmed)) return false;

  return !/^[^\w\s]+$/.test(trimmed);
};

export const HighlightedText = ({ text }: HighlightedTextProps) => {
  const {
    clickedSentence,
    hoveredSentence,
    handleSentenceHover,
    handleSentenceLeave,
    handleSentenceClick,
    selectedText,
  } = useSentenceSelection();

  const elementRef = useRef<HTMLSpanElement>(null);
  const mouseDownRef = useRef<{ x: number; y: number } | null>(null);
  const [sentenceData, setSentenceData] = useState<{
    sentence: string;
    id: string;
  }>(() => ({
    sentence: text,
    id: generateSentenceId(text),
  }));

  const shouldHighlight = isValidSentenceForHighlight(sentenceData.sentence);
  const isClicked = clickedSentence?.text === sentenceData.sentence;
  const isHovered = hoveredSentence === sentenceData.sentence;

  useEffect(() => {
    if (elementRef.current) {
      const data = detectSentenceFromText(text, elementRef.current);
      setSentenceData(data);
    }
  }, [text]);

  useEffect(() => {
    const styleId = "sentence-hover-styles";
    if (!document.getElementById(styleId)) {
      const style = document.createElement("style");
      style.id = styleId;
      document.head.appendChild(style);
    }
  }, []);

  useEffect(() => {
    const elements = document.querySelectorAll(
      `[data-sentence-id="${sentenceData.id}"]`,
    );

    if (isHovered) {
      elements.forEach((el) => el.classList.add("sentence-hover-active"));
    } else {
      elements.forEach((el) => el.classList.remove("sentence-hover-active"));
    }

    return () => {
      elements.forEach((el) => el.classList.remove("sentence-hover-active"));
    };
  }, [isHovered, sentenceData.id]);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!shouldHighlight) return;
    mouseDownRef.current = { x: e.clientX, y: e.clientY };
  };

  const handleMouseUp = (e: React.MouseEvent) => {
    if (!shouldHighlight || selectedText) return;

    const start = mouseDownRef.current;
    if (!start) return;

    const deltaX = Math.abs(e.clientX - start.x);
    const deltaY = Math.abs(e.clientY - start.y);
    const isClick = deltaX < 5 && deltaY < 5;

    const selection = window.getSelection();
    const hasSelection = selection?.toString().trim();

    if (!isClick || hasSelection) {
      mouseDownRef.current = null;
      return;
    }

    handleSentenceClick(sentenceData.sentence, e.currentTarget as HTMLElement);

    mouseDownRef.current = null;
  };

  const handleMouseEnter = () => {
    if (!shouldHighlight) return;

    const selection = window.getSelection();
    if (selection?.toString().trim()) return;

    handleSentenceHover(sentenceData.sentence);
  };

  const handleMouseLeave = () => {
    if (!shouldHighlight) return;
    handleSentenceLeave();
  };

  return (
    <span
      ref={elementRef}
      className={`${isClicked ? "bg-(--text-selected)" : ""} ${
        shouldHighlight ? "transition-colors duration-200" : ""
      }`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      data-sentence-id={sentenceData.id}
      data-sentence-text={sentenceData.sentence}
    >
      {text}
    </span>
  );
};
