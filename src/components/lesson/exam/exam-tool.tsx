import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { AnimatePresence } from "framer-motion";
import { useSetAtom } from "jotai/index";
import explainImg from "@/assets/images/explain-tool.svg";
import giveSamplesImage from "@/assets/images/give-sample-tool.svg";
import owlAskMe from "@/assets/images/owls/owl-exam-tool.png";
import summaryImage from "@/assets/images/summary-tool.svg";
import translateImage from "@/assets/images/translate-tool.svg";
import { useSentenceSelection } from "@/components/lesson/exam/use-sentence-selected";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import * as m from "@/paraglide/messages.js";
import { chatProfileQueryOptions } from "@/services/conversations";
import { atomCurrentChat } from "@/store/chat";
import { ChatbotAction } from "@/types/chat";

const tools: { title: ChatbotAction; image: string }[] = [
  {
    title: ChatbotAction.EXPLAIN,
    image: explainImg,
  },
  {
    title: ChatbotAction.EXAMPLES,
    image: giveSamplesImage,
  },
  {
    title: ChatbotAction.SUMMARY,
    image: summaryImage,
  },
  {
    title: ChatbotAction.TRANSLATE,
    image: translateImage,
  },
];

export const ExamTool = () => {
  const setCurrentMessage = useSetAtom(atomCurrentChat);
  const { currentSelection, selectedText, clearSelection, resetClicked } =
    useSentenceSelection();
  const queryChatProfile = useQuery(chatProfileQueryOptions());

  const handleSelectTool = (tool: ChatbotAction) => {
    if (!currentSelection) return;

    clearSelection();
    resetClicked();
    if (tool === ChatbotAction.TRANSLATE) {
      const isEnglish = queryChatProfile.data?.preferences.language === "en";
      setCurrentMessage({
        type: tool,
        input: isEnglish
          ? m["exam_tool.translate_to_vietnamese"]()
          : m["exam_tool.translate_to_english"](),
        quote: currentSelection,
        hints: {
          language:
            queryChatProfile.data?.preferences.language === "en" ? "vi" : "en",
        },
      });
      return;
    }

    if (tool === ChatbotAction.EXPLAIN) {
      setCurrentMessage((prev) => ({
        type: tool,
        input: m["exam_tool.explain_this"](),
        quote: currentSelection,
        hints: {
          ...prev?.hints,
          section_id: selectedText?.sectionId || undefined,
        },
      }));
      return;
    }

    if (tool === ChatbotAction.EXAMPLES) {
      setCurrentMessage((prev) => ({
        type: tool,
        input: m["exam_tool.give_examples"](),
        quote: currentSelection,
        hints: {
          ...prev?.hints,
          section_id: selectedText?.sectionId || undefined,
        },
      }));
      return;
    }

    if (tool === ChatbotAction.SUMMARY) {
      setCurrentMessage((prev) => ({
        type: tool,
        input: m["exam_tool.summarize_this"](),
        quote: currentSelection,
        hints: {
          ...prev?.hints,
          section_id: selectedText?.sectionId || undefined,
        },
      }));
      return;
    }

    setCurrentMessage((prev) => ({
      type: tool,
      input: "",
      quote: currentSelection,
      hints: {
        ...prev?.hints,
        section_id: selectedText?.sectionId || undefined,
      },
    }));
  };

  return (
    <div className="-translate-y-2 z-40 flex h-full w-max items-center justify-center gap-1 rounded-md border-(--card-tag-bg) border-2 bg-(--learning-layout-bg) p-1 shadow-[0px_2px_6px_0px_#00000040]">
      {tools.map((tool) => (
        <div key={tool.title}>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="rounded-md"
                  onClick={() => handleSelectTool(tool.title)}
                >
                  <img
                    src={tool.image}
                    alt={tool.title}
                    className="size-4 object-cover md:size-5"
                  />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{tool.title}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      ))}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="rounded-md"
              onClick={() => handleSelectTool(ChatbotAction.CHAT)}
            >
              <img
                className="ml-1 size-8 object-cover"
                src={owlAskMe}
                alt="Ask me"
              />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{m["exam_tool.ask_something_else"]()}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export const ExamToolRender = ({
  isMobile,
  rect,
}: {
  rect: DOMRect;
  isMobile: boolean;
}) => {
  return (
    <AnimatePresence>
      <motion.div
        key={`tooltip-${rect.top}-${rect.left}`}
        style={{
          position: "fixed",
          top: `${isMobile ? rect.bottom + 10 : rect.top - 40}px`,
          left: `${isMobile ? rect.left / 2 : rect.left}px`,
          zIndex: 50,
        }}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        transition={{ duration: 0.2 }}
      >
        <ExamTool />
      </motion.div>
    </AnimatePresence>
  );
};
