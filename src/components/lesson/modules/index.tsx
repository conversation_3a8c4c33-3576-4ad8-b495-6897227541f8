import { useMemo } from "react";
import { LessonGroup } from "@/components/lesson/modules/lesson-group";
import { ModuleFooter } from "@/components/lesson/modules/module-footer";
import { ModuleHeader } from "@/components/lesson/modules/module-header";
import * as m from "@/paraglide/messages.js";
import { PropsWithClassName } from "@/types/app";
import { UserCourse } from "@/types/courses";
import { Lesson } from "@/types/lessons";
import { cn } from "@/utils/cn";

interface LessonModuleProps {
  lessons: Lesson[];
  userCourse: UserCourse | undefined;
  currentLesson?: Lesson;
}

export const LessonModules = ({
  lessons,
  userCourse,
  className,
  currentLesson,
}: LessonModuleProps & PropsWithClassName) => {
  const isCourseCompleted = useMemo(() => {
    return (
      Object.keys(userCourse?.progresses || {}).length === lessons.length &&
      lessons?.every((lesson) => {
        return userCourse?.progresses[lesson.id] === "COMPLETED";
      })
    );
  }, [lessons, userCourse?.progresses]);

  if (!lessons?.length) {
    return null;
  }

  return (
    <div
      className={cn("w-full px-2 pb-16 sm:px-3 md:px-6 lg:pb-44", className)}
    >
      <ModuleHeader
        isStarted={!!userCourse?.progresses}
        img={"https://i.imgur.com/t8WdYLB.png"}
        title={m["courses.lesson_modules.start"]()}
      />
      <LessonGroup
        userCourse={userCourse}
        lessons={lessons}
        currentLesson={currentLesson}
      />
      <div className="flex justify-end">
        <ModuleFooter
          isCompleted={isCourseCompleted}
          title={m["courses.lesson_modules.end"]()}
        />
      </div>
    </div>
  );
};
