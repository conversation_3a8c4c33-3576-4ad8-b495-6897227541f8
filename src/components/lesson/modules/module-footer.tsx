import { ConnectorIcon, LessonEndIcon } from "@/components/icons";
import { cn } from "@/utils/cn";

interface ModuleFooterProps {
  isCompleted: boolean;
  title: string;
}

export const ModuleFooter = ({ isCompleted, title }: ModuleFooterProps) => {
  return (
    <div className="relative flex w-full flex-col items-center lg:w-1/2 lg:flex-row lg:items-end">
      <ConnectorIcon type="end" />
      <div className="lg:-bottom-[4.5rem] relative right-0 bottom-0 flex flex-col gap-y-2 bg-(--background) lg:absolute">
        <div
          className={cn(
            "inline-block rounded-full border-[3px] p-1",
            isCompleted
              ? "border-(--card-tag-bg)"
              : "border-(--lesson-group-border)",
          )}
        >
          <div className="size-12 rounded-full bg-muted object-cover p-1 md:size-16">
            <LessonEndIcon className="h-full w-full" />
          </div>
        </div>
        <p
          className={cn(
            "rounded-xs bg-muted p-2 text-center font-bold [font-size:var(--size-2xs)]",
          )}
        >
          {title.toUpperCase()}
        </p>
      </div>
    </div>
  );
};
