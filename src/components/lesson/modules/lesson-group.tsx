import { useMemo } from "react";
import { LessonItem } from "@/components/lesson/modules/lesson-item";
import { LessonItemSkeleton } from "@/components/lesson/modules/lesson-item-skeleton";
import { Image } from "@/components/ui/image";
import * as m from "@/paraglide/messages.js";
import { useProfile } from "@/services/auth";
import { groupAndSortLessonsAsArray } from "@/services/lessons";
import { UserCourse } from "@/types/courses";
import { Lesson, LessonStatus } from "@/types/lessons";
import { cn } from "@/utils/cn";

type LessonGroupProps = {
  lessons: Lesson[];
  userCourse: UserCourse | undefined;
  currentLesson: Lesson | undefined;
};

export const LessonGroup = ({
  lessons,
  userCourse,
  currentLesson,
}: LessonGroupProps) => {
  const { data: user } = useProfile();

  const processedLessons = useMemo(() => {
    if (!lessons) return [];
    let lastCompletedIndex = -1;
    const progresses = userCourse?.progresses || {};

    return lessons.map((lesson, index) => {
      const progressStatus = progresses[lesson.id];

      if (progressStatus === "COMPLETED") {
        lastCompletedIndex = index;
        return { ...lesson, status: LessonStatus.COMPLETED };
      }

      if (progressStatus) {
        return {
          ...lesson,
          status:
            progressStatus === "STARTED"
              ? LessonStatus.CURRENT
              : LessonStatus.IN_PROGRESS,
        };
      }

      if (index === 0) {
        return {
          ...lesson,
          status: !lesson.requirements?.keys
            ? LessonStatus.CURRENT
            : LessonStatus.LOCKED,
        };
      }

      return {
        ...lesson,
        status:
          index === lastCompletedIndex + 1
            ? !lesson.requirements?.keys
              ? LessonStatus.CURRENT
              : LessonStatus.LOCKED
            : undefined,
      };
    });
  }, [lessons, userCourse?.progresses]);

  const groupedLessons = useMemo(() => {
    return groupAndSortLessonsAsArray(processedLessons);
  }, [processedLessons]);

  if (!lessons?.length) {
    return (
      <div className="flex w-full flex-col gap-y-4 rounded-xl border-[5px] border-border bg-card p-4">
        {[...Array(3)].map((_, i) => (
          <LessonItemSkeleton key={`skeleton-${i.toString()}`} />
        ))}
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col">
      {groupedLessons.map((group, groupIndex) => (
        <div
          className="flex w-full flex-col items-center"
          key={`group-${groupIndex.toString()}`}
        >
          {!group.isUngrouped && (
            <ModuleHeader
              index={groupIndex}
              img={"https://i.imgur.com/t8WdYLB.png"}
              title={group.groupName}
              isStarted={!!userCourse?.progresses[group.lessons[0].id]}
            />
          )}
          <div className="flex w-full flex-col overflow-hidden rounded-xl border border-[--lesson-group-border] bg-(--card) shadow-[0px_10px_14px_0px_#10184026]">
            {group.lessons.map((lesson) => {
              return (
                <LessonItem
                  key={`lesson-${lesson.id}`}
                  lesson={lesson}
                  currentLesson={currentLesson}
                  user={user}
                />
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
};

const ModuleHeader = ({
  img,
  title,
  isStarted,
  index,
}: {
  img: string;
  title: string;
  isStarted?: boolean;
  index: number;
}) => {
  return (
    <div className="relative flex w-full flex-1 flex-col items-center ">
      {index !== 0 && <div className="h-20 w-1 bg-gray-200" />}
      <div className="flex w-full flex-1 flex-col items-center bg-(--background)">
        <div
          className={cn(
            "inline-block w-max rounded-full border-[3px] p-1",
            isStarted && "border-(--card-tag-bg)",
          )}
        >
          <Image
            src={img}
            alt={m["courses.lesson_modules.lesson_start_alt"]()}
            className="size-12 rounded-full bg-muted object-cover p-1 md:size-16"
          />
        </div>
        <div className="h-4 w-1 bg-gray-200" />
        <p
          className={cn(
            "w-full rounded-md border border-(--lesson-modules-border) bg-(--lesson-module-title) px-10 py-2 text-center text-sm sm:text-base",
          )}
        >
          {title.toUpperCase()}
        </p>
      </div>
      <div className="h-4 w-1 bg-gray-200" />
    </div>
  );
};
