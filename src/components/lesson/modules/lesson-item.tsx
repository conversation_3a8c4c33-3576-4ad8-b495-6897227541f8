import { useNavigate } from "@tanstack/react-router";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useCallback, useMemo } from "react";
import {
  ClockIcon,
  ContinueIcon,
  GameIcon,
  LessonStatusIcon,
  ReviseIcon,
  StartIcon,
  UnlockIcon,
} from "@/components/icons";
import { LoginFormDialog } from "@/components/login-form-dialog";
import { Button } from "@/components/ui/button";
import * as m from "@/paraglide/messages.js";
import { atomAuthHint } from "@/store/auth";
import { atomLessonUnlock } from "@/store/lesson";
import { User } from "@/types/auth";
import { Lesson, LessonStatus } from "@/types/lessons";
import { cn } from "@/utils/cn";

interface LessonItemProps {
  lesson: Lesson;
  currentLesson: Lesson | undefined;
  user: User | undefined;
}

const getLessonConfig = () => ({
  [LessonStatus.COMPLETED]: {
    text: m["courses.lesson_item.revise"](),
    Icon: ReviseIcon,
    iconColor: (isCurrent: boolean) => (isCurrent ? "#fff" : undefined),
  },
  [LessonStatus.CURRENT]: {
    text: m["courses.lesson_item.start"](),
    Icon: StartIcon,
    iconColor: (isCurrent: boolean) => (isCurrent ? "#fff" : undefined),
  },
  [LessonStatus.LOCKED]: {
    text: m["courses.lesson_item.unlock"](),
    Icon: UnlockIcon,
    iconColor: () => undefined,
  },
  [LessonStatus.IN_PROGRESS]: {
    text: m["courses.lesson_item.continue"](),
    Icon: ContinueIcon,
    iconColor: () => undefined,
  },
});

const getDisplayIndex = (index: number): string =>
  String(index + 1).padStart(2, "0");

const useLessonState = (lesson: Lesson, currentLesson: Lesson | undefined) => {
  const authHint = useAtomValue(atomAuthHint);

  return useMemo(() => {
    const status = lesson?.status || LessonStatus.LOCKED;
    const isCurrentRoute = currentLesson?.id === lesson?.id;
    const isProcessingLesson = status === LessonStatus.IN_PROGRESS;
    const shouldShowBtn = !(authHint && isCurrentRoute);

    return {
      status,
      isCurrentRoute,
      isProcessingLesson,
      shouldShowBtn,
    };
  }, [lesson, currentLesson, authHint]);
};

const useLessonStyles = (
  status: LessonStatus,
  isCurrentRoute: boolean,
  hasStatus: boolean,
  shouldShowBtn: boolean,
) => {
  const containerClasses = useMemo(
    () =>
      cn(
        "relative flex gap-x-6 border-t border-gray-200 px-4 py-4 sm:px-9 sm:py-6",
        {
          "bg-(--lesson-btn-bg)":
            isCurrentRoute && status === LessonStatus.IN_PROGRESS,
          "bg-(--lesson-btn-revise)":
            isCurrentRoute && status === LessonStatus.COMPLETED,
          "bg-[#F4F6FA]": !hasStatus,
          "!py-10": !shouldShowBtn,
        },
      ),
    [isCurrentRoute, status, hasStatus, shouldShowBtn],
  );

  const indexClasses = useMemo(
    () =>
      cn(
        "flex items-center justify-center rounded-md bg-muted p-3 text-(--lesson-index-color)",
        {
          "bg-(--lesson-completed-bg) text-secondary-foreground":
            status === LessonStatus.COMPLETED,
          "bg-(--lesson-btn-active) text-secondary-foreground":
            status === LessonStatus.IN_PROGRESS,
          "bg-primary text-secondary-foreground":
            status === LessonStatus.CURRENT,
        },
      ),
    [status],
  );

  const buttonClasses = useMemo(
    () =>
      cn(
        "h-full w-full flex-1 border-muted px-2 py-2 shadow-[0px_2px_0px_0px_#ECEEF5] text-xs md:px-3 rounded-[10px] font-medium flex items-end justify-center gap-1",
        {
          "border border-solid border-(--lesson-btn-border) bg-(--lesson-btn-active) hover:bg-(--lesson-btn-active)/80 text-primary-foreground shadow-[0px_2px_0px_0px_#FF9500]":
            status === LessonStatus.IN_PROGRESS,
          "bg-primary hover:bg-primary/80 text-secondary-foreground shadow-[0px_2px_0px_0px_#008527] border-[#008527]":
            status === LessonStatus.CURRENT ||
            (status === LessonStatus.COMPLETED && isCurrentRoute),
        },
      ),
    [status, isCurrentRoute],
  );

  return { containerClasses, indexClasses, buttonClasses };
};

const LessonIndex = ({
  ordinalIndex,
  status,
  indexClasses,
}: {
  ordinalIndex: number;
  status: LessonStatus;
  indexClasses: string;
}) => (
  <div className="relative h-max min-w-[1.5rem]">
    <div className={indexClasses}>
      <span className="font-bold text-sm">{getDisplayIndex(ordinalIndex)}</span>
    </div>
    <div className="-right-2 -bottom-2 absolute flex items-center justify-center rounded-full bg-(--card) p-[2px]">
      <LessonStatusIcon status={status} />
    </div>
  </div>
);

const LessonContent = ({ name }: { name: string }) => (
  <div className="flex gap-x-1.5 md:gap-x-2">
    <div className="flex flex-col gap-y-2">
      <p className="text-sm sm:text-base">{name}</p>
      <div className="flex items-center gap-2 text-xs sm:gap-3">
        <div className="flex items-center gap-[2px] sm:gap-2">
          <ClockIcon />
          <span>2 {m["courses.lesson_item.mins"]()}</span>
        </div>
        <div className="flex items-center gap-1 sm:gap-2">
          <GameIcon />
          <span>2 {m["courses.lesson_item.games"]()}</span>
        </div>
      </div>
    </div>
  </div>
);

export const LessonItem = ({
  lesson,
  currentLesson,
  user,
}: LessonItemProps) => {
  const navigate = useNavigate();
  const authHint = useAtomValue(atomAuthHint);
  const setLessonUnlock = useSetAtom(atomLessonUnlock);

  const { status, isCurrentRoute, isProcessingLesson, shouldShowBtn } =
    useLessonState(lesson, currentLesson);
  const { containerClasses, indexClasses, buttonClasses } = useLessonStyles(
    status,
    isCurrentRoute,
    !!lesson?.status,
    shouldShowBtn,
  );

  const handleClick = useCallback(() => {
    if (!lesson?.status || !user) return;

    const lessonRequiredKey = lesson?.requirements?.keys || 0;
    if (status !== LessonStatus.LOCKED || !lessonRequiredKey) {
      navigate({ to: `/learn/$courseSlug/${lesson.slug}` });
    } else {
      setLessonUnlock(lesson);
    }
  }, [status, navigate, lesson, user, setLessonUnlock]);

  if (!lesson) return null;

  const { text: buttonText, Icon, iconColor } = getLessonConfig()[status];
  const isCurrentProcessingLesson = isProcessingLesson && isCurrentRoute;

  return (
    <div className={containerClasses} id={lesson.slug}>
      {isCurrentProcessingLesson && (
        <div className="absolute top-1/2 right-0 h-3/4 w-[5px] translate-y-[-50%] rounded-l-xl bg-(--lesson-btn-active)" />
      )}

      <LessonIndex
        ordinalIndex={lesson.ordinal_index}
        status={status}
        indexClasses={indexClasses}
      />

      <div className="flex flex-1 flex-col justify-between gap-y-3 xs:gap-y-4">
        <LessonContent name={lesson.name} />

        {lesson.status &&
          shouldShowBtn &&
          (authHint ? (
            <Button
              variant="outline"
              size="lg"
              className={buttonClasses}
              onClick={handleClick}
              aria-label={`${buttonText} lesson: ${lesson.name}`}
              disabled={!lesson.status}
            >
              <Icon color={iconColor(isCurrentRoute)} />
              {buttonText.toUpperCase()}
            </Button>
          ) : (
            <LoginFormDialog>
              <Button
                variant="outline"
                size="lg"
                className={buttonClasses}
                aria-label={`${buttonText} lesson: ${lesson.name}`}
                disabled={!lesson.status}
              >
                <Icon color={iconColor(isCurrentRoute)} />
                {buttonText.toUpperCase()}
              </Button>
            </LoginFormDialog>
          ))}
      </div>
    </div>
  );
};
