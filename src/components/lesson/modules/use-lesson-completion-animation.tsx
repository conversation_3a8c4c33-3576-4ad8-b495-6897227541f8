import { useEffect, useState } from "react";
import * as m from "@/paraglide/messages.js";

type AnimationStep = "idle" | "game" | "challenge" | "lesson" | "completed";

interface UseLessonCompletionAnimationProps {
  hasGameSection: boolean;
  hasChallengeSection: boolean;
  gameXP: number;
  challengeXP: number;
  lessonXP: number;
}

export function useLessonCompletionAnimation({
  hasGameSection,
  hasChallengeSection,
  gameXP,
  challengeXP,
  lessonXP,
}: UseLessonCompletionAnimationProps) {
  const [currentStep, setCurrentStep] = useState<AnimationStep>("idle");
  const [cumulativeXP, setCumulativeXP] = useState(0);
  const [showCompletionText, setShowCompletionText] = useState("");
  const [showFinalContent, setShowFinalContent] = useState(false);

  useEffect(() => {
    const animationSequence = async () => {
      // Initial delay
      await new Promise((resolve) => setTimeout(resolve, 200));

      let currentTotal = 0;

      // Step 1: Game completion
      if (hasGameSection) {
        setCurrentStep("game");
        setShowCompletionText(m["courses.lesson_completion.game_completed"]());
        await new Promise((resolve) => setTimeout(resolve, 500));

        currentTotal += gameXP;
        setCumulativeXP(currentTotal);
        await new Promise((resolve) => setTimeout(resolve, 1500));

        setShowCompletionText("");
        await new Promise((resolve) => setTimeout(resolve, 300));
      }

      // Step 2: Challenge completion
      if (hasChallengeSection) {
        setCurrentStep("challenge");
        setShowCompletionText(
          m["courses.lesson_completion.challenge_completed"](),
        );
        await new Promise((resolve) => setTimeout(resolve, 500));

        currentTotal += challengeXP;
        setCumulativeXP(currentTotal);
        await new Promise((resolve) => setTimeout(resolve, 1500));

        setShowCompletionText("");
        await new Promise((resolve) => setTimeout(resolve, 300));
      }

      // Step 3: Lesson completion
      setCurrentStep("lesson");
      setShowCompletionText(m["courses.lesson_completion.lesson_completed"]());
      await new Promise((resolve) => setTimeout(resolve, 500));

      currentTotal += lessonXP;
      setCumulativeXP(currentTotal);
      await new Promise((resolve) => setTimeout(resolve, 1500));

      setShowCompletionText("");
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Step 5: Show final content
      setCurrentStep("completed");
      setShowFinalContent(true);
    };

    animationSequence();
  }, [hasGameSection, hasChallengeSection, gameXP, challengeXP, lessonXP]);

  return {
    currentStep,
    cumulativeXP,
    showCompletionText,
    showFinalContent,
  };
}
