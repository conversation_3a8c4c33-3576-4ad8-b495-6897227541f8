import { useQueryClient } from "@tanstack/react-query";
import { linkOptions, useNavigate } from "@tanstack/react-router";
import { AnimatePresence, motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { useSet<PERSON>tom } from "jotai/index";
import { LockIcon } from "lucide-react";
import { useEffect, useState } from "react";
import LessonCompletedImg from "@/assets/images/lesson-completed.png";
import { ArrowLineLeftIcon, ArrowLineRightIcon } from "@/components/icons";
import { useLessonCompletionAnimation } from "@/components/lesson/modules/use-lesson-completion-animation";
import LessonUnlock from "@/components/lesson-unlock";
import { Button } from "@/components/ui/button";
import { GradientText } from "@/components/ui/gradient-text";
import * as m from "@/paraglide/messages.js";
import { useProfile } from "@/services/auth";
import { courseProgressesQueryOptions } from "@/services/courses";
import { sectionsQueryOptions, startLesson } from "@/services/lessons";
import { atomAuth } from "@/store/auth";
import { atomLessonUnlock } from "@/store/lesson";
import { Course } from "@/types/courses";
import { Lesson, Section, SectionType } from "@/types/lessons";
import { useBadgePopup } from "@/utils/badges";

interface LessonCompletedProps {
  course: Course;
  lessons: Lesson[];
  lesson: Lesson;
  sections: Section[];
  countCompleted: number;
}

const gameXP = 10;
const challengeXP = 15;
const lessonXP = 10;

const AnimatedXPCounter = ({
  targetXP,
  duration = 1000,
}: {
  targetXP: number;
  duration?: number;
}) => {
  const [displayXP, setDisplayXP] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      const easeOut = 1 - Math.pow(1 - progress, 3);
      const currentXP = Math.floor(easeOut * targetXP);

      setDisplayXP(currentXP);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [targetXP, duration]);

  return (
    <motion.p
      className="relative text-center font-light text-5xl sm:text-6xl"
      animate={{
        scale: displayXP > 0 ? [1, 1.1, 1] : 1,
      }}
      transition={{
        duration: 0.25,
        ease: "easeOut",
      }}
    >
      {displayXP > 0 && "+"}
      {displayXP} {m["courses.lesson_completion.xp_suffix"]()}
    </motion.p>
  );
};

export const LessonCompleted = ({
  course,
  lesson,
  lessons,
  sections,
  countCompleted,
}: LessonCompletedProps) => {
  const nextLesson = lessons[countCompleted];
  const linkToCourse = linkOptions({
    to: "/courses/$courseSlug",
    params: { courseSlug: course.slug },
  });
  const linkToNext = linkOptions({
    to: "/learn/$courseSlug/$lessonSlug",
    params: { courseSlug: course.slug, lessonSlug: nextLesson.slug },
  });
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const auth = useAtomValue(atomAuth);
  const { data: user } = useProfile();
  const setLessonUnlock = useSetAtom(atomLessonUnlock);

  const nextLessonRequiredKey = nextLesson?.requirements?.keys || 0;
  const hasGameSection = sections.some(
    (section) => section.type === SectionType.GAME,
  );
  const hasChallengeSection = sections.some(
    (section) => section.type === SectionType.CHALLENGE,
  );
  const totalXP =
    (hasGameSection ? gameXP : 0) +
    (hasChallengeSection ? challengeXP : 0) +
    lessonXP;

  const { cumulativeXP, showCompletionText, showFinalContent } =
    useLessonCompletionAnimation({
      hasGameSection,
      hasChallengeSection,
      gameXP,
      challengeXP,
      lessonXP,
    });

  const { showPopup } = useBadgePopup();
  const handleProcessNavigateSection = async () => {
    await startLesson(nextLesson.id);
    queryClient.prefetchQuery(sectionsQueryOptions(nextLesson.id));
    queryClient.prefetchQuery(
      courseProgressesQueryOptions(course.id, auth?.jwt),
    );
    navigate(linkToNext);
  };

  const handleNextSection = async () => {
    if (!user) return;

    try {
      if (nextLessonRequiredKey > 0) {
        setLessonUnlock(nextLesson);
        return;
      }

      await handleProcessNavigateSection();
    } catch (e) {}
  };

  if (!user) return null;

  const onLessonCompleted = (node: HTMLDivElement) => {
    if (!node) return;
    showPopup();
  };

  return (
    <motion.div
      ref={onLessonCompleted}
      className="relative flex h-full w-full flex-col items-center justify-center gap-6 overflow-hidden bg-[linear-gradient(180deg,#FFFFFF_48.04%,#FFF4E266_100%)] px-3 md:gap-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      key={lesson.id}
      transition={{ duration: 0.3 }}
    >
      <div className="relative">
        <motion.img
          src={LessonCompletedImg}
          alt={m["courses.lesson_completion.lesson_completed_alt"]()}
          className="w-36 object-cover md:w-52"
        />
      </div>
      <div className="relative flex flex-col items-center justify-center gap-5">
        <GradientText className="!font-light text-3xl md:text-[42px]">
          {m["courses.lesson_completion.lesson_complete_title"]()}
        </GradientText>

        <AnimatedXPCounter targetXP={cumulativeXP} duration={800} />

        <AnimatePresence mode="wait">
          {showCompletionText && (
            <motion.div
              key={showCompletionText}
              initial={{ opacity: 0, y: 20, scale: 0.9 }}
              animate={{
                opacity: 1,
                y: 0,
                scale: 1,
              }}
              exit={{ opacity: 0, y: -20, scale: 0.9 }}
              transition={{
                duration: 0.4,
                ease: "backOut",
              }}
              className="absolute rounded-lg bg-(--lesson-btn-revise) px-9 py-2"
            >
              {showCompletionText}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Final Content Animation */}
        <div className="flex min-h-[200px] items-center justify-center">
          <AnimatePresence>
            {showFinalContent && (
              <motion.div
                initial={{ opacity: 0, y: 50, scale: 0.9 }}
                animate={{
                  opacity: 1,
                  y: 0,
                  scale: 1,
                }}
                transition={{
                  duration: 0.8,
                  ease: "backOut",
                  delay: 0.2,
                }}
                className="flex flex-col items-center gap-6"
              >
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="text-center text-gray-700"
                >
                  {m["courses.lesson_completion.great_job_message"]()}{" "}
                  <span className="font-semibold text-green-600">
                    {totalXP} {m["courses.lesson_completion.xp_suffix"]()}
                  </span>
                  .
                </motion.p>

                <motion.p
                  className="rounded-lg bg-(--lesson-btn-revise) px-9 py-2"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.7 }}
                >
                  <span className="font-semibold">
                    {countCompleted}/{lessons.length}
                  </span>{" "}
                  <span>
                    {m["courses.lesson_completion.lessons_in_course"]()}
                  </span>
                </motion.p>

                <motion.div
                  className="flex w-full flex-col gap-3"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1, duration: 0.4 }}
                >
                  <Button
                    size="xl"
                    className="w-full rounded-full font-normal text-base"
                    onClick={handleNextSection}
                  >
                    {!nextLessonRequiredKey ? (
                      <>
                        {m["courses.lesson_completion.next_lesson"]()}
                        <ArrowLineRightIcon />
                      </>
                    ) : (
                      <>
                        {m["courses.lesson_completion.unlock_next_lesson"]()}{" "}
                        <LockIcon />
                      </>
                    )}
                  </Button>

                  <Button
                    variant="outline"
                    size="xl"
                    className="w-full rounded-full bg-(--card) font-normal text-base"
                    onClick={() => navigate(linkToCourse)}
                  >
                    <ArrowLineLeftIcon />{" "}
                    {m["courses.lesson_completion.back_to_course"]()}
                  </Button>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
      <LessonUnlock />
    </motion.div>
  );
};
