import { ConnectorIcon } from "@/components/icons";
import { Image } from "@/components/ui/image";
import * as m from "@/paraglide/messages.js";
import { cn } from "@/utils/cn";

interface ModuleHeaderProps {
  img: string;
  title: string;
  isStarted?: boolean;
  isMobile?: boolean;
}

export const ModuleHeader = ({ img, title, isStarted }: ModuleHeaderProps) => {
  return (
    <div className="relative flex w-full flex-col items-center lg:w-1/2 lg:flex-row lg:items-end lg:pt-14">
      <div className="relative top-4 left-0 flex flex-col gap-y-2 bg-(--background) lg:absolute">
        <div
          className={cn(
            "inline-block rounded-full border-[3px] p-1",
            isStarted && "border-(--card-tag-bg)",
          )}
        >
          <Image
            src={img}
            alt={m["courses.lesson_modules.lesson_start_alt"]()}
            className="size-12 rounded-full bg-muted object-cover p-1 md:size-16"
          />
        </div>
        <p
          className={cn(
            "rounded-[4px] bg-muted p-2 text-center font-bold [font-size:var(--size-2xs)]",
          )}
        >
          {title.toUpperCase()}
        </p>
      </div>
      <ConnectorIcon type="start" />
    </div>
  );
};
