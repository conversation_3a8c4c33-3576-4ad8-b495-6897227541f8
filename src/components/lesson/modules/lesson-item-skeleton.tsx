import { Skeleton } from "@/components/ui/skeleton";

export const LessonItemSkeleton = () => {
  return (
    <div className="flex justify-between gap-x-1.5 rounded-[--size-2xs] p-[5px]">
      <div className="flex gap-x-1.5 md:gap-x-2">
        <div className="min-w-[1.5rem]">
          <Skeleton className="h-6 w-6 rounded-full" />
        </div>
        <div className="flex flex-col gap-y-1.5">
          <Skeleton className="h-4 w-[160px] md:w-[200px]" />
          <div className="flex items-center gap-2 text-xs sm:gap-3">
            <div className="flex items-center gap-[2px] sm:gap-2">
              <Skeleton className="h-3 w-3" />
              <Skeleton className="h-3 w-12" />
            </div>
            <div className="flex items-center gap-1 sm:gap-2">
              <Skeleton className="h-3 w-3" />
              <Skeleton className="h-3 w-12" />
            </div>
          </div>
        </div>
      </div>
      <Skeleton className="h-8 w-16 rounded-md" />
    </div>
  );
};
