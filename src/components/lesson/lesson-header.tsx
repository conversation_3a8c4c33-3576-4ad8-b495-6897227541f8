import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { useAtomValue } from "jotai";
import { XIcon } from "lucide-react";
import { memo, useCallback, useMemo } from "react";
import { NoteIcon } from "@/components/icons";
import {
  ProgressIcons,
  ProgressTracker,
} from "@/components/lesson/lesson-progress";
import { SaidbarTrigger } from "@/components/saidbar";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { UserXp } from "@/components/user/user-xp";
import { getLearningLessonProgresses } from "@/services/courses/getLearningCourseProgresses";
import { sectionsQueryOptions } from "@/services/lessons";
import { atomCurrentSection } from "@/store/chat";
import { atomSidebarCourseModule } from "@/store/sidebar";
import { Course } from "@/types/courses";
import { Lesson, Section, SectionType } from "@/types/lessons";

interface LessonHeaderProps {
  course: Course;
  lesson: Lesson;
}

export const LessonHeader = memo(({ course, lesson }: LessonHeaderProps) => {
  const { data: sections } = useQuery(sectionsQueryOptions(lesson.id));
  const milestones = useMemo(
    () => calculateMilestones(sections || []),
    [sections],
  );
  const currentSection = useAtomValue(atomCurrentSection);
  const navigate = useNavigate();
  const progress = useMemo(
    () =>
      getLearningLessonProgresses({
        lesson,
        sections,
        currentSection,
      }),
    [lesson, sections, currentSection],
  );

  const handleNavigateBack = useCallback(() => {
    navigate({
      to: "/courses/$courseSlug",
      params: { courseSlug: course.slug },
    });
  }, [navigate, course.slug]);

  return (
    <div className="sticky top-0 z-20 flex w-full items-center justify-center gap-3 bg-(--card) px-2 shadow-[0_4px_6px_0_rgba(216,216,216,0.25)] sm:px-7 md:justify-between lg:px-10">
      <div className="cursor-pointer md:block md:min-w-20">
        <Button size="icon" variant="ghost" onClick={handleNavigateBack}>
          <XIcon className="w-4 md:w-auto" />
        </Button>
      </div>

      <div className="block md:hidden">
        <SaidbarTrigger atom={atomSidebarCourseModule} />
      </div>

      <div className="hidden w-auto min-w-[60%] pt-10 sm:w-4/5 md:block md:w-[60%]">
        <ProgressTracker milestones={milestones} currentProgress={progress} />
      </div>

      <div className="block flex-1 md:hidden">
        <Progress value={progress} className="h-3" />
      </div>

      <div className="hidden md:block">
        <UserXp />
      </div>

      <div className="block cursor-pointer md:hidden">
        <NoteIcon className="size-7" />
      </div>
    </div>
  );
});

const calculateMilestones = (sections: Section[]) => {
  if (!sections || sections.length === 0) {
    return [];
  }

  const totalSections = sections.length;
  const lastSection = sections.at(-1);

  const milestones = [
    {
      label: "START",
      icon: <ProgressIcons.Lesson />,
      percentage: 0,
    },
  ];

  const gameIndices: number[] = [];
  let lastChallengeIndex = -1;

  sections.forEach((section, index) => {
    if (section.type === SectionType.GAME) {
      gameIndices.push(index);
    }
    if (section.type === SectionType.CHALLENGE) {
      lastChallengeIndex = index;
    }
  });

  gameIndices.forEach((gameIndex) => {
    const gamePercentage = Math.round(((gameIndex + 1) / totalSections) * 100);
    milestones.push({
      label: "",
      icon: <ProgressIcons.Game />,
      percentage: gamePercentage,
    });
  });

  if (lastChallengeIndex !== -1) {
    const challengePercentage = Math.round(
      ((lastChallengeIndex + 1) / totalSections) * 100,
    );
    milestones.push({
      label: "",
      icon: <ProgressIcons.Challenge />,
      percentage: challengePercentage,
    });
  }

  if (lastSection && lastSection.type === SectionType.TEXT) {
    milestones.push({
      label: "COMPLETE",
      icon: <ProgressIcons.Conclusion />,
      percentage: 100,
    });
  }

  return milestones.sort((a, b) => a.percentage - b.percentage);
};
