"use client";

import type React from "react";
import { useMemo } from "react";
import { cn } from "@/utils/cn";

interface Milestone {
  label: string;
  icon: React.ReactNode;
  percentage: number;
}

interface ProgressTrackerProps {
  milestones: Milestone[];
  currentProgress: number;
  className?: string;
}

export function ProgressTracker({
  milestones,
  currentProgress,
  className,
}: ProgressTrackerProps) {
  // Memoize calculations to prevent unnecessary recalculations on re-renders
  const { normalizedProgress, sortedMilestones, activeIndex } = useMemo(() => {
    // Ensure progress is between 0 and 100
    const normalized = Math.min(100, Math.max(0, currentProgress));

    // Sort milestones only once
    const sorted = [...milestones].sort((a, b) => a.percentage - b.percentage);

    // Find the current active milestone index
    const currentIndex = sorted.findIndex(
      (milestone) => milestone.percentage > normalized,
    );

    const active = currentIndex === -1 ? sorted.length - 1 : currentIndex - 1;

    return {
      normalizedProgress: normalized,
      sortedMilestones: sorted,
      activeIndex: active,
    };
  }, [milestones, currentProgress]);

  return (
    <div className={cn("relative w-full py-4", className)}>
      {/* Main progress bar - background */}
      <div className="-translate-y-1/2 absolute top-1/2 right-0 left-0 z-10 h-3 rounded-md bg-muted" />

      {/* Filled progress bar */}
      <div
        className="-translate-y-1/2 absolute top-1/2 left-0 z-10 h-3 rounded-md bg-green-500 transition-all duration-300"
        style={{ width: `${normalizedProgress}%` }}
      />

      {/* Milestone markers */}
      <div className="relative">
        {sortedMilestones.map((milestone, index) => {
          const isActive = index <= activeIndex;
          return (
            <MilestoneMarker
              key={`${milestone.label}-${milestone.percentage}`}
              milestone={milestone}
              isActive={isActive}
              isFirst={index === 0}
              isLast={index === sortedMilestones.length - 1}
            />
          );
        })}
      </div>
    </div>
  );
}

function MilestoneMarker({
  milestone,
  isActive,
  isLast,
  isFirst,
}: {
  milestone: Milestone;
  isActive: boolean;
  isLast: boolean;
  isFirst: boolean;
}) {
  const baseClasses =
    "absolute flex flex-col items-center px-2 py-1 rounded-sm";
  const activeClasses = isActive ? "bg-[#D4F5DD]" : "bg-muted";
  const textClasses = isActive ? "text-green-700" : "";

  return (
    <div
      className={cn(baseClasses, activeClasses)}
      style={{
        left: `${milestone.percentage - (isFirst ? 0.2 : 1.1)}%`,
        transform: isLast
          ? "translateX(-0.5rem) translateY(-2.5rem)"
          : "translateY(-2.5rem)",
      }}
    >
      <div className="z-20 flex h-3 items-center justify-center gap-1">
        {milestone.label && (
          <span
            className={cn(
              "hidden whitespace-nowrap font-bold [font-size:var(--size-2xs)] md:inline-block",
              textClasses,
            )}
          >
            {milestone.label}
          </span>
        )}
        {milestone.icon}
      </div>
      <div
        className={cn(
          "-bottom-4 -translate-x-1/2 absolute left-1/2 h-7 w-[5px]",
          isActive ? "bg-[#D4F5DD]" : "bg-muted",
          (isFirst || (isLast && milestone.label === "COMPLETE")) && "left-2",
        )}
      />
    </div>
  );
}

export const ProgressIcons = {
  Lesson: () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="15"
      height="13"
      fill="none"
      viewBox="0 0 15 13"
    >
      <path
        fill="#000"
        fillRule="evenodd"
        d="M7.584 1.069A7.38 7.38 0 0 0 1.41.605a.73.73 0 0 0-.487.676v7.286c0 .488.526.822 1.01.64a6.04 6.04 0 0 1 5.054.38l.756.426q.088.049.18.046a.35.35 0 0 0 .18-.046l.756-.425c1.53-.86 3.399-1 5.055-.38.483.181 1.009-.153 1.009-.64V1.28a.73.73 0 0 0-.488-.676 7.38 7.38 0 0 0-6.172.464l-.34.191zm.84 1.493c0-.259-.224-.468-.5-.468s-.5.21-.5.468V8.5c0 .259.224.469.5.469s.5-.21.5-.47z"
        clipRule="evenodd"
      ></path>
      <path
        fill="#000"
        d="M1.74 10.401a4.58 4.58 0 0 1 4.367 0l.725.397a2.29 2.29 0 0 0 2.184 0l.724-.397a4.58 4.58 0 0 1 4.367 0l.069.038c.238.13.319.417.18.64a.52.52 0 0 1-.684.17l-.069-.038a3.53 3.53 0 0 0-3.359 0l-.725.396a3.35 3.35 0 0 1-3.19 0l-.726-.396a3.53 3.53 0 0 0-3.359 0l-.068.037a.52.52 0 0 1-.684-.168.453.453 0 0 1 .18-.641z"
      ></path>
    </svg>
  ),
  Game: () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="12"
      fill="none"
      viewBox="0 0 16 12"
    >
      <path
        fill="#000"
        d="M11.768 7.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5"
      ></path>
      <path
        fill="#000"
        fillRule="evenodd"
        d="M8.768.75a.75.75 0 0 0-1.5 0v.75h-3.75a3 3 0 0 0-3 3V9a3 3 0 0 0 3 3h9a3 3 0 0 0 3-3V4.5a3 3 0 0 0-3-3h-3.75zm5.25 6a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0M4.268 4.5a.75.75 0 0 1 .75.75V6h.75a.75.75 0 0 1 0 1.5h-.75v.75a.75.75 0 0 1-1.5 0V7.5h-.75a.75.75 0 1 1 0-1.5h.75v-.75a.75.75 0 0 1 .75-.75"
        clipRule="evenodd"
      ></path>
    </svg>
  ),
  Challenge: () => (
    <svg
      width="9"
      height="13"
      viewBox="0 0 9 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.32034 0.791074C4.13932 0.711708 3.96103 0.76723 3.84634 0.884035C3.73493 0.997497 3.6792 1.17067 3.71827 1.34526C3.77994 1.62085 3.8125 1.90757 3.8125 2.20208C3.8125 2.87449 3.64303 3.34543 3.35144 3.73583C3.05502 4.1327 2.62593 4.45579 2.08416 4.82035C2.07565 4.82608 2.06772 4.83263 2.06048 4.83992L1.99487 4.90604C0.832471 5.65701 0.0625 6.96458 0.0625 8.45208C0.0625 10.782 1.9513 12.6708 4.28125 12.6708C6.6112 12.6708 8.5 10.782 8.5 8.45208C8.5 7.76799 8.33706 7.1215 8.04772 6.54973C8.02418 6.50322 7.979 6.47151 7.92725 6.46518C7.87551 6.45886 7.82402 6.47876 7.78997 6.51824C7.74591 6.56934 7.70275 6.61976 7.66023 6.66945C7.1113 7.31082 6.66831 7.8284 5.7601 8.0596C5.72148 8.06943 5.69748 8.06212 5.68046 8.05149C5.66042 8.03897 5.6385 8.01398 5.62348 7.97405C5.59217 7.89085 5.60561 7.78474 5.66975 7.7152C6.15549 7.18857 6.50056 6.4737 6.62386 5.45676C6.85484 3.55182 5.98905 1.5227 4.32034 0.791074Z"
        fill="black"
      />
    </svg>
  ),
  Conclusion: () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="none"
      viewBox="0 0 16 16"
    >
      <path
        fill="#161616"
        d="M13.147 3.781h-.938v-.469a.94.94 0 0 0-.937-.937H5.645a.94.94 0 0 0-.937.938v.468h-.938a.94.94 0 0 0-.937.938v1.406A1.877 1.877 0 0 0 4.709 8h.15a3.83 3.83 0 0 0 3.131 2.782v1.906H5.646v.937h5.626v-.937H8.928V10.78A3.73 3.73 0 0 0 12.088 8h.121a1.877 1.877 0 0 0 1.875-1.875V4.719a.94.94 0 0 0-.937-.938M4.708 7.063a.94.94 0 0 1-.938-.938V4.719h.938zm8.438-.938a.94.94 0 0 1-.938.938V4.719h.938z"
      ></path>
    </svg>
  ),
};
