import { atom, PrimitiveAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { PanelLeftIcon } from "lucide-react";
import { ReactElement, useCallback } from "react";
import * as m from "@/paraglide/messages.js";
import { PropsWithClassName } from "@/types/app";
import { cn } from "@/utils/cn";
import { useIsMobile } from "@/utils/use-mobile";
import { Button } from "./ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "./ui/sheet";
import { SIDEBAR_WIDTH_MOBILE } from "./ui/sidebar";

export const atomSidebarDefaultPrimitive = atom<{
  open: boolean;
  openMobile: boolean;
}>({
  open: false,
  openMobile: false,
});

export function useSaidbar<
  T extends {
    open: boolean;
    openMobile: boolean;
  },
>(atom: PrimitiveAtom<T>) {
  const { open, openMobile } = useAtomValue(atom);
  const set = useSetAtom(atom);
  const setOpen = (v: boolean | ((prev: boolean) => boolean)) => {
    if (typeof v === "boolean") {
      return set((prev) => {
        return {
          ...prev,
          open: v,
        };
      });
    }

    return set((prev) => {
      return {
        ...prev,
        open: v(prev.open),
      };
    });
  };

  const setOpenMobile = (v: boolean | ((prev: boolean) => boolean)) => {
    if (typeof v === "boolean") {
      return set((prev) => {
        return {
          ...prev,
          openMobile: v,
        };
      });
    }

    return set((prev) => {
      return {
        ...prev,
        openMobile: v(prev.openMobile),
      };
    });
  };

  const isMobile = useIsMobile();

  const state = open ? "expanded" : "collapsed";

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const toggleSidebar = useCallback(() => {
    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);
  }, [isMobile]);

  return { open, openMobile, setOpen, setOpenMobile, state, toggleSidebar };
}

export function Saidbar<
  T extends {
    open: boolean;
    openMobile: boolean;
  },
>({
  side = "left",
  variant = "sidebar",
  collapsible = "offcanvas",
  className,
  children,
  atom,
  classNameInner,
  ...props
}: React.ComponentProps<"div"> & {
  side?: "left" | "right";
  variant?: "sidebar" | "floating" | "inset";
  collapsible?: "offcanvas" | "icon" | "none";
  classNameInner?: string;
  atom: PrimitiveAtom<T>;
}) {
  const isMobile = useIsMobile();
  const { openMobile, setOpenMobile, state } = useSaidbar(atom);

  if (collapsible === "none") {
    return (
      <div
        data-slot="sidebar"
        className={cn(
          "flex h-full w-(--sidebar-width) flex-col bg-sidebar text-sidebar-foreground",
          className,
        )}
        {...props}
      >
        {children}
      </div>
    );
  }

  if (isMobile) {
    return (
      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>
        <SheetContent
          data-sidebar="sidebar"
          data-slot="sidebar"
          data-mobile="true"
          className={cn(
            "w-(--sidebar-width) bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",
            className,
          )}
          style={
            {
              "--sidebar-width": SIDEBAR_WIDTH_MOBILE,
            } as React.CSSProperties
          }
          side={side}
        >
          <SheetHeader className="sr-only">
            <SheetTitle>{m["common.navigation.sidebar"]()}</SheetTitle>
            <SheetDescription>
              {m["common.navigation.sidebar_description"]()}
            </SheetDescription>
          </SheetHeader>
          <div className="flex h-full w-full flex-col">{children}</div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <div
      className="group peer hidden text-sidebar-foreground md:block"
      data-state={state}
      data-collapsible={state === "collapsed" ? collapsible : ""}
      data-variant={variant}
      data-side={side}
      data-slot="sidebar"
    >
      {/* This is what handles the sidebar gap on desktop */}
      <div
        data-slot="sidebar-gap"
        className={cn(
          "relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear",
          "group-data-[collapsible=offcanvas]:w-0",
          "group-data-[side=right]:rotate-180",
          variant === "floating" || variant === "inset"
            ? "group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]"
            : "group-data-[collapsible=icon]:w-(--sidebar-width-icon)",
        )}
      />
      <div
        data-slot="sidebar-container"
        className={cn(
          "fixed z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",
          side === "left"
            ? "left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]"
            : "right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",
          // Adjust the padding for floating and inset variants.
          variant === "floating" || variant === "inset"
            ? "p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]"
            : "group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",
          className,
        )}
        {...props}
      >
        <div
          data-sidebar="sidebar"
          data-slot="sidebar-inner"
          className={cn(
            "flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow-sm",
            classNameInner,
          )}
        >
          {children}
        </div>
      </div>
    </div>
  );
}

export function SaidbarTrigger<
  T extends {
    open: boolean;
    openMobile: boolean;
  },
>({
  className,
  onClick,
  atom,
  icon = <PanelLeftIcon />,
  ...props
}: React.ComponentProps<typeof Button> & {
  atom: PrimitiveAtom<T>;
  icon?: ReactElement<PropsWithClassName>;
}) {
  const { toggleSidebar } = useSaidbar(atom);

  return (
    <Button
      data-sidebar="trigger"
      data-slot="sidebar-trigger"
      variant="ghost"
      size="icon"
      className={cn("size-7", className)}
      onClick={(event) => {
        onClick?.(event);
        toggleSidebar();
      }}
      {...props}
    >
      {icon}
      <span className="sr-only">{m["common.actions.toggle_sidebar"]()}</span>
    </Button>
  );
}

export function SaidbarContent({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="sidebar-content"
      data-sidebar="content"
      className={cn(
        "flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",
        className,
      )}
      {...props}
    />
  );
}
