import { AnimatePresence, motion } from "framer-motion";
import { Check } from "lucide-react";
import { GameExplanation } from "@/components/games/game-explain";
import {
  CorrectAlert,
  IncorrectAlert,
} from "@/components/games/quiz/QuizControl";
import { ArrowLineLeftIcon, ResetIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import * as m from "@/paraglide/messages.js";
import { GameConfig, SectionType } from "@/types/lessons";
import { cn } from "@/utils/cn";

interface GameControlProps {
  showResults: boolean;
  isAllCorrect: boolean;
  checkAnswer: () => void;
  resetGame: () => void;
  nextSection: () => void;
  backSection?: () => void;
  gameConfig: GameConfig;
  canAnswer: boolean;
  canReset: boolean;
  isRevise: boolean;
}

export const GameControl = ({
  gameConfig,
  nextSection,
  resetGame,
  isAllCorrect,
  showResults,
  checkAnswer,
  canAnswer,
  canReset,
  isRevise,
  backSection,
}: GameControlProps) => {
  return (
    <motion.div
      className="mx-auto mt-5 mb-10 flex items-center justify-center"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.7 }}
    >
      {isRevise && (
        <div className="flex items-center gap-x-2">
          {backSection && (
            <Button
              variant="outline"
              size="lg"
              className="md:!px-16 rounded-full px-10"
              onClick={backSection}
            >
              <ArrowLineLeftIcon />
              {m["common.actions.back"]()}
            </Button>
          )}
          <Button
            size="lg"
            className="md:!px-16 rounded-full px-10"
            onClick={nextSection}
          >
            <span className="text-base md:text-lg">
              {m["common.actions.continue"]()}
            </span>
            <Check className="size-4" />
          </Button>
        </div>
      )}
      {!showResults ? (
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center gap-x-2">
            {backSection && (
              <Button
                variant="outline"
                size="lg"
                className="md:!px-16 rounded-full px-10"
                onClick={backSection}
              >
                <ArrowLineLeftIcon />
                {m["common.actions.back"]()}
              </Button>
            )}
            <Button
              disabled={!canAnswer}
              size="lg"
              className="md:!px-16 rounded-full px-10"
              onClick={checkAnswer}
            >
              <span className="text-base md:text-lg">
                {m["common.actions.check"]()}
              </span>
              <Check className="size-4" />
            </Button>
          </div>
          <div
            className={`flex items-center gap-x-2 ${!canReset ? "cursor-auto opacity-50" : "cursor-pointer"}`}
            onClick={resetGame}
          >
            <ResetIcon />
            {m["common.actions.reset"]()}
          </div>
        </div>
      ) : (
        <AnimatePresence mode="wait">
          {!isAllCorrect ? (
            <div className="w-full">
              <IncorrectAlert onRetryQuestion={() => resetGame()} />
            </div>
          ) : (
            <div className="flex w-full flex-col">
              {!isRevise && (
                <CorrectAlert
                  isRevise={isRevise}
                  position={undefined}
                  type={SectionType.GAME}
                  isFinishAnswer={false}
                  onNextQuestion={() => {
                    nextSection();
                  }}
                />
              )}
              {gameConfig?.parameters?.explanation?.trim() && (
                <div className={cn(isRevise ? "" : "mt-8")}>
                  <GameExplanation
                    explanation={gameConfig.parameters.explanation}
                  />
                </div>
              )}{" "}
            </div>
          )}
        </AnimatePresence>
      )}
    </motion.div>
  );
};
