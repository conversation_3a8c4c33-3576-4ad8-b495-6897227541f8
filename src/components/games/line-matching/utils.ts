import {
  Connection,
  DragState,
  MatchItem,
} from "@/components/games/line-matching/types";
import { GameConfig } from "@/types/lessons";

export const getNextColor = (
  colors: { color: string; border: string; shadow: string }[],
  colorIndex: React.MutableRefObject<number>,
) => {
  return colors[colorIndex.current % colors.length];
};

export const canConnect = (
  fromId: string,
  toId: string,
  leftItems: MatchItem[],
  rightItems: MatchItem[],
  connections: Connection[],
): boolean => {
  // Can't connect to self
  if (fromId === toId) return false;

  const fromItem = [...leftItems, ...rightItems].find(
    (item) => item.id === fromId,
  );
  const toItem = [...leftItems, ...rightItems].find((item) => item.id === toId);

  // Must exist and be from different columns
  if (!fromItem || !toItem || fromItem.column === toItem.column) return false;

  // Check if either item is already connected
  const hasExistingConnection = connections.some(
    (conn) =>
      conn.fromId === fromId ||
      conn.toId === fromId ||
      conn.fromId === toId ||
      conn.toId === toId,
  );

  return !hasExistingConnection;
};

export const getConnectionPath = (
  fromId: string,
  toId: string,
  itemPositions: Record<string, DOMRect>,
  containerRef: React.RefObject<HTMLDivElement | null>,
  leftItems: MatchItem[],
  rightItems: MatchItem[],
  isMobile?: boolean,
) => {
  const fromRect = itemPositions[fromId];
  const toRect = itemPositions[toId];
  const containerRect = containerRef.current?.getBoundingClientRect();

  if (!fromRect || !toRect || !containerRect) return "";

  // Determine which item is on the left and which is on the right
  const fromItem = [...leftItems, ...rightItems].find(
    (item) => item.id === fromId,
  );
  const toItem = [...leftItems, ...rightItems].find((item) => item.id === toId);

  let startX: number, startY: number, endX: number, endY: number;

  if (fromItem?.column === "left") {
    // Connect from right edge of left item to left edge of right item
    startX = fromRect.right - containerRect.left + 5;
    startY = fromRect.top + fromRect.height / 2 - containerRect.top;
    endX = toRect.left - containerRect.left + 5;
    endY = toRect.top + toRect.height / 2 - containerRect.top;
  } else {
    // Connect from left edge of right item to right edge of left item
    startX = fromRect.left - containerRect.left;
    startY = fromRect.top + fromRect.height / 2 - containerRect.top;
    endX = toRect.right - containerRect.left;
    endY = toRect.top + toRect.height / 2 - containerRect.top;
  }

  const horizontalDistance = Math.abs(endX - startX);

  const controlPointOffset = Math.max(
    horizontalDistance * (isMobile ? 0.1 : 0.3),
    isMobile ? 10 : 30,
  );

  const controlPoint1X =
    startX +
    (fromItem?.column === "left" ? controlPointOffset : -controlPointOffset);
  const controlPoint1Y = startY;
  const controlPoint2X =
    endX +
    (toItem?.column === "left" ? controlPointOffset : -controlPointOffset);
  const controlPoint2Y = endY;

  return `M ${startX} ${startY} C ${controlPoint1X} ${controlPoint1Y}, ${controlPoint2X} ${controlPoint2Y}, ${endX} ${endY}`;
};

export const getDragPath = (
  dragState: DragState,
  itemPositions: Record<string, DOMRect>,
  containerRef: React.RefObject<HTMLDivElement | null>,
  leftItems: MatchItem[],
  rightItems: MatchItem[],
) => {
  if (!dragState.isDragging || !dragState.fromId) return "";

  const fromRect = itemPositions[dragState.fromId];
  const containerRect = containerRef.current?.getBoundingClientRect();

  if (!fromRect || !containerRect) return "";

  const fromItem = [...leftItems, ...rightItems].find(
    (item) => item.id === dragState.fromId,
  );

  let startX: number, startY: number;

  if (fromItem?.column === "left") {
    startX = fromRect.right - containerRect.left + 10;
    startY = fromRect.top + fromRect.height / 2 - containerRect.top;
  } else {
    startX = fromRect.left - containerRect.left + 10;
    startY = fromRect.top + fromRect.height / 2 - containerRect.top;
  }

  let endX: number, endY: number;

  if (dragState.hoveredTargetId) {
    const targetRect = itemPositions[dragState.hoveredTargetId];
    if (targetRect) {
      const targetItem = [...leftItems, ...rightItems].find(
        (item) => item.id === dragState.hoveredTargetId,
      );

      if (targetItem?.column === "left") {
        endX = targetRect.right - containerRect.left;
        endY = targetRect.top + targetRect.height / 2 - containerRect.top;
      } else {
        endX = targetRect.left - containerRect.left;
        endY = targetRect.top + targetRect.height / 2 - containerRect.top;
      }
    } else {
      endX = dragState.mousePosition.x;
      endY = dragState.mousePosition.y;
    }
  } else {
    // Not hovering over a target, use mouse position
    endX = dragState.mousePosition.x;
    endY = dragState.mousePosition.y;
  }

  const horizontalDistance = Math.abs(endX - startX);
  const controlPointOffset = Math.max(horizontalDistance * 0.3, 40);

  const controlPoint1X =
    startX +
    (fromItem?.column === "left" ? controlPointOffset : -controlPointOffset);
  const controlPoint1Y = startY;
  const controlPoint2X =
    endX +
    (dragState.hoveredTargetId
      ? leftItems
          .concat(rightItems)
          .find((item) => item.id === dragState.hoveredTargetId)?.column ===
        "left"
        ? controlPointOffset
        : -controlPointOffset
      : -controlPointOffset);
  const controlPoint2Y = endY;

  return `M ${startX} ${startY} C ${controlPoint1X} ${controlPoint1Y}, ${controlPoint2X} ${controlPoint2Y}, ${endX} ${endY}`;
};

// Helper function to force position updates with better timing
export const forceUpdatePositions = (
  leftItems: MatchItem[],
  rightItems: MatchItem[],
): Record<string, DOMRect> => {
  const positions: Record<string, DOMRect> = {};
  const allItems = [...leftItems, ...rightItems];

  allItems.forEach((item) => {
    const element = document.getElementById(`item-${item.id}`);
    if (element) {
      // Force reflow to get accurate positions
      element.offsetHeight;
      positions[item.id] = element.getBoundingClientRect();
    }
  });

  return positions;
};

export const getItemStyle = (
  itemId: string,
  dragState: DragState,
  selectedItemId: string | null,
  connections: Connection[],
  colors: { color: string; border: string; shadow: string }[],
  colorIndex: React.MutableRefObject<number>,
  isItemConnected: (itemId: string) => boolean,
  getItemConnection: (itemId: string) => Connection | null,
  canConnect: (
    fromId: string,
    toId: string,
    leftItems: MatchItem[],
    rightItems: MatchItem[],
    connections: Connection[],
  ) => boolean,
  leftItems: MatchItem[],
  rightItems: MatchItem[],
) => {
  const isConnected = isItemConnected(itemId);
  const isHoveredTarget = dragState.hoveredTargetId === itemId;
  const isDragSource = dragState.fromId === itemId;
  const isSelected = selectedItemId === itemId;
  const connectionColor = getItemConnection(itemId);
  const nextColor = getNextColor(colors, colorIndex);
  const defaultStyle = {
    borderColor: "#E6E8F0",
    backgroundColor: "#FFFFFF",
    boxShadow: "0px 5px 0px 0px #E6E8F0",
    dotColor: "#fff",
  };

  const getConnectedStyle = (nextColor: (typeof colors)[0]) => ({
    borderColor: nextColor.color,
    boxShadow: nextColor.shadow,
    dotColor: nextColor.color,
  });

  if (isConnected && connectionColor) {
    return getConnectedStyle(connectionColor);
  }

  if (isSelected) {
    return getConnectedStyle(connectionColor || nextColor);
  }

  if (isHoveredTarget && dragState.fromId) {
    if (
      canConnect(dragState.fromId, itemId, leftItems, rightItems, connections)
    ) {
      return getConnectedStyle(connectionColor || nextColor);
    }
  }

  if (isDragSource) {
    return getConnectedStyle(connectionColor || nextColor);
  }

  return defaultStyle;
};

export function convertToMatchItems(
  config: GameConfig,
  column: "left" | "right",
): MatchItem[] {
  const matchItems: MatchItem[] = [];

  config.parameters.categories.forEach((category) => {
    // Left item: category label
    const leftItem: MatchItem = {
      id: `${category.label}`,
      text: category.label,
      column: "left",
    };
    matchItems.push(leftItem);

    // Right item: first item from items array
    if (category.items.length > 0) {
      const rightItem: MatchItem = {
        id: `${category.items[0]}`,
        text: category.items[0],
        column: "right",
      };
      matchItems.push(rightItem);
    }
  });

  return matchItems
    .filter((item) => item.column === column)
    .sort(() => Math.random() - 0.5);
}
