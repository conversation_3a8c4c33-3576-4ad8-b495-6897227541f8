import { AnimatePresence, motion } from "framer-motion";
import { useSetAtom } from "jotai/index";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { GameControl } from "@/components/games/game-control";
import { Connection, DragState } from "@/components/games/line-matching/types";
import { atomCurrentGameUserAnswer } from "@/store/chat";
import { GameConfig, GameSectionCategory } from "@/types/lessons";
import { colors } from "./constants";
import {
  canConnect,
  convertToMatchItems,
  forceUpdatePositions,
  getConnectionPath,
  getDragPath,
  getItemStyle,
  getNextColor,
} from "./utils";

interface LineMatchingGameProps {
  nextSection: () => void;
  backSection?: () => void;
  gameConfig: GameConfig;
  isAnswered: boolean;
}

function LineMatchingGame({
  gameConfig,
  nextSection,
  isAnswered,
  backSection,
}: LineMatchingGameProps) {
  const [connections, setConnections] = useState<Connection[]>([]);
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    fromId: null,
    mousePosition: { x: 0, y: 0 },
    hoveredTargetId: null,
  });
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [itemPositions, setItemPositions] = useState<Record<string, DOMRect>>(
    {},
  );
  const [isMobile, setIsMobile] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const colorIndex = useRef(0);
  const [clickTimeout, setClickTimeout] = useState<NodeJS.Timeout | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessingClick, setIsProcessingClick] = useState(false);
  const leftItems = useMemo(
    () => convertToMatchItems(gameConfig, "left"),
    [gameConfig],
  );
  const rightItems = useMemo(
    () => convertToMatchItems(gameConfig, "right"),
    [gameConfig],
  );
  const [showResults, setShowResults] = useState(false);
  const [isAllCorrect, setIsAllCorrect] = useState(false);

  const setCurrentGameUserAnswer = useSetAtom(atomCurrentGameUserAnswer);

  useEffect(() => {
    const result: GameSectionCategory[] = [];

    const connectionMap = new Map<string, string[]>();

    connections.forEach((connection) => {
      const leftItem = leftItems.find((item) => item.id === connection.fromId);
      const rightItem = rightItems.find((item) => item.id === connection.toId);

      if (leftItem && rightItem) {
        if (!connectionMap.has(leftItem.text)) {
          connectionMap.set(leftItem.text, []);
        }
        connectionMap.get(leftItem.text)?.push(rightItem.text);
      }
    });

    connectionMap.forEach((connectedItems, leftItemText) => {
      result.push({
        label: leftItemText,
        items: connectedItems,
      });
    });

    setCurrentGameUserAnswer(result);
  }, [connections, leftItems, rightItems, setCurrentGameUserAnswer]);

  // Initialize correct connections if already answered
  useEffect(() => {
    if (!isAnswered || connections.length > 0) return;

    const correctConnections: Connection[] =
      gameConfig.parameters.categories.map((category, index) => ({
        fromId: category.label,
        toId: category.items[0],
        ...getNextColor(colors, { current: index }),
      }));

    setConnections(correctConnections);
    setShowResults(true);
    setIsAllCorrect(true);
    colorIndex.current = correctConnections.length;
  }, [isAnswered, gameConfig.parameters.categories, connections.length]);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || "ontouchstart" in window);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const updateItemPositions = useCallback(() => {
    const newPositions = forceUpdatePositions(leftItems, rightItems);
    setItemPositions(newPositions);
  }, [leftItems, rightItems]);

  useEffect(() => {
    const updateItemPositionsInterval = setInterval(updateItemPositions, 500);
    return () => clearInterval(updateItemPositionsInterval);
  }, [updateItemPositions]);

  const createConnection = (fromId: string, toId: string) => {
    if (showResults) {
      setShowResults(false);
    }
    // Prevent interaction if already answered
    if (isAnswered || isProcessingClick) return false;

    if (canConnect(fromId, toId, leftItems, rightItems, connections)) {
      setIsProcessingClick(true);

      const fromItem = [...leftItems, ...rightItems].find(
        (item) => item.id === fromId,
      );
      const toItem = [...leftItems, ...rightItems].find(
        (item) => item.id === toId,
      );

      let actualFromId = fromId;
      let actualToId = toId;

      // Swap if needed to ensure left->right direction
      if (fromItem?.column === "right" && toItem?.column === "left") {
        actualFromId = toId;
        actualToId = fromId;
      }

      const newConnection: Connection = {
        fromId: actualFromId,
        toId: actualToId,
        ...getNextColor(colors, colorIndex),
      };
      colorIndex.current++;

      setConnections((prev) => {
        const hasExisting = prev.some(
          (conn) =>
            conn.fromId === actualFromId ||
            conn.toId === actualFromId ||
            conn.fromId === actualToId ||
            conn.toId === actualToId,
        );

        if (hasExisting) {
          setIsProcessingClick(false);
          return prev;
        }

        setTimeout(() => setIsProcessingClick(false), 100);
        return [...prev, newConnection];
      });

      updateItemPositions();
      return true;
    }
    return false;
  };

  const checkAnswer = () => {
    setShowResults(true);

    const userPairs = new Set(connections.map((c) => `${c.fromId}->${c.toId}`));
    const answerPairs = new Set(
      gameConfig.parameters.categories.map(
        (category) => `${category.label}->${category.items[0]}`,
      ),
    );

    if (answerPairs.size !== userPairs.size) {
      setIsAllCorrect(false);
      return false;
    }

    for (const pair of answerPairs) {
      if (!userPairs.has(pair)) {
        setIsAllCorrect(false);
        return false;
      }
    }

    setIsAllCorrect(true);
    return true;
  };

  // Remove a connection involving the given item
  const removeConnection = (itemId: string) => {
    // Prevent interaction if already answered
    if (isAnswered || isProcessingClick) return false;
    setShowResults(false);
    const existingConnection = connections.find(
      (conn) => conn.fromId === itemId || conn.toId === itemId,
    );
    if (existingConnection) {
      setIsProcessingClick(true);
      setConnections((prev) =>
        prev.filter((conn) => conn !== existingConnection),
      );
      setTimeout(() => setIsProcessingClick(false), 100);
      return true;
    }
    return false;
  };

  const handleItemClick = (
    itemId: string,
    event: React.MouseEvent | React.TouchEvent,
  ) => {
    event.preventDefault();
    event.stopPropagation();

    // Prevent interaction if already answered
    if (isAnswered) return;

    // Prevent processing if already processing or dragging
    if (isProcessingClick || isDragging) return;

    // Check if this item is already connected - if so, remove the connection
    if (removeConnection(itemId)) {
      setSelectedItemId(null);
      return;
    }

    // If no item is selected yet, select this one
    if (!selectedItemId) {
      setSelectedItemId(itemId);
      return;
    }

    // If an item is already selected, try to create a connection
    if (selectedItemId && selectedItemId !== itemId) {
      createConnection(selectedItemId, itemId);
      setSelectedItemId(null); // Clear selection whether successful or not
      return;
    }

    // If the same item is clicked again, deselect it
    if (selectedItemId === itemId) {
      setSelectedItemId(null);
    }
  };

  const handleMouseDown = (itemId: string, event: React.MouseEvent) => {
    // Prevent interaction if already answered
    if (isAnswered) return;

    // Skip drag functionality on mobile or if processing
    if (isMobile || isProcessingClick) return;

    event.preventDefault();
    event.stopPropagation();

    // Clear any existing timeout
    if (clickTimeout) {
      clearTimeout(clickTimeout);
      setClickTimeout(null);
    }

    // Set a timeout to distinguish between click and drag
    const timeout = setTimeout(() => {
      // This is a drag operation
      setIsDragging(true);
      const existingConnection = connections.find(
        (conn) => conn.fromId === itemId || conn.toId === itemId,
      );

      if (!existingConnection) {
        setDragState({
          isDragging: true,
          fromId: itemId,
          mousePosition: { x: 0, y: 0 },
          hoveredTargetId: null,
        });
      }
    }, 150); // 150ms delay to distinguish click from drag

    setClickTimeout(timeout);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (
      isAnswered ||
      isMobile ||
      !dragState.isDragging ||
      !containerRef.current
    )
      return;

    const rect = containerRef.current.getBoundingClientRect();
    setDragState((prev) => ({
      ...prev,
      mousePosition: {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      },
    }));
  };

  const handleMouseUp = (targetId?: string) => {
    if (isAnswered || isMobile) return;

    // Clear the timeout
    if (clickTimeout) {
      clearTimeout(clickTimeout);
      setClickTimeout(null);
    }

    if (
      dragState.isDragging &&
      dragState.fromId &&
      targetId &&
      dragState.fromId !== targetId
    ) {
      createConnection(dragState.fromId, targetId);
    }

    setDragState({
      isDragging: false,
      fromId: null,
      mousePosition: { x: 0, y: 0 },
      hoveredTargetId: null,
    });

    // Reset dragging state after a short delay to allow click events to process
    setTimeout(() => {
      setIsDragging(false);
    }, 50);
  };

  const handleContainerClick = (event: React.MouseEvent) => {
    // Prevent interaction if already answered
    if (isAnswered) return;

    // Only clear selection if clicking on the container itself, not on items
    if (event.target === event.currentTarget) {
      setSelectedItemId(null);
    }
  };

  const handleItemMouseEnter = (itemId: string) => {
    if (isAnswered || isMobile || !dragState.isDragging || !dragState.fromId)
      return;

    const fromItem = [...leftItems, ...rightItems].find(
      (item) => item.id === dragState.fromId,
    );
    const toItem = [...leftItems, ...rightItems].find(
      (item) => item.id === itemId,
    );

    // Only highlight if it's a valid target
    if (
      fromItem &&
      toItem &&
      fromItem.column !== toItem.column &&
      dragState.fromId !== itemId
    ) {
      const hasExistingConnection = connections.some(
        (conn) =>
          conn.fromId === dragState.fromId ||
          conn.toId === dragState.fromId ||
          conn.fromId === itemId ||
          conn.toId === itemId,
      );

      if (!hasExistingConnection) {
        setDragState((prev) => ({
          ...prev,
          hoveredTargetId: itemId,
        }));
      }
    }
  };

  const handleItemMouseLeave = () => {
    if (isAnswered || isMobile || !dragState.isDragging) return;

    setDragState((prev) => ({
      ...prev,
      hoveredTargetId: null,
    }));
  };

  const isItemConnected = (itemId: string) => {
    return connections.some(
      (conn) => conn.fromId === itemId || conn.toId === itemId,
    );
  };

  const getItemConnection = (itemId: string) => {
    const connection = connections.find(
      (conn) => conn.fromId === itemId || conn.toId === itemId,
    );
    return connection ? connection : null;
  };

  const resetGame = () => {
    // Prevent reset if already answered
    if (isAnswered) return;

    // Clear all timeouts and states
    if (clickTimeout) {
      clearTimeout(clickTimeout);
      setClickTimeout(null);
    }

    setShowResults(false);
    setIsAllCorrect(false);
    setConnections([]);
    setSelectedItemId(null);
    setDragState({
      isDragging: false,
      fromId: null,
      mousePosition: { x: 0, y: 0 },
      hoveredTargetId: null,
    });
    setIsDragging(false);
    setIsProcessingClick(false);
    colorIndex.current = 0;
    window.scrollTo(0, 0);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (clickTimeout) {
        clearTimeout(clickTimeout);
      }
    };
  }, [clickTimeout]);

  return (
    <div className="@container mx-auto max-w-6xl">
      <div className="sm:!mt-8 mx-auto mt-2 w-full overflow-hidden rounded-(--size-2xs) border-3 border-[#E6E8F0]">
        <div className="@container mx-auto max-w-6xl">
          <motion.div
            className="mt-8 mb-6 flex justify-center text-center sm:mb-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.3 }}
          >
            <p className="text-lg md:max-w-[60%] md:text-xl">
              {gameConfig.description}
            </p>
          </motion.div>

          <div
            ref={containerRef}
            className={`relative m-4 rounded-lg bg-[#F4F6FA] p-2 sm:m-8 sm:p-4 md:p-8 ${
              isAnswered ? "pointer-events-none" : ""
            }`}
            onMouseMove={handleMouseMove}
            onMouseUp={() => handleMouseUp()}
            onClick={handleContainerClick}
            style={{ touchAction: "manipulation" }}
          >
            <div className="grid grid-cols-2 gap-5 sm:gap-7 md:gap-8 lg:gap-16">
              <div>
                <div className="space-y-2 sm:space-y-3 md:space-y-4">
                  {leftItems.map((item) => {
                    const style = getItemStyle(
                      item.id,
                      dragState,
                      selectedItemId,
                      connections,
                      colors,
                      colorIndex,
                      isItemConnected,
                      getItemConnection,
                      canConnect,
                      leftItems,
                      rightItems,
                    );
                    return (
                      <motion.div
                        key={item.id}
                        id={`item-${item.id}`}
                        className={`relative min-h-[50px] ${
                          isAnswered ? "cursor-default" : "cursor-pointer"
                        } touch-manipulation select-none rounded-lg border-2 p-2 transition-all duration-200 sm:p-3 md:p-4`}
                        onMouseDown={(e) => handleMouseDown(item.id, e)}
                        onMouseUp={() => handleMouseUp(item.id)}
                        onClick={(e) => handleItemClick(item.id, e)}
                        onTouchEnd={(e) => handleItemClick(item.id, e)}
                        onMouseEnter={() => handleItemMouseEnter(item.id)}
                        onMouseLeave={handleItemMouseLeave}
                        whileTap={isAnswered ? {} : { scale: 0.98 }}
                        style={style}
                      >
                        <p className="text-xs sm:text-base" title={item.text}>
                          {item.text}
                        </p>
                        <div
                          className="-right-2 -translate-y-1/2 absolute top-1/2 z-20 flex size-3 items-center justify-center rounded-full border-2 transition-opacity duration-200"
                          style={{
                            background: style.dotColor,
                            borderColor: style.borderColor,
                          }}
                        />
                      </motion.div>
                    );
                  })}
                </div>
              </div>
              {/* Right Column */}
              <div>
                <div className="space-y-2 sm:space-y-3 md:space-y-4">
                  {rightItems.map((item) => {
                    const style = getItemStyle(
                      item.id,
                      dragState,
                      selectedItemId,
                      connections,
                      colors,
                      colorIndex,
                      isItemConnected,
                      getItemConnection,
                      canConnect,
                      leftItems,
                      rightItems,
                    );
                    return (
                      <motion.div
                        key={item.id}
                        id={`item-${item.id}`}
                        className={`relative min-h-[50px] ${
                          isAnswered ? "cursor-default" : "cursor-pointer"
                        } touch-manipulation select-none rounded-lg border-2 p-2 transition-all duration-200 sm:p-3 md:p-4`}
                        onMouseDown={(e) => handleMouseDown(item.id, e)}
                        onMouseUp={() => handleMouseUp(item.id)}
                        onClick={(e) => handleItemClick(item.id, e)}
                        onTouchEnd={(e) => handleItemClick(item.id, e)}
                        onMouseEnter={() => handleItemMouseEnter(item.id)}
                        onMouseLeave={handleItemMouseLeave}
                        style={style}
                        whileTap={isAnswered ? {} : { scale: 0.98 }}
                      >
                        <p className="text-xs sm:text-base" title={item.text}>
                          {item.text}
                        </p>
                        <div
                          className="-left-2 -translate-y-1/2 absolute top-1/2 z-20 flex size-3 items-center justify-center rounded-full border-2 transition-opacity duration-200"
                          style={{
                            background: style.dotColor,
                            borderColor: style.borderColor,
                          }}
                        />
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* SVG Layer for Lines */}
            <svg
              className="-left-1 pointer-events-none absolute inset-0 z-0"
              style={{ width: "105%", height: "100%" }}
            >
              <AnimatePresence>
                {connections.map((connection) => (
                  <motion.path
                    key={`${connection.fromId}-${connection.toId}`}
                    d={getConnectionPath(
                      connection.fromId,
                      connection.toId,
                      itemPositions,
                      containerRef,
                      leftItems,
                      rightItems,
                      isMobile,
                    )}
                    stroke={connection.color}
                    strokeWidth="2"
                    fill="none"
                    initial={{ pathLength: 0, opacity: 0 }}
                    animate={{ pathLength: 1, opacity: 1 }}
                    exit={{ pathLength: 0, opacity: 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  />
                ))}
              </AnimatePresence>

              {!isMobile && !isAnswered && dragState.isDragging && (
                <path
                  d={getDragPath(
                    dragState,
                    itemPositions,
                    containerRef,
                    leftItems,
                    rightItems,
                  )}
                  stroke="#9CA3AF"
                  strokeWidth="2"
                  fill="none"
                  strokeDasharray="5,5"
                  opacity="0.7"
                />
              )}
            </svg>
          </div>
        </div>
      </div>
      <GameControl
        isRevise={isAnswered}
        canAnswer={connections.length === leftItems.length && !isAnswered}
        canReset={connections.length > 0 && !isAnswered}
        showResults={showResults}
        isAllCorrect={isAllCorrect}
        checkAnswer={checkAnswer}
        resetGame={resetGame}
        nextSection={nextSection}
        gameConfig={gameConfig}
        backSection={backSection}
      />
    </div>
  );
}

export default React.memo(LineMatchingGame);
