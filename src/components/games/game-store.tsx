import { useAtomValue } from "jotai";
import { useMemo } from "react";
import LineMatchingGame from "@/components/games/line-matching";
import { atomVisibleSections } from "@/store/lesson";
import { GameType, SectionGame } from "@/types/lessons";
import DragDropGame from "./drag-and-drop";
import { GameError } from "./game-error";

export function GameStore(props: {
  navigateSection: (idx: number) => void;
  section: SectionGame;
  sectionIndex: number;
  gameSections: SectionGame[];
  isRevise: boolean;
  navigateBackSection?: () => void;
}) {
  const {
    navigateSection,
    section,
    sectionIndex,
    isRevise,
    navigateBackSection,
  } = props;
  const visibleSections = useAtomValue(atomVisibleSections);
  const isAnswered = useMemo(() => {
    return (
      isRevise || visibleSections.slice(0, -1).some((v) => v.id === section.id)
    );
  }, [isRevise, visibleSections, section]);

  switch (section.contentParsed?.game) {
    case GameType.CATEGORIZING:
      return (
        <DragDropGame
          {...props}
          gameConfig={section.contentParsed}
          nextSection={() => navigateSection(sectionIndex + 1)}
          section={section}
          key={section.id} // Reset state when section changes
          isAnswered={isAnswered}
          backSection={navigateBackSection}
        />
      );
    case GameType.MATCHING:
      return (
        <LineMatchingGame
          {...props}
          gameConfig={section.contentParsed}
          nextSection={() => navigateSection(sectionIndex + 1)}
          key={section.id} // Reset state when section changes
          isAnswered={isAnswered}
          backSection={navigateBackSection}
        />
      );
    default:
      return (
        <GameError
          navigateSection={navigateSection}
          section={section}
          sectionIndex={sectionIndex}
        />
      );
  }
}
