import { ExplainIcon } from "@/components/icons";
import * as m from "@/paraglide/messages.js";

interface GameExplanationProps {
  explanation: string;
}

export const GameExplanation = ({ explanation }: GameExplanationProps) => {
  return (
    <div className="flex gap-3 rounded-xl bg-[#D9E2FF4D] p-8 px-5 py-3 md:px-10 md:py-8">
      <div className="w-[3px] flex-shrink-0 self-stretch rounded-full bg-[#0C66E4]" />
      <div className="flex flex-1 flex-col gap-2">
        <div className="flex items-center gap-2">
          <ExplainIcon />
          <p>{m["games.explanation"]()}</p>
        </div>
        <p className="whitespace-pre-line text-sm">{explanation}</p>
      </div>
    </div>
  );
};
