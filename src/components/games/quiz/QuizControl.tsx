import { AnimatePresence, motion } from "framer-motion";
import { Check, LoaderCircleIcon } from "lucide-react";
import owlCorrectAlertImg from "@/assets/images/owls/owl-correct-alert.png";
import userSkip from "@/assets/images/user-skip.svg";
import { ArrowLineLeftIcon, ArrowLineRightIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import { GradientText } from "@/components/ui/gradient-text";
import * as m from "@/paraglide/messages.js";
import { SectionType } from "@/types/lessons";
import { cn } from "@/utils/cn";

type QuizControlsProps = {
  loading: boolean;
  onCheck: () => void;
  onBack?: () => void;
  skippable: boolean;
  onSkippable: () => void;
  disabled: boolean;
  selected?: boolean;
  isLast: boolean;
};

export function QuizControls({
  loading,
  onCheck,
  skippable,
  onSkippable,
  disabled,
  isLast,
  selected,
  onBack,
}: QuizControlsProps) {
  return (
    <div className="flex w-full items-center justify-between gap-2 sm:flex-row md:gap-5">
      <div className="flex items-center gap-2">
        {onBack && (
          <Button
            variant="outline"
            disabled={loading}
            size="lg"
            className="md:!px-16 rounded-full px-10"
            onClick={onBack}
          >
            <ArrowLineLeftIcon />
            {m["common.actions.back"]()}
          </Button>
        )}
        <Button
          disabled={disabled || !selected}
          type="submit"
          size="lg"
          className="md:!px-16 rounded-full px-10"
          onClick={onCheck}
        >
          <span className="text-base md:text-lg">
            {loading ? "" : m["common.actions.check"]()}
          </span>
          {loading ? (
            <LoaderCircleIcon className="size-4 animate-spin" />
          ) : (
            <Check className="size-4" />
          )}
        </Button>
      </div>
      <div className="flex items-center gap-3">
        {skippable && !isLast && (
          <button
            disabled={!skippable}
            type="reset"
            className={cn(
              "flex cursor-pointer items-center justify-center gap-1",
              !skippable && "text-(--text-disabled)",
            )}
            onClick={onSkippable}
          >
            <img src={userSkip} alt="user-skip" className="size-4 md:size-6" />
            <span className="text-base underline md:text-lg">
              {m["common.actions.skip"]()}
            </span>
          </button>
        )}
      </div>
    </div>
  );
}

export function IncorrectAlert({
  onRetryQuestion,
}: {
  onRetryQuestion: () => void;
}) {
  const incorrectVariants = {
    initial: { opacity: 0, y: 50 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.5 } },
    exit: { opacity: 0, y: -50, transition: { duration: 0.3 } },
  };
  // const scrollToIncorrectAlert = (node: HTMLElement | null) => {
  //   if (node) {
  //     node.scrollIntoView({ behavior: "smooth" });
  //   }
  // };
  return (
    <motion.div
      // ref={scrollToIncorrectAlert}
      className="mt-4 w-full max-w-4xl rounded-xl border border-(--card-tag-bg) bg-(--card-tag-bg)/10 p-8"
      key="incorrect-answer"
      variants={incorrectVariants}
      initial="initial"
      animate="animate"
      exit="exit"
    >
      <div className="flex items-center justify-center gap-4 md:text-xl">
        {m["games.incorrect"]()}
        <button
          type="button"
          onClick={onRetryQuestion}
          className="cursor-pointer rounded-full bg-(--learning-input-btn-bg) px-6 py-2 font-normal text-card text-lg"
        >
          {m["common.actions.try_again"]()}
        </button>
      </div>
    </motion.div>
  );
}

type CorrectAlertProps = {
  isFinishAnswer: boolean;
  onNextQuestion: () => void;
  position: [number, number] | undefined;
  type: SectionType | undefined;
  isRevise?: boolean;
  isLastSectionOfLesson?: boolean;
};

export function CorrectAlert({
  isFinishAnswer,
  onNextQuestion,
  position,
  type,
  isRevise,
  isLastSectionOfLesson = false,
}: CorrectAlertProps) {
  const correctVariants = {
    initial: { opacity: 0, y: 50 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.5 } },
    exit: { opacity: 0, y: -50, transition: { duration: 0.3 } },
  };

  const scrollToCorrectAlert = (node: HTMLElement | null) => {
    if (isRevise) return;
    if (node) {
      node.scrollIntoView({ behavior: "smooth" });
    }
  };

  const showNextButton = () => {
    if (isLastSectionOfLesson) return true;
    if (type === SectionType.GAME) return !isRevise;
    if (type === SectionType.CHALLENGE) return !isFinishAnswer || !isRevise;
    return true;
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        ref={scrollToCorrectAlert}
        className="mt-4 w-full rounded-xl border border-(--primary) bg-(--primary)/10 @lg:px-8 px-4 py-4"
        key="correct-alert"
        variants={correctVariants}
        initial="initial"
        animate="animate"
        exit="exit"
      >
        <div className="flex w-full @md:flex-row flex-col items-center justify-between gap-3">
          <div className="flex items-center gap-2">
            <div className="rounded-full bg-[#BEF2C7] px-2 py-1">
              <img
                src={owlCorrectAlertImg}
                alt="Success indicator"
                className="max-w-14"
              />
            </div>
            <div className="flex flex-col gap-1">
              <GradientText className="font-normal text-2xl sm:text-3xl">
                {m["games.correct_answer"]()}
              </GradientText>
              {position && (
                <div className="flex @lg:flex-row flex-col @lg:items-center gap-2">
                  <p>
                    <span className="font-semibold">
                      {position[0] + 1}/{position[1]}
                    </span>{" "}
                    {type === SectionType.CHALLENGE
                      ? position[1] === 1
                        ? m["games.quiz_completed"]()
                        : m["games.quizzes_completed"]()
                      : position[1] === 1
                        ? m["games.game_completed"]()
                        : m["games.games_completed"]()}
                  </p>
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-row items-center justify-center gap-2 sm:flex-col md:flex-row">
            {showNextButton() && (
              <Button
                size="lg"
                className="lg:!px-14 @sm:w-max rounded-full px-10 font-normal text-lg"
                onClick={onNextQuestion}
              >
                {isFinishAnswer
                  ? m["common.actions.finish"]()
                  : m["common.actions.continue"]()}
                <ArrowLineRightIcon />
              </Button>
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
