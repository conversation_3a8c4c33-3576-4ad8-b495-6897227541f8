import { useMutation } from "@tanstack/react-query";
import { UUID } from "crypto";
import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { checkAnswer, useQuizAttempts, useQuizzes } from "@/services/quizzes";
import { atomCurrentQuizId, atomMappingQuizIdToUserAnswer } from "@/store/chat";
import { atomVisibleSections } from "@/store/lesson";
import { Quiz, QuizAnswer } from "@/types/quizzes";

export enum STATUSES {
  CORRECT = "correct",
  INCORRECT = "incorrect",
}

export type Attempt = {
  status: STATUSES | undefined;
  answer: QuizAnswer;
  explanation?: string;
};

export function useQuiz({ sectionID }: { sectionID: UUID }) {
  const { data: quizzes, isPending: isPendingQuizzes } = useQuizzes(sectionID);
  const { data: answerAttempts, isPending: isPendingAttempts } =
    useQuizAttempts(sectionID);
  const [quiz, setQuiz] = useState<Quiz | undefined>(undefined);
  const [mappingQuizIdToUserAnswer, setMappingQuizIdToUserAnswer] = useAtom(
    atomMappingQuizIdToUserAnswer,
  );

  const quizzesWithAttempts = useMemo(() => {
    if (!quizzes?.length) {
      return [];
    }

    return quizzes.map((q) => {
      q.attempt = answerAttempts?.find((v) => v.quiz_id === q.id);
      if (q.attempt) {
        // correct only preset when we submit the attempt
        // so we normalize it here
        q.attempt.correctly_answered =
          q.attempt.correctly_answered || !!q.attempt.points;
      }
      return q;
    });
  }, [quizzes, answerAttempts]);

  useEffect(() => {
    quizzesWithAttempts.forEach((q) => {
      setMappingQuizIdToUserAnswer((prev) => {
        return {
          ...prev,
          [q.id]: {
            status: q.attempt?.correctly_answered
              ? STATUSES.CORRECT
              : undefined,
            answer: q.attempt?.user_answer || [],
            explanation: q.attempt?.explanation,
          },
        };
      });
    });
  }, [quizzesWithAttempts, setMappingQuizIdToUserAnswer]);

  const quizIndex = useMemo(() => {
    return quizzesWithAttempts.findIndex((v) => v.id === quiz?.id);
  }, [quizzesWithAttempts, quiz]);

  const quizIsLast = quizIndex >= quizzesWithAttempts.length - 1;
  const visibleSections = useAtomValue(atomVisibleSections);
  const setCurrentQuizId = useSetAtom(atomCurrentQuizId);
  const isLastSectionOfLesson =
    sectionID === visibleSections[visibleSections.length - 1]?.id;
  const isRevise = visibleSections.some((v) => v.id === sectionID);

  const handleNext = (callbackLast?: () => void) => {
    if (quizIsLast) {
      return callbackLast?.();
    }

    const q = quizzesWithAttempts[quizIndex + 1];
    if (!q) {
      return callbackLast?.();
    }
    setQuiz(q);
    setCurrentQuizId(q.id);
    if (!q.attempt) {
      setMappingQuizIdToUserAnswer((prev) => {
        return {
          ...prev,
          [q.id]: { status: undefined, answer: [] },
        };
      });
      return;
    }
  };

  const handleBack = (callbackFirst?: () => void) => {
    if (quizIndex === 0) {
      setQuiz(undefined);
      return callbackFirst?.();
    }

    const q = quizzesWithAttempts[quizIndex - 1];
    if (!q) {
      return callbackFirst?.();
    }
    setQuiz(q);
    setCurrentQuizId(q.id);
  };

  const handleRetry = () => {
    if (!quiz?.id) return;

    setMappingQuizIdToUserAnswer((prev) => {
      return {
        ...prev,
        [quiz?.id]: { status: undefined, answer: [] },
      };
    });
  };

  const mutationAnswer = useMutation({
    mutationKey: ["answer", quiz?.id],
    mutationFn: ({ answer }: { answer: QuizAnswer }) => {
      if (!quiz?.id) {
        throw new Error("No quiz");
      }

      return checkAnswer(quiz?.id, answer);
    },
  });

  const handleCheck = async () => {
    if (!quiz?.id) return;

    const userAnswer = mappingQuizIdToUserAnswer[quiz.id]?.answer || [];

    if (userAnswer.length < 1) {
      return toast("Please select an answer");
    }

    mutationAnswer
      .mutateAsync({
        answer: userAnswer,
      })
      .then((v) => {
        if (v.correct) {
          setMappingQuizIdToUserAnswer((prev) => {
            return {
              ...prev,
              [quiz.id]: {
                status: STATUSES.CORRECT,
                answer: v.user_answer,
                explanation: v.explanation,
              },
            };
          });
          return;
        }

        setMappingQuizIdToUserAnswer((prev) => {
          return {
            ...prev,
            [quiz.id]: { status: STATUSES.INCORRECT, answer: v.user_answer },
          };
        });
      });
  };

  const updateMappingForQuiz = (
    quizId: UUID,
    selected: number,
    isMultipleChoice: boolean,
  ) => {
    setMappingQuizIdToUserAnswer((prev) => {
      if (!isMultipleChoice) {
        return {
          ...prev,
          [quizId]: {
            answer: [selected],
            status: undefined,
          },
        };
      }

      const currentAnswers = prev[quizId]?.answer || [];
      const newAnswers = currentAnswers.includes(selected)
        ? currentAnswers.filter((x: number) => x !== selected)
        : [...currentAnswers, selected];
      return {
        ...prev,
        [quizId]: {
          answer: newAnswers,
          status: undefined,
        },
      };
    });
  };

  const handleSelect = (selected: number) => {
    if (mutationAnswer.isPending || !quiz?.id) {
      return;
    }

    if (!quiz?.multiple_choices) {
      updateMappingForQuiz(quiz.id, selected, false);
      return;
    }

    updateMappingForQuiz(quiz.id, selected, true);
  };

  // we bootstrap quiz based on the last correct attempt
  // biome-ignore lint/correctness/useExhaustiveDependencies: setQuiz is stable
  useEffect(() => {
    if (!quizzesWithAttempts.length) {
      return;
    }

    if (isPendingAttempts) {
      return;
    }

    const allDone = quizzesWithAttempts.every(
      (v) => !!v.attempt?.correctly_answered,
    );

    // in case that quizzes are done, we show the first quiz and set the attempt to correct with the correct answer
    if (allDone) {
      setQuiz(quizzesWithAttempts[0]);
      return;
    }

    const idxLast = quizzesWithAttempts.findIndex(
      (v) => !!v.attempt?.correctly_answered,
    );

    setQuiz((v) => {
      if (v) {
        // already bootstrapped
        return v;
      }

      // first correct attempt not found => use first quiz
      // else, use the next quiz
      // first correct attempt should not be found at the last quiz, in that case, idxLast + 1 will exceed array length, but it's undefined anyway
      return idxLast === -1 || idxLast === quizzesWithAttempts.length - 1
        ? quizzesWithAttempts[0]
        : quizzesWithAttempts[idxLast + 1];
    });
  }, [quizzesWithAttempts, isPendingAttempts, setQuiz]);

  return {
    quiz,
    quizIndex,
    quizIsLast,
    quizzesWithAttempts,
    isPendingQuizzes,
    isLastSectionOfLesson,

    mutationAnswer,

    handleNext,
    handleBack,
    handleRetry,
    handleCheck,
    handleSelect,
  };
}
