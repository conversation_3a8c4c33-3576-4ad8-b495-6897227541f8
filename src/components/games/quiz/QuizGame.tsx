import { AnimatePresence, motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { Check } from "lucide-react";
import { useMemo } from "react";
import { GameExplanation } from "@/components/games/game-explain";
import { AnswerOption } from "@/components/games/quiz/AnswerOption";
import {
  CorrectAlert,
  IncorrectAlert,
  QuizControls,
} from "@/components/games/quiz/QuizControl";
import { ArrowLineRightIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import * as m from "@/paraglide/messages.js";
import { atomMappingQuizIdToUserAnswer } from "@/store/chat";
import { SectionQuiz, SectionType } from "@/types/lessons";
import { STATUSES, useQuiz } from "./use-quiz";

type QuizGameProps = {
  section: SectionQuiz;
  nextSection: () => void;
  backSection?: () => void;
  isRevise: boolean;
};

export default function QuizGame({
  section,
  nextSection,
  isRevise,
  backSection,
}: QuizGameProps) {
  const {
    quiz,
    quizIndex,
    quizIsLast,
    quizzesWithAttempts,
    isPendingQuizzes,
    mutationAnswer,
    isLastSectionOfLesson = false,
    handleNext,
    handleRetry,
    handleCheck,
    handleSelect,
    handleBack,
  } = useQuiz({
    sectionID: section.id,
  });

  const mappingQuizIdToUserAnswer = useAtomValue(atomMappingQuizIdToUserAnswer);
  const attempt = useMemo(() => {
    if (!quiz?.id) {
      return {
        answer: [],
        status: undefined,
      };
    }
    return (
      mappingQuizIdToUserAnswer[quiz.id] ?? {
        answer: [],
        status: undefined,
        explanation: "",
      }
    );
  }, [quiz?.id, mappingQuizIdToUserAnswer]);

  function handleNextOrNextSection() {
    return handleNext(() => {
      nextSection();
    });
  }

  if (!quizzesWithAttempts?.length) {
    return;
  }

  const questionVariants = {
    initial: { opacity: 0, y: 50 },
    animate: { opacity: 1, y: 0, transition: { duration: 0.5 } },
    exit: { opacity: 0, y: -50, transition: { duration: 0.3 } },
  };

  return (
    <div className="@container flex h-max flex-col items-center gap-2 px-2 sm:h-full">
      <div className="relative flex w-full flex-col items-center justify-center md:gap-8">
        <AnimatePresence mode="wait">
          {!isPendingQuizzes && !quizzesWithAttempts.length && (
            <div className="flex flex-col items-center gap-4">
              <p>Oops, not available at the moment.</p>
              <p className="text-foreground/50 text-xs">{section?.id}</p>
              <Button
                size="lg"
                className="md:!px-16 rounded-full px-10"
                onClick={nextSection}
              >
                Continue
                <ArrowLineRightIcon />
              </Button>
            </div>
          )}
          {quiz && (
            <motion.div
              key={quiz.id}
              variants={questionVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              className="w-full"
              data-quiz-id={quiz.id}
            >
              <Question
                position={[quizIndex, quizzesWithAttempts.length]}
                question={quiz.question}
                multipleChoice={quiz.multiple_choices}
              />

              <div className="my-4 grid @sm:w-auto w-full @md:grid-cols-2 grid-cols-1 gap-x-4 gap-y-3">
                {quiz.answers.map((item, index) => (
                  <AnswerOption
                    key={item}
                    position={[index, quizzesWithAttempts.length]}
                    answer={item}
                    attempt={attempt}
                    onSelected={handleSelect}
                    disabled={
                      mutationAnswer.isPending ||
                      attempt.status === STATUSES.CORRECT
                    }
                  />
                ))}
              </div>
              {!attempt.status && (
                <QuizControls
                  loading={mutationAnswer.isPending}
                  onCheck={handleCheck}
                  skippable={(quiz.skippable || isRevise) && !quizIsLast}
                  onSkippable={handleNextOrNextSection}
                  disabled={
                    mutationAnswer.isPending ||
                    attempt.status === STATUSES.INCORRECT
                  }
                  selected={!!attempt.answer.length}
                  isLast={quizIsLast}
                  onBack={
                    backSection
                      ? () => handleBack(() => backSection())
                      : undefined
                  }
                />
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      <AnimatePresence mode="wait">
        {attempt.status === STATUSES.INCORRECT ? (
          <IncorrectAlert onRetryQuestion={handleRetry} />
        ) : attempt.status === STATUSES.CORRECT ? (
          <CorrectAlert
            isRevise={isRevise}
            type={SectionType.CHALLENGE}
            isFinishAnswer={quizIsLast}
            onNextQuestion={handleNextOrNextSection}
            position={[quizIndex, quizzesWithAttempts.length]}
            isLastSectionOfLesson={isLastSectionOfLesson}
          />
        ) : null}
      </AnimatePresence>
      {attempt.status === STATUSES.CORRECT && (
        <AnimatePresence mode="wait">
          <div className="mt-4 w-full max-w-4xl">
            {attempt.explanation && (
              <GameExplanation explanation={attempt.explanation} />
            )}
          </div>
        </AnimatePresence>
      )}
    </div>
  );
}

function Question({
  position,
  question,
  multipleChoice,
}: {
  position: [number, number];
  question: string;
  multipleChoice: boolean;
}) {
  const isSingle = position[1] === 1;

  return (
    <div className="flex w-full cursor-pointer flex-col gap-4 rounded-lg border-(--line-border) border-3 border-b-6 bg-(--card) px-2 py-4 md:px-5 md:py-7">
      <div className="flex @sm:flex-row flex-col items-center justify-between gap-2">
        <p className="text-base text-foreground/50 md:text-xl">
          {isSingle
            ? m["games.quiz.single"]()
            : `${m["games.quiz.question"]()} ${position[0] + 1} ${m["games.quiz.of"]()} ${position[1]}`}
        </p>
        <div className="flex max-w-max items-center gap-1 rounded-xl bg-(--learning-btn-foreground)/20 px-4 py-1.5 text-(--learning-btn-foreground) text-sm">
          <Check className="h-4 w-4" />{" "}
          {multipleChoice
            ? m["games.quiz.select_all"]()
            : m["games.quiz.pick_correct"]()}
        </div>
      </div>
      <span className="whitespace-pre-line text-base md:text-xl">
        {question}
      </span>
    </div>
  );
}
