import { ArrowLineLeftIcon, ArrowLineRightIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import * as m from "@/paraglide/messages.js";
import { Section, SectionGame } from "@/types/lessons";

interface GameErrorProps {
  navigateSection: (newIndex: number) => void;
  section: SectionGame | Section;
  sectionIndex: number;
}

export const GameError = ({
  navigateSection,
  section,
  sectionIndex,
}: GameErrorProps) => {
  return (
    <div className="@container mx-auto flex h-max flex-col items-center p-5 sm:h-full md:p-10">
      <div className="relative flex w-full max-w-3xl flex-col items-center justify-center rounded-xl p-4 md:gap-8 md:p-8">
        <div className="flex flex-col items-center gap-4">
          <p>{m["ui.not_available"]()}</p>
          <p className="text-foreground/50 text-xs">{section.id}</p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="lg"
              className="md:!px-16 flex justify-center rounded-full bg-transparent px-10 text-foreground"
              onClick={() => navigateSection(sectionIndex - 1)}
            >
              <ArrowLineLeftIcon />
              {m["common.actions.back"]()}
            </Button>
            <Button
              size="lg"
              className="md:!px-16 flex justify-center rounded-full"
              onClick={() => navigateSection(sectionIndex + 1)}
            >
              {m["common.actions.continue"]()}
              <ArrowLineRightIcon />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
