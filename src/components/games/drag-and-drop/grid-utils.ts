// Function to calculate optimal grid columns with max 3 per row and balanced layout
export const getOptimalGridCols = (itemCount: number): string => {
  if (itemCount <= 1) return "grid-cols-1";
  if (itemCount <= 2) return "grid-cols-1 lg:grid-cols-2";
  if (itemCount === 3) return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
  if (itemCount === 4) return "grid-cols-1 md:grid-cols-2"; // 2x2 layout for balance
  if (itemCount <= 6) return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"; // 2x3 layout
  if (itemCount <= 9) return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"; // 3x3 layout
  // For more than 9 items, stick to max 3 columns
  return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
};
