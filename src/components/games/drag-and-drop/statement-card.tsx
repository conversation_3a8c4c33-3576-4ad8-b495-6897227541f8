import { useDraggable } from "@dnd-kit/core";
import { memo } from "react";
import { cn } from "@/utils/cn";
import type { DragDropItem } from "./index";

interface StatementCardProps {
  statement: DragDropItem;
  isDragging: boolean;
  activeCategoryIndex?: number;
  showResults?: boolean;
  categoryIndex?: number;
  isSelected?: boolean;
  onSelect?: (id: string) => void;
}

const getCategoryColors = (index: number | undefined) => {
  if (index === undefined) return { border: "", shadow: "" };

  const colors = [
    {
      border: "border-[#FFB800]",
      shadow: "shadow-[0px_5px_0px_0px_#FFB800]",
    },
    {
      border: "border-[#027BE5]",
      shadow: "shadow-[0px_5px_0px_0px_#027BE5]",
    },
    {
      border: "border-green-400",
      shadow: "shadow-[0px_5px_0px_0px_rgb(74,222,128)]",
    },
    {
      border: "border-purple-300",
      shadow: "shadow-[0px_5px_0px_0px_rgb(196,181,253)]",
    },
    {
      border: "border-red-400",
      shadow: "shadow-[0px_5px_0px_0px_rgb(248,113,113)]",
    },
    {
      border: "border-pink-400",
      shadow: "shadow-[0px_5px_0px_0px_rgb(244,114,182)]",
    },
  ];

  return colors[index % colors.length];
};

const StatementCard = memo(function StatementCard({
  statement,
  isDragging,
  categoryIndex,
  isSelected = false,
  onSelect,
  activeCategoryIndex,
}: StatementCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    isDragging: isBeingDragged,
  } = useDraggable({
    id: statement.id,
  });

  const categoryColors = getCategoryColors(
    categoryIndex !== undefined ? categoryIndex : activeCategoryIndex,
  );

  const handleClick = (e: React.MouseEvent) => {
    if (onSelect) {
      e.stopPropagation();
      onSelect(statement.id);
    }
  };

  const baseClasses =
    "group w-full cursor-grab touch-manipulation rounded-(--size-2xs) border-3 bg-white px-2 py-1 transition-all duration-200 active:cursor-grabbing";
  const dragClasses = isBeingDragged
    ? "pointer-events-none rotate-1 scale-105 select-none opacity-0 drop-shadow-none"
    : "";
  const selectedClasses = isSelected
    ? "border-[#667799] shadow-[0px_5px_0px_0px_#667799]"
    : `border-[#E6E8F0] shadow-[0px_5px_0px_0px_#E6E8F0] ${categoryColors?.border || ""} ${categoryColors?.shadow || ""}`;
  const draggingClasses = isDragging ? "scale-105 border-3" : "";

  return (
    <div
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      onClick={handleClick}
      className={cn(baseClasses, dragClasses, selectedClasses, draggingClasses)}
      title={onSelect ? "Click to move card" : ""}
    >
      <div className="flex items-start gap-2 sm:gap-3">
        <p className="flex-1 select-none whitespace-pre-line py-1 text-gray-700 text-xs leading-relaxed sm:text-sm">
          {statement.content}
        </p>
      </div>
    </div>
  );
});

export default StatementCard;
