import { useDroppable } from "@dnd-kit/core";
import { AnimatePresence, motion } from "framer-motion";
import { memo } from "react";
import { cn } from "@/utils/cn";
import type { DragDropItem } from "./index";
import StatementCard from "./statement-card";

interface DropZoneProps {
  id: string;
  title: string;
  borderColor: string;
  bgColor: string;
  statements: DragDropItem[];
  showResults?: boolean;
  categoryIndex: number;
  onSelect?: (id: string) => void;
  hasSelectedCard?: boolean;
  onCardSelect?: (id: string) => void;
}

const DropZone = memo(function DropZone({
  id,
  title,
  borderColor,
  bgColor,
  statements,
  showResults = false,
  categoryIndex,
  onSelect,
  hasSelectedCard = false,
  onCardSelect,
}: DropZoneProps) {
  const { isOver, setNodeRef } = useDroppable({
    id,
  });

  const handleClick = () => {
    if (onSelect) {
      onSelect(id);
    }
  };

  const hasCards = statements.length > 0;
  const shouldShowSubtleAnimation = hasSelectedCard && !hasCards;
  const shouldShowMinimalAnimation = hasSelectedCard && hasCards;

  return (
    <motion.div
      ref={setNodeRef}
      className={cn(
        "relative min-h-[150px] overflow-hidden rounded-xl border-2 p-2 transition-all duration-300 sm:min-h-[170px] sm:p-3",
        borderColor,
        bgColor,
        // Normal state
        !hasSelectedCard && !isOver ? "border-dashed" : "",
        // Drag over state
        isOver &&
          "scale-[1.02] border-solid shadow-lg brightness-105 sm:scale-105",
        // Selected card state - differentiate between empty and filled
        hasSelectedCard &&
          !isOver &&
          !hasCards &&
          "cursor-pointer border-solid shadow-md hover:scale-[1.01] hover:shadow-lg",
        hasSelectedCard &&
          !isOver &&
          hasCards &&
          "cursor-pointer border-solid hover:shadow-md hover:brightness-105",
        // Combined state (selected + hover)
        hasSelectedCard &&
          isOver &&
          "scale-[1.03] border-solid shadow-xl brightness-110 sm:scale-[1.06]",
      )}
      onClick={handleClick}
      animate={{
        borderWidth: hasSelectedCard ? 3 : 2,
      }}
      transition={{
        borderWidth: {
          duration: 0.2,
          ease: "easeOut",
        },
      }}
    >
      {/* Subtle glow effect - only for empty dropzones */}
      <AnimatePresence>
        {shouldShowSubtleAnimation && (
          <motion.div
            className="pointer-events-none absolute inset-0 rounded-xl bg-gradient-to-br from-blue-50/30 to-indigo-50/30"
            initial={{ opacity: 0 }}
            animate={{
              opacity: [0.2, 0.4, 0.2],
            }}
            exit={{ opacity: 0 }}
            transition={{
              opacity: {
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              },
            }}
          />
        )}
      </AnimatePresence>

      {/* Minimal border glow for filled dropzones */}
      <AnimatePresence>
        {shouldShowMinimalAnimation && (
          <motion.div
            className="pointer-events-none absolute inset-0 rounded-xl border-2 border-blue-400/20"
            initial={{ opacity: 0 }}
            animate={{
              opacity: [0, 0.6, 0],
            }}
            exit={{ opacity: 0 }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        )}
      </AnimatePresence>

      <div className="relative z-10 text-center">
        <motion.h3
          className="text-gray-800 text-xl sm:text-2xl"
          animate={{
            color: hasSelectedCard && !hasCards ? "" : "",
          }}
          transition={{ duration: 0.3 }}
        >
          {title}
        </motion.h3>
      </div>

      {statements.length === 0 ? (
        <div className="relative z-10 flex h-20 items-center justify-center">
          <div className="text-center">
            <AnimatePresence mode="wait">
              {hasSelectedCard ? (
                <motion.div
                  key="selected"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                  className="flex flex-col items-center gap-2"
                >
                  <motion.p
                    className="select-none text-xs sm:text-sm"
                    animate={{
                      opacity: [0.6, 1, 0.6],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  >
                    Click to place card here
                  </motion.p>
                </motion.div>
              ) : (
                <motion.p
                  key="default"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="select-none text-gray-500 text-xs sm:text-sm"
                >
                  Drop cards here
                </motion.p>
              )}
            </AnimatePresence>
          </div>
        </div>
      ) : (
        <motion.div
          className="relative z-10 mt-3 mb-8 space-y-2 sm:mt-4 sm:space-y-3"
          layout
        >
          {statements.map((statement, index) => (
            <motion.div
              key={`${statement.id}-${categoryIndex}-${index}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{
                duration: 0.2,
                delay: index * 0.05,
                ease: "easeOut",
              }}
              layout
            >
              <StatementCard
                categoryIndex={categoryIndex}
                statement={statement}
                isDragging={false}
                showResults={showResults}
                onSelect={onCardSelect}
              />
            </motion.div>
          ))}

          {/* Subtle indicator for filled dropzones */}
          {hasSelectedCard && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="-bottom-10 -translate-x-1/2 absolute left-1/2 flex w-full items-center justify-center py-2"
            >
              <motion.div className="flex items-center gap-1 text-gray-800 text-xs sm:text-sm">
                <span>✨</span>
                <span>Click to add card here</span>
                <span>✨</span>
              </motion.div>
            </motion.div>
          )}
        </motion.div>
      )}
    </motion.div>
  );
});

export default DropZone;
