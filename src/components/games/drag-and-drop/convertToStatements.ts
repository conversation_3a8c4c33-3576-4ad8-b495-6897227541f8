import { DragDropItem } from "@/components/games/drag-and-drop/index";
import { GameConfig } from "@/types/lessons";

export function convertToStatements(config: GameConfig): DragDropItem[] {
  const statements: DragDropItem[] = [];

  config.parameters.categories.forEach((category, categoryIndex) => {
    category.items.forEach((item, index) => {
      const statement: DragDropItem = {
        id: `${category.label}-${index}`,
        content: item,
        categoryIndex: categoryIndex, // Changed from type to categoryIndex
      };
      statements.push(statement);
    });
  });

  return statements.sort(() => Math.random() - 0.5);
}
