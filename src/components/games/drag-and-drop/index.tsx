import {
  DndContext,
  type DragEndEvent,
  DragOverEvent,
  DragOverlay,
  type DragStartEvent,
  PointerSensor,
  rectIntersection,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { motion } from "framer-motion";
import { useSet<PERSON>tom } from "jotai/index";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { convertToStatements } from "@/components/games/drag-and-drop/convertToStatements";
import { atomCurrentGameUserAnswer } from "@/store/chat";
import { GameConfig, GameSectionCategory, Section } from "@/types/lessons";
import { GameControl } from "../game-control";
import DropZone from "./drop-zone";
import { getOptimalGridCols } from "./grid-utils";
import StatementCard from "./statement-card";
import StatementCardsContainer from "./statement-cards-container";

export interface DragDropItem {
  id: string;
  content: string;
  categoryIndex: number;
}

interface DragDropGameProps {
  nextSection: () => void;
  backSection?: () => void;
  gameConfig: GameConfig;
  isAnswered: boolean;
  section: Section;
}

const categoryColors = [
  { border: "border-yellow-400", bg: "bg-yellow-50" },
  { border: "border-[#027BE5]", bg: "bg-[#ECF5FF]" },
  { border: "border-green-400", bg: "bg-green-50" },
  { border: "border-purple-400", bg: "bg-purple-50" },
  { border: "border-pink-400", bg: "bg-pink-50" },
];

function DragDropGame({
  nextSection,
  gameConfig,
  isAnswered,
  section,
  backSection,
}: DragDropGameProps) {
  const initialStatements = useMemo(
    () => convertToStatements(gameConfig),
    [gameConfig],
  );

  const [statements, setStatements] =
    useState<DragDropItem[]>(initialStatements);
  const [categorizedCards, setCategorizedCards] = useState<
    Record<number, DragDropItem[]>
  >({});
  const [activeStatement, setActiveStatement] = useState<DragDropItem | null>(
    null,
  );
  const [showResults, setShowResults] = useState(false);
  const [isAllCorrect, setIsAllCorrect] = useState(false);
  const [selectedStatementId, setSelectedStatementId] = useState<string | null>(
    null,
  );
  const [overDropZone, setOverDropZone] = useState<string | null>(null);
  const setCurrentGameUserAnswer = useSetAtom(atomCurrentGameUserAnswer);

  // biome-ignore lint/correctness/useExhaustiveDependencies: reset showResult fail when user continue play
  useEffect(() => {
    const result: GameSectionCategory[] = gameConfig.parameters.categories.map(
      (category, index) => {
        const cardsInCategory = categorizedCards[index] || [];
        return {
          label: category.label,
          items: cardsInCategory.map((card) => card.content),
        };
      },
    );

    if (showResults) {
      setShowResults(false);
    }

    setCurrentGameUserAnswer(result);
  }, [categorizedCards, gameConfig, setCurrentGameUserAnswer]);

  // Setup initial state when isAnswered is true
  useEffect(() => {
    if (!isAnswered) {
      return;
    }
    // Place all statements in their correct categories
    const correctCategorizedCards: Record<number, DragDropItem[]> = {};

    initialStatements.forEach((statement) => {
      const categoryIndex = statement.categoryIndex;
      if (!correctCategorizedCards[categoryIndex]) {
        correctCategorizedCards[categoryIndex] = [];
      }
      correctCategorizedCards[categoryIndex].push(statement);
    });

    setStatements([]); // Clear statements container
    setCategorizedCards(correctCategorizedCards);
    setShowResults(true);
    setIsAllCorrect(true);
  }, [isAnswered, initialStatements]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    }),
  );

  const handleDragStart = (event: DragStartEvent) => {
    if (isAllCorrect || isAnswered) return;
    const { active } = event;
    const statementId = active.id as string;
    const statement = findStatementById(statementId);
    setActiveStatement(statement);
    setSelectedStatementId(null);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    if (isAllCorrect || isAnswered) return;
    const { active, over } = event;
    setActiveStatement(null);
    setSelectedStatementId(null);
    setOverDropZone(null);

    if (!over) return;

    const statementId = active.id as string;
    const dropZoneId = over.id as string;

    const statement = findStatementById(statementId);
    if (!statement) return;

    removeStatementFromAllContainers(statementId);

    if (dropZoneId === "statements") {
      setStatements((prev) => [...prev, statement]);
      return;
    }
    const categoryIndex = parseInt(dropZoneId.split("-")[1]);
    if (!Number.isNaN(categoryIndex)) {
      setCategorizedCards((prev) => ({
        ...prev,
        [categoryIndex]: [...(prev[categoryIndex] || []), statement],
      }));
    }
  };

  const findStatementById = (id: string): DragDropItem | null => {
    const allCategorizedCards = Object.values(categorizedCards).flat();
    return (
      [...statements, ...allCategorizedCards].find(
        (statement) => statement.id === id,
      ) || null
    );
  };

  const removeStatementFromAllContainers = (id: string) => {
    setStatements((prev) => prev.filter((s) => s.id !== id));
    setCategorizedCards((prev) => {
      const newCategorizedCards = { ...prev };
      Object.keys(newCategorizedCards).forEach((key) => {
        const categoryIndex = parseInt(key);
        newCategorizedCards[categoryIndex] = newCategorizedCards[
          categoryIndex
        ].filter((s) => s.id !== id);
      });
      return newCategorizedCards;
    });
  };

  const checkAnswers = () => {
    let correctCount = 0;
    let totalPlaced = 0;

    Object.entries(categorizedCards).forEach(([categoryIndexStr, cards]) => {
      const categoryIndex = parseInt(categoryIndexStr);
      cards.forEach((card) => {
        totalPlaced++;
        if (card.categoryIndex === categoryIndex) {
          correctCount++;
        }
      });
    });

    const totalStatements = initialStatements.length;
    const isAllCorrect =
      correctCount === totalPlaced && totalPlaced === totalStatements;
    setShowResults(true);
    setIsAllCorrect(isAllCorrect);
  };

  const resetGame = useCallback(() => {
    if (isAnswered) return; // Don't allow reset if already answered

    setStatements(initialStatements);
    setCategorizedCards({});
    setShowResults(false);
    setIsAllCorrect(false);
  }, [initialStatements, isAnswered]);

  const canCheckAnswers = statements.length === 0 && !isAnswered;

  const handleCardSelect = (id: string) => {
    if (isAllCorrect || isAnswered) return;

    // If the card is already selected, deselect it
    if (selectedStatementId === id) {
      setSelectedStatementId(null);
      return;
    }

    setSelectedStatementId(id);

    if (showResults) {
      setShowResults(false);
      setIsAllCorrect(false);
    }
  };

  const handleDropZoneSelect = (dropZoneId: string) => {
    if (isAllCorrect || isAnswered || !selectedStatementId) return;

    const statement = findStatementById(selectedStatementId);
    if (!statement) return;

    removeStatementFromAllContainers(selectedStatementId);

    if (dropZoneId === "statements") {
      setStatements((prev) => [...prev, statement]);
      return;
    }
    const categoryIndex = parseInt(dropZoneId.split("-")[1]);
    if (!Number.isNaN(categoryIndex)) {
      setCategorizedCards((prev) => ({
        ...prev,
        [categoryIndex]: [...(prev[categoryIndex] || []), statement],
      }));
    }
    setSelectedStatementId(null);
  };

  // Add a new function to handle card selection from any container
  const handleCardSelectFromAnyContainer = (id: string) => {
    if (isAllCorrect || isAnswered) return;

    // If we're in results mode, reset results when moving cards
    if (showResults) {
      setShowResults(false);
      setIsAllCorrect(false);
    }

    // If the card is already selected, deselect it
    if (selectedStatementId === id) {
      setSelectedStatementId(null);
      return;
    }

    // If the card is in a drop zone, move it back to statements
    const statement = findStatementById(id);
    if (statement) {
      removeStatementFromAllContainers(id);
      setStatements((prev) => [...prev, statement]);
    }
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event;
    setOverDropZone(
      over?.id.toString().includes("category-") ? (over?.id as string) : null,
    );
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={rectIntersection}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={handleDragOver}
    >
      <motion.div
        className="@container mx-auto max-w-6xl"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.4, ease: "easeOut" }}
      >
        <motion.div
          className="sm:!mt-8 mx-auto mt-2 w-full overflow-hidden rounded-(--size-2xs) border-3 border-[#E6E8F0]"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.div
            className="mt-8 mb-6 flex justify-center text-center sm:mb-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.3 }}
          >
            <p className="text-lg md:max-w-[60%] md:text-xl">
              {gameConfig.description}
            </p>
          </motion.div>

          <motion.div
            id={`dropzone-container-${section?.id}`}
            className={`mb-6 grid gap-4 p-3 sm:mb-8 sm:gap-6 sm:p-6 lg:p-4 ${getOptimalGridCols(gameConfig.parameters.categories.length)}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            {gameConfig.parameters.categories.map((category, index) => (
              <motion.div
                key={index.toString()}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
              >
                <DropZone
                  id={`category-${index}`}
                  title={category.label}
                  borderColor={
                    categoryColors[index % categoryColors.length].border
                  }
                  bgColor={categoryColors[index % categoryColors.length].bg}
                  statements={categorizedCards[index] || []}
                  showResults={showResults}
                  categoryIndex={index}
                  onSelect={handleDropZoneSelect}
                  hasSelectedCard={!!selectedStatementId}
                  onCardSelect={handleCardSelectFromAnyContainer}
                />
              </motion.div>
            ))}
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <StatementCardsContainer
              categories={gameConfig.parameters.categories}
              statements={statements}
              onCardSelect={handleCardSelect}
              selectedStatementId={selectedStatementId}
              activeStatement={activeStatement}
              section={section}
            />
          </motion.div>

          <DragOverlay>
            {activeStatement ? (
              <StatementCard
                statement={activeStatement}
                isDragging={true}
                activeCategoryIndex={Number.parseInt(
                  overDropZone?.split("-")?.[1] || "-1",
                )}
              />
            ) : null}
          </DragOverlay>
        </motion.div>

        <GameControl
          isRevise={isAnswered}
          checkAnswer={checkAnswers}
          canAnswer={canCheckAnswers}
          canReset={initialStatements.length > statements.length && !isAnswered}
          showResults={showResults}
          isAllCorrect={isAllCorrect || isAnswered}
          resetGame={resetGame}
          nextSection={nextSection}
          gameConfig={gameConfig}
          backSection={backSection}
        />
      </motion.div>
    </DndContext>
  );
}

export default React.memo(DragDropGame);
