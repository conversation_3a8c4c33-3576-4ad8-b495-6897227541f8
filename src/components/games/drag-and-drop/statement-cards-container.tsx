import { useDroppable } from "@dnd-kit/core";
import { memo, useCallback } from "react";
import { GameSectionCategory, Section } from "@/types/lessons";
import { cn } from "@/utils/cn";
import { getOptimalGridCols } from "./grid-utils";
import type { DragDropItem } from "./index";
import StatementCard from "./statement-card";

interface StatementCardsContainerProps {
  statements: DragDropItem[];
  categories: GameSectionCategory[];
  onCardSelect?: (id: string) => void;
  selectedStatementId?: string | null;
  activeStatement?: DragDropItem | null;
  section: Section;
}

const StatementCardsContainer = memo(function StatementCardsContainer({
  statements,
  onCardSelect,
  selectedStatementId,
  section,
}: StatementCardsContainerProps) {
  const { setNodeRef } = useDroppable({
    id: "statements",
  });

  const handleCardSelect = useCallback(
    (id: string) => {
      onCardSelect?.(id);
      const element = document.getElementById(
        `dropzone-container-${section?.id}`,
      );
      if (element) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "start",
          inline: "nearest",
        });
      }
    },
    [onCardSelect, section?.id],
  );

  if (!statements.length) {
    return null;
  }

  return (
    <div
      ref={setNodeRef}
      className="bg-gradient-to-br from-gray-50 to-gray-100 p-4 sm:mx-0 sm:p-6"
    >
      <div className="mb-4 text-center sm:mb-6">
        <h3 className="mb-2 text-gray-800 text-lg sm:text-xl">
          Statement Cards
        </h3>
      </div>

      <div
        className={cn(
          "grid gap-2 sm:gap-y-4 md:gap-x-3 xl:gap-x-5",
          getOptimalGridCols(statements.length),
        )}
      >
        {statements.map((statement) => (
          <StatementCard
            key={statement.id}
            statement={statement}
            isDragging={false}
            onSelect={handleCardSelect}
            isSelected={selectedStatementId === statement.id}
          />
        ))}
      </div>
    </div>
  );
});

export default StatementCardsContainer;
