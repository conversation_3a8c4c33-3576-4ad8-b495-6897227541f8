import { LoaderCircleIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import inviteFriendsImg from "@/assets/images/invite-friends.png";
import keyAvailableImg from "@/assets/images/key-available.png";
import keyLockImg from "@/assets/images/key-lock.png";
import { ArrowLineRightIcon } from "@/components/icons";
import { ShadowButton } from "@/components/shadow-button";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { GradientText } from "@/components/ui/gradient-text";
import * as m from "@/paraglide/messages.js";
import { User } from "@/types/auth";
import { Course } from "@/types/courses";
import { Lesson } from "@/types/lessons";

type LessonUnlockDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  lesson: Lesson;
  course?: Course;
  onUnlock: () => Promise<void>;
  user: User;
};

export const KeyUnlockDialog = ({
  open,
  onOpenChange,
  lesson,
  onUnlock,
  user,
}: LessonUnlockDialogProps) => {
  const availableKeys = user?.keys || 0;
  const requiredKeys = lesson.requirements?.keys || 0;
  const [unlocking, setUnlocking] = useState(false);

  const handleUnlock = async () => {
    setUnlocking(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      await onUnlock();
      toast.success(m["courses.lesson_unlock.unlock_success"]());
    } catch (e) {
      toast.error(m["courses.lesson_unlock.unlock_failed"]());
    } finally {
      onOpenChange(false);
      setUnlocking(false);
    }
  };

  if (!requiredKeys) return null;
  return (
    <div>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent
          aria-describedby={undefined}
          className="gap-5 bg-(--card) p-4 sm:max-w-xl sm:p-8"
        >
          <DialogTitle className="sr-only">
            {m["courses.lesson_unlock.title"]()}
          </DialogTitle>
          <img
            src={inviteFriendsImg}
            alt="invite-friends"
            className="mb-1 w-32"
          />
          <div className="flex flex-col gap-1">
            <GradientText className="mb-3 font-light text-2xl sm:text-4xl">
              {m["courses.lesson_unlock.unlock_question"]()}
            </GradientText>
            <div>
              <h2 className="sm:text-xl">
                {m["courses.lesson_unlock.unlock_description"]()}
              </h2>
            </div>
          </div>
          <div className="flex max-w-sm gap-5">
            <div className="flex-1 rounded-(--size-2xs) bg-[#FFF2C2] p-5">
              <div className="flex items-center gap-2">
                <img
                  src={keyLockImg}
                  alt="key-lock"
                  className="h-auto max-w-[66px] object-cover"
                />
                <p className="text-3xl sm:text-[42px]">0{requiredKeys}</p>
              </div>
              <p className="mt-2 rounded-lg bg-[#FFBF00] px-4 py-1 text-center">
                {m["courses.lesson_unlock.required_key"]()}
              </p>
            </div>
            <div className="flex-1 rounded-(--size-2xs) bg-[#ECF5FF] p-5">
              <div className="flex items-center gap-2">
                <img
                  src={keyAvailableImg}
                  alt="key-lock"
                  className="h-auto max-w-[66px] object-cover"
                />
                <p className="text-3xl sm:text-[42px]">{availableKeys}</p>
              </div>
              <p className="mt-2 rounded-lg bg-[#B7D9FF] px-4 py-1 text-center">
                {m["courses.lesson_unlock.your_keys"]()}
              </p>
            </div>
          </div>
          <div className="mt-1 flex items-center gap-6">
            <ShadowButton
              size="lg"
              className="sm:!px-10 rounded-full font-normal"
              onClick={handleUnlock}
              disabled={unlocking}
            >
              {m["courses.lesson_unlock.yes_unlock"]()}
              {unlocking ? (
                <LoaderCircleIcon className="size-4 animate-spin" />
              ) : (
                <ArrowLineRightIcon />
              )}
            </ShadowButton>
            <p
              className="cursor-pointer underline underline-offset-2"
              onClick={() => onOpenChange(false)}
            >
              {m["courses.lesson_unlock.cancel"]()}
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
