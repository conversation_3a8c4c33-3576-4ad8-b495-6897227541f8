# EditorConfig helps maintain consistent coding styles for multiple developers
# See https://editorconfig.org

# top-most EditorConfig file
root = true

# Default settings for all files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2
max_line_length = 100

# JS, JSX, TS and TSX files
[*.{js,jsx,ts,tsx}]
indent_size = 2
quote_type = single

# CSS, SCSS files
[*.{css,scss,sass}]
indent_size = 2

# JSON files
[*.json]
indent_size = 2

# HTML files
[*.html]
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# Package.json
[package.json]
indent_size = 2