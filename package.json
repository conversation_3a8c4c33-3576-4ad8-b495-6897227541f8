{"name": "tanstack-start-example-basic-react-query", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev --port 3000", "build": "node build-message-watcher.js", "start": "node .output/server/index.mjs", "lint": "pnpm biome check src", "lint:fix": "pnpm biome check --write src", "machine-translate": "inlang machine translate --project project.inlang", "test": "NODE_OPTIONS=--experimental-vm-modules jest", "test:watch": "NODE_OPTIONS=--experimental-vm-modules jest --watch", "test:coverage": "NODE_OPTIONS=--experimental-vm-modules jest --coverage", "test:types": "tsc --noEmit", "prepare": "husky", "lint-staged": "lint-staged"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@floating-ui/react-dom": "^2.1.3", "@inlang/cli": "^3.0.12", "@inlang/paraglide-js": "^2.1.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-form": "^1.12.2", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.80.6", "@tanstack/react-router": "^1.129.8", "@tanstack/react-router-devtools": "^1.129.8", "@tanstack/react-router-with-query": "^1.129.8", "@tanstack/react-start": "^1.121.0", "@yornaath/batshit": "^0.10.1", "arktype": "^2.1.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookie-es": "^2.0.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.16.0", "grapheme-splitter": "^1.0.4", "input-otp": "^1.4.2", "jest-config": "^30.0.0", "jotai": "^2.12.5", "lucide-react": "^0.514.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "vaul": "^1.1.2", "vite": "^6.3.5"}, "devDependencies": {"@biomejs/biome": "2.0.0-beta.1", "@tailwindcss/vite": "^4.1.8", "@tanstack/router-core": "^1.129.8", "@tanstack/start-client-core": "^1.129.8", "@types/google.accounts": "^0.0.16", "@types/jest": "^29.5.14", "@types/matter-js": "^0.19.8", "@types/node": "^24.0.0", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "husky": "^9.1.7", "jest": "^30.0.0", "lint-staged": "^16.1.2", "tailwindcss": "^4.1.8", "ts-jest": "^29.3.4", "type-fest": "^4.41.0", "typescript": "^5.8.3", "unrs-resolver": "1.7.13", "vite-tsconfig-paths": "^5.1.4"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["pnpm biome check src"]}}