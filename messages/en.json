{"$schema": "https://inlang.com/schema/inlang-message-format", "common": {"actions": {"login": "<PERSON><PERSON>", "logout": "Logout", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "back": "Back", "next": "Next", "submit": "Submit", "search": "Search", "filter": "Filter", "sort": "Sort", "continue": "Continue", "check": "Check", "reset": "Reset", "try_again": "Try again", "skip": "Skip this", "finish": "Finish", "view_account": "View Account", "show_all": "Show all", "show_less": "Show less", "all": "All", "toggle_sidebar": "Toggle Sidebar"}, "navigation": {"dashboard": "Dashboard", "courses": "Courses", "learning": "My Learning", "profile": "Profile", "help_center": "Help Center", "settings": "Settings", "sidebar": "Sidebar", "sidebar_description": "Displays the mobile sidebar."}, "status": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information"}, "time": {"minutes": "minutes", "hours": "hours", "days": "days", "weeks": "weeks", "months": "months", "years": "years"}}, "auth": {"title": "Sign in to start learning 📚", "login_with_google": "Login with Google", "login_with_email": "Login with <PERSON><PERSON>", "email_placeholder": "Enter your email", "password_placeholder": "Enter your password", "forgot_password": "Forgot password?", "no_account": "Don't have an account?", "create_account": "Create account", "or": "OR", "close": "Close"}, "dashboard": {"welcome": "Welcome back", "continue_learning": "Continue Learning", "your_progress": "Your Progress", "recent_courses": "Recent Courses", "achievements": "Achievements", "streak": "Learning Streak", "total_xp": "Total Experience Points", "cards": {"ai_mentor": {"dive_in_full": "Dive in full", "access_mode": "access mode", "alphas_free": "Alpha's Free"}, "daily_attendance": {"free_xp_daily": "Free {xp} XP Daily", "double_up_weekly": "Double up weekly!", "claim_xp_today": "Claim your {xp} XP today!", "come_back_tomorrow": "Come back tomorrow for {xp} XP!"}, "invite_friend": {"title": "In<PERSON><PERSON>", "description": "to unlock more lessons"}, "streaks": {"title": "{count} Streaks", "description": "Get 1 Streak by completing 1 lesson", "day": "Day {number}"}, "learning_progress": {"completed_percent": "{progress}% completed", "completed_date": "Completed Date:"}, "share_learn": {"title": "Share & Learn", "description": "with your friends"}, "leaderboard": {"title": "Hall of Fame", "description": "Where top minds rise with every XP earned", "no_players_title": "No players in this league", "no_players_description": "Be the first to join this league and start competing!"}}}, "course_rating": {"frustrating": "Frustrating", "could_be_better": "Could be better", "okay": "Okay", "pretty_good": "Pretty Good", "excellent": "Excellent", "overall_rating": "Overall Rating", "share_thoughts": "Share your thoughts", "feedback_placeholder": "How was your journey with this course? Your feedback means a lot and helps us improve your learning experience.", "minimum_characters": "Don't hold back! We're looking for {count} characters minimum.", "skip_this": "Skip this", "submit_ratings": "Submit Ratings", "course_quality": "Course Quality", "learning_engagement": "Learning Engagement", "ai_mentor_support": "AI Mentor Support", "thanks_feedback": "Thanks for Your Feedback!", "review_helps_improve": "Your review helps improve our course experience for everyone.", "back_to_courses": "Back to Courses", "share_course_friends": "Share this Course with Friends", "well_done": "Well done, {name}!", "total_xp_earned": "Total XP earned from the course", "rate_course_enjoy": "Rate this course now and enjoy", "rating_submitted_success": "Rating submitted successfully", "rating_submission_failed": "Rating submission failed", "link_copied": "Link copied to clipboard", "student": "Student", "quality": {"needs_major_improvements": "Needs major improvements", "several_areas_better": "Several areas could be better", "good_content_general": "Good content general", "solid_well_structured": "Solid and well-structured", "clear_useful_high_quality": "Clear, useful, and high-quality"}, "engagement": {"not_engaging": "Not engaging at all", "somewhat_monotonous": "Somewhat monotonous", "reasonably_engaging": "Reasonably engaging", "fun_interactive": "Fun and interactive", "super_engaging": "Super engaging and enjoyable"}, "ai_mentor": {"not_helpful": "Not helpful", "occasionally_helpful": "Occasionally helpful", "mostly_reliable": "Mostly reliable", "mostly_helpful": "Mostly helpful and on-topic", "super_helpful": "Super helpful and made learning easier"}}, "course_info": {"modules": "modules", "lessons": "Lessons", "games": "games", "my_bookmark": "My Bookmark"}, "exam_tool": {"ask_something_else": "Ask something else", "translate_to_vietnamese": "Translate this to Vietnamese", "translate_to_english": "Translate this to English", "explain_this": "Explain this", "give_examples": "Give me some examples", "summarize_this": "Summarize this"}, "courses": {"title": "Courses", "all_courses": "All Courses", "featured": "Featured", "categories": "Categories", "difficulty": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced"}, "course_info": "Course Information", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "duration": "Duration", "lessons": "Lessons", "students": "Students", "rating": "Rating", "enroll": "Enroll Now", "start_course": "Start Course", "sort_by": "Sort by", "sort_newest": "Newest", "sort_top_rated": "Top rated", "sort_top_pick": "Top pick", "select": "Select...", "curriculum": "Curriculum", "share_and_learn": "Share & Learn", "with_friends": "with your friends", "course_not_found": "Course not found.", "unexpected_error": "Well, this is unexpected...", "courses_breadcrumb": "Courses", "unknown_category": "Unknown", "what_you_will_learn": "What you will learn:", "revise": "Revise", "unlock_to_continue": "Unlock to Continue", "start_learning": "Start Learning", "continue_learning": "Continue Learning", "share_message": "Join me at aicademy – where learning gets easier with an AI Mentor by your side! Pick a course and let's explore together!", "total_learners": "Total Learners", "total_courses": "Total Courses", "about_instructor": "About", "lesson_unlock": {"title": "Unlock next lesson", "unlock_question": "Unlock this lesson?", "unlock_description": "Use your Key to unlock this lesson and continue your learning journey!", "required_key": "Required key", "your_keys": "Your keys", "yes_unlock": "Yes, Unlock lesson", "cancel": "Cancel", "unlock_success": "Lesson unlocked successfully", "unlock_failed": "Failed to unlock lesson", "invite_friends_title": "Invite friends to unlock lesson", "no_keys_message": "You have no key to unlock this lesson", "key_reward_message": "You will get 1 key when your invited friend completes 1 lesson.", "share_message": "Check out this lesson on <PERSON><PERSON> with me!", "or_share": "Or share"}, "lesson_modules": {"start": "Start", "end": "End", "lesson_start_alt": "lesson start"}, "lesson_item": {"revise": "Revise", "start": "Start", "unlock": "Unlock", "continue": "Continue", "mins": "mins", "games": "games"}, "lesson_completion": {"game_completed": "Game completed!", "challenge_completed": "Challenge completed!", "lesson_completed": "Lesson completed!", "lesson_complete_title": "Lesson Complete!", "lesson_completed_alt": "lesson completed", "great_job_message": "Great job! You've mastered this lesson and earned", "xp_suffix": "XP", "lessons_in_course": "lessons in this course", "next_lesson": "Next lesson", "unlock_next_lesson": "Unlock Next Lesson", "back_to_course": "Back to Course"}}, "learning": {"my_learning": "My Learning", "in_progress": "In Progress", "completed": "Completed", "bookmarked": "Bookmarked", "continue_where_left": "Continue where you left off", "lesson_completed": "Lesson Completed", "course_completed": "Course Completed", "progress": "Progress", "next_lesson": "Next Lesson", "previous_lesson": "Previous Lesson", "exam_slide": {"lets_play": "Let's Play!"}, "empty_states": {"in_progress": "You will find your in-progress courses here.", "completed": "You will find your finished courses here."}}, "profile": {"title": "Profile", "edit_profile": "Edit Profile", "personal_info": "Personal Information", "name": "Name", "email": "Email", "bio": "Bio", "avatar": "Avatar", "statistics": {"title": "Statistics", "total_xp": "Total XP", "completed_courses": "Completed Courses", "friends_invited": "Friends Invited", "badges": "Badges"}, "courses_completed": "Courses Completed", "hours_learned": "Hours Learned", "certificates": "Certificates", "achievements": "Achievements", "profile_settings": "Profile settings", "display_name": "Display Name", "display_name_placeholder": "Enter your display name", "username_placeholder": "Enter your username", "biography": "Biography", "bio_placeholder": "ex: Educator with 5+ years of experience in tech and online learning.", "achievement_details": {"personal_records": "Personal Records", "awards": "Awards", "no_records_yet": "No records earned yet – start your journey today!", "no_badges_yet": "No badges earned yet – start your journey today!", "badges": {"alpha_crew": {"congratulation": "You've Earned the Alpha Crew Badge!", "title": "Thank you for being among the first to explore aicademy!", "description": "As a token of appreciation, we've awarded you the Alpha Crew badge — given exclusively to early users who joined us during the Alpha phase."}, "first_milestone": {"congratulation": "You've Reached Your First Milestone!", "title": "You just completed your very first lesson — and that's a big deal. As a reward, we've given you the First Milestone badge!", "description": "Every journey starts with a single step. Let's keep going!"}, "the_inviter": {"congratulation": "You've Earned the \"The Inviter\" Badge!", "title": "You invited 5+ friends to join aicademy — amazing!", "description": "Thanks for helping grow our learning community."}}, "popup": {"congratulations": "Congratulations!", "keep_learning": "Keep Learning", "view_my_badges": "View My Badges"}}, "friends": {"invite_title": "Invite friends.", "unlock_lessons": "Unlock more lessons.", "share_to": "Share to", "bring_friend_description": "Bring a friend on board — when they complete one lesson, you'll get", "key_to_unlock": "a key to unlock a new lesson.", "keys_available": "Keys Available", "invite_list": "Invite list", "top_30_shown": "Top 30 invitees shown", "friends_email": "Friend's Email", "signup_status": "Sign-up Status", "lesson_completed": "Lesson Completed", "key_earned_date": "Key Earned Date", "no_friends_yet": "No friends yet? Invite your first one now!", "invite_friends": "Invite Friends", "share_unique_link": "Share your unique link with friends", "earn_keys": "<PERSON><PERSON><PERSON>", "get_key_when_friend_completes": "Get 1 key when each friend completes a lesson", "unlock_lesson": "<PERSON><PERSON>on", "use_keys_access": "Use keys to access lessons and get more XP"}}, "help": {"title": "Help Center", "search_help": "Search Help", "faq": "Frequently Asked Questions", "contact_support": "Contact Support", "user_guide": "User Guide", "troubleshooting": "Troubleshooting", "learning_experiences": {"title": "Learning Experiences", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod.", "questions": {"q1": "What kind of games or activities are included?", "a1": "aicademy makes learning feel like a game with interactive lessons, XP points, leaderboards, and <PERSON><PERSON>, your AI Mentor, guiding you every step of the way.", "q2": "Is Aicademy free to use?", "a2": "That's <PERSON><PERSON>! 🦉 <PERSON><PERSON> is your built-in AI Mentor who's always there to explain tough concepts, give examples, and help you out whenever you're stuck - whenever you need support.", "q3": "Do I need to know anything about <PERSON> before I start?", "a3": "Yes! You can learn and explore courses for free. In addition, learners can use in-game items to unlock even more exciting features and experiences.", "q4": "Can I use Aicademy on mobile?", "a4": "Currently, courses are available in Vietnamese, with English support coming soon.", "q5": "Is my data safe on Aicademy?", "a5": "aicademy works on all modern devices — laptop, tablet, or smartphone. Just open it in your web browser!"}}}, "games": {"checking": "Checking...", "incorrect": "That's incorrect!", "correct_answer": "Correct Answer!", "quiz_completed": "Quiz completed", "quizzes_completed": "Quizzes completed", "game_completed": "Game completed", "games_completed": "Games completed", "explanation": "Explanation", "quiz": {"single": "Quiz", "question": "Question", "of": "of", "select_all": "Select ALL that apply", "pick_correct": "Pick a correct answer"}}, "chatbot": {"input": {"placeholder_mobile": "Message aicademy...", "placeholder_desktop": "Ask me or simply highlight any part of the text in this lesson"}, "intro": {"greeting": "Hi! I'm <PERSON><PERSON>, an AI Mentor. Ask me anything!", "name": "Ailo - AI Mentor"}, "loading": {"cooking": "Hang tight, we are cooking!", "loading": "Loading..."}, "shortcuts": {"shift_enter": "to add a new line", "enhanced_by_ai": "Enhanced by AI. Double-check key info."}, "settings": {"language": "Language", "auto": "Auto"}, "feedback": {"title": "Give feedback", "description": "Provide additional context to help us improve.", "options": {"incorrect_information": "Incorrect information", "instruction_ignored": "Instruction ignored", "being_lazy": "Being lazy", "dont_like_style": "Don't like style", "bad_recommendation": "Bad recommendation", "other": "Other"}, "how_to_improve": "How can we improve?", "optional": "(optional)", "placeholder": "What was unsatisfying about this response?", "submitting": "Submitting...", "submit_feedback": "Submit <PERSON>"}}, "ui": {"not_available": "Oops, not available at the moment.", "copy": "Copy", "copied": "Copied!", "url_copied": "URL copied!", "feedback_submitted": "<PERSON><PERSON><PERSON> submitted successfully", "already_claimed": "Already claimed today or not available yet", "empty_data": {"no_data_found": "No data found", "no_data_description": "There's no data to display at the moment."}, "invite": {"title": "You got an invitation from", "description": "Your friend has invited you to join aicademy – a place to learn together with an AI Mentor, at your own pace.", "start_exploring": "Start exploring interactive lessons and unlock new content!", "agree_join": "<PERSON><PERSON><PERSON> and Join now"}, "text": {"read_more": "Read more", "collapse": "Collapse"}}, "course": {"by": "by", "oops": "Oops!", "course_error_alt": "Course Error", "go_back_home": "Go back to home page", "rating": {"not_yet": "Not yet", "learner": "learner", "learners": "learners", "review": "review", "reviews": "reviews"}, "reviews": {"title": "Learn reviews", "course_rating": "course rating", "course_quality": "Course Quality", "ai_mentor": "AI Mentor", "learning_engagement": "Learning Engagement", "reviews_count": "Reviews", "show_less_reviews": "Show less reviews", "show_all_reviews": "Show all reviews"}}, "errors": {"general": "An error occurred", "network": "Network connection error", "not_found": "Not found", "unauthorized": "Unauthorized access", "validation": "Invalid data", "try_again": "Please try again", "page_not_found": "The page you are looking for does not exist.", "go_back": "Go back", "start_over": "Start Over", "home": "Home"}, "landing": {"navbar": {"start_learning": "Start Learning 🎓", "login": "<PERSON><PERSON>"}, "starter_screen": {"main_heading": "Enhanced Learning", "subheading": "powered by AI", "description": "Enhanced courses that make learning clear and practical, with an AI mentor guiding real-world use.", "cta_button": "Explore Now", "section_title": "What is aicademy?", "section_description": "aicademy delivers enhanced learning through AI-supported content and interactive lessons — focused on building strong knowledge through practice and feedback."}, "discover_courses": {"heading": "Discover aicademy courses", "description": "Learning by doing. Pick a course and level up with AI Mentor."}, "why_choose": {"heading": "Why choose aicademy?", "description": "Learning by doing. Pick a course and level up with AI Mentor.", "reason1": {"title": "24/7 AI Mentorship", "description": "Constant AI companion throughout your learning journey, ready to provide personalized guidance."}, "reason2": {"title": "Bite-sized Lessons", "description": "Learn more in less time. Our lessons are short, focused, and fit into your busy day."}, "reason3": {"title": "Interactive Challenges", "description": "Simulations, scenario-based decisions, sorting and matching activities"}}, "faq": {"heading": "Frequently Asked Questions", "questions": {"q1": "How is aicademy different from other online learning platforms?", "a1": "aicademy provides personalized learning experiences with AI Mentor, focusing on hands-on learning and real-world applications.", "q2": "What is the AI Mentor?", "a2": "AI Mentor is an intelligent learning assistant that helps you understand content deeply, provides instant feedback, and offers personalized guidance.", "q3": "Is aicademy free to use?", "a3": "Yes! You can even experience the unique features on aicademy without any cost.", "q4": "What language do the courses support?", "a4": "Currently we support Vietnamese and English, with plans to expand to more languages in the future.", "q5": "Which devices can I use to access aicademy?", "a5": "aicademy works on any device with internet connection - computers, tablets, and smartphones."}, "see_more": "See more"}, "discover_way_learn": {"heading": "Discover Smarter Ways to Learn", "start_learning": "Start Learning", "footer": {"courses": "Courses", "privacy": "Privacy Policy", "faq": "FAQ", "copyright": "© 2025 aicademy Inc. All rights reserved"}}}, "help_center": {"title": "Help Center", "greeting": "Hi, how can we help you?", "search_placeholder": "Search for anything...", "section_not_found": "Section not found", "section_not_found_desc": "The help center section you're looking for doesn't exist.", "still_have_questions": "Still have questions?", "email_us": "Email us:", "chat_with_ailo": "Chat with <PERSON><PERSON>:", "get_instant_answers": "Get instant answers 24/7"}}