{"files.watcherExclude": {"**/routeTree.gen.ts": true}, "search.exclude": {"**/routeTree.gen.ts": true}, "files.readonlyInclude": {"**/routeTree.gen.ts": true}, "editor.quickSuggestions": {"strings": "on"}, "typescript.preferences.autoImportSpecifierExcludeRegexes": ["^(node:)?os$"], "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}}