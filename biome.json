{"$schema": "https://biomejs.dev/schemas/2.0.0-beta.1/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**/*", "!**/src/routeTree.gen.ts", "!**/src/components/ui/*", "!**/src/__test__/*", "!**/src/paraglide/*", "!**/build-message-watcher.js"]}, "formatter": {"enabled": true, "indentStyle": "space"}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"a11y": {"useFocusableInteractive": "off", "noSvgWithoutTitle": "off", "useKeyWithClickEvents": "off"}, "correctness": {"noChildrenProp": "off"}, "nursery": {"useSortedClasses": {"level": "error", "fix": "safe", "options": {"attributes": ["classList"], "functions": ["clsx", "cva", "tw"]}}}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}