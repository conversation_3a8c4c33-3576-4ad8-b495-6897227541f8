import { paraglideVitePlugin as paraglide } from "@inlang/paraglide-js";
import tailwindcss from "@tailwindcss/vite";
import { tanstackStart } from "@tanstack/react-start/plugin/vite";
import { defineConfig } from "vite";
import tsConfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  plugins: [
    tsConfigPaths({
      projects: ["./tsconfig.json"],
    }),
    tailwindcss(),
    paraglide({
      project: "./project.inlang",
      outdir: "./src/paraglide",
      outputStructure: "message-modules",
      cookieName: "PARAGLIDE_LOCALE",
      strategy: ["cookie", "url", "preferredLanguage", "baseLocale"],
    }),
    tanstackStart({
      target: "cloudflare-pages",
      pages: [
        { path: "/", prerender: { enabled: true, crawlLinks: false } },
        { path: "/privacy", prerender: { enabled: true, crawlLinks: false } },
        {
          path: "/terms-of-service",
          prerender: { enabled: true, crawlLinks: false },
        },
        { path: "/courses", prerender: { enabled: true, crawlLinks: false } },
        {
          path: "/help-center",
          prerender: { enabled: true, crawlLinks: false },
        },
      ],
      sitemap: {
        host: "https://aicademy.org",
      },
    }),
  ],
  define: {
    global: "globalThis",
  },
  optimizeDeps: {
    exclude: ["@tanstack/start-client-core"],
  },
  build: {
    rollupOptions: {
      onwarn(warning, warn) {
        if (warning.code === "MODULE_LEVEL_DIRECTIVE") {
          return;
        }

        warn(warning);
      },
      onLog(level, log, handler) {
        if (log.code === "MODULE_LEVEL_DIRECTIVE") {
          return;
        }

        handler(level, log);
      },
    },
  },
});
